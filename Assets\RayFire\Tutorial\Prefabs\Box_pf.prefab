%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 1265905815854442}
  m_IsPrefabParent: 1
--- !u!1 &1265905815854442
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 4187334637074886}
  - component: {fileID: 33279081108705132}
  - component: {fileID: 23093967442453408}
  - component: {fileID: 114211121995268436}
  m_Layer: 0
  m_Name: Box_pf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4187334637074886
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1265905815854442}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -23.52, y: 22.67, z: -40.446712}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &23093967442453408
MeshRenderer:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1265905815854442}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &33279081108705132
MeshFilter:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1265905815854442}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &114211121995268436
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 1265905815854442}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.1
    depth: 1
    time: 0.3
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 1
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
