{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\ul\b\f0\fs52\lang9 How to slice Mesh object in runtime\ulnone\b0\fs22\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cylinder\b0 , this will be the cylinder which will slice. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Pillar\i0 ", set its \b position \b0 to [0,5.5,0] and \b scale \b0 to [1,5,1]\line\par
{\pntext\f0 3.\tab}\b Destroy \b0 it\rquote s default Capsule collider.\line\par
{\pntext\f0 4.\tab}Create \b Quad\b0 , this will be our slicing object.\line\par
{\pntext\f0 5.\tab}Set its \b name \b0 to "\i Blade\i0 ", \b position \b0 to [2,8,0], \b rotation \b0 to [50,90,90] and \b scale \b0 to [1,6,1]\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 it\rquote s default Mesh collider.\line\par
{\pntext\f0 7.\tab}Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 8.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 9.\tab}Add \b RayFire Rigid \b0 component to Pillar. Only objects with Rigid component can be sliced.\line\par
{\pntext\f0 10.\tab}Set \b Initialization to At Start\b0 .\line\par
{\pntext\f0 11.\tab}Set \b Demolition Type \b0 to \b Runtime\b0 .\line\par
{\pntext\f0 12.\tab}In \b Limitations \b0 properties set \b Solidity \b0 to 0.5 and enable \b Slice By Blade\b0 , otherwise it won\rquote t be possible to slice this object.\line\par
{\pntext\f0 13.\tab}Add \b RayFire Blade \b0 component to the Blade object.\line\par
{\pntext\f0 14.\tab}Set \b Action \b0 to \b Slice\b0 .\line\par
{\pntext\f0 15.\tab}Set \b On Trigger \b0 to \b Exit\b0 .\line\par
{\pntext\f0 16.\tab}Set \b Slice \lang1033 Plane\lang9  \b0 to XY.\line\par
{\pntext\f0 17.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 18.\tab}Enable \b Move Tool\b0 , \b select \b0 Blade object and set \b Tool Handle \b0 in \b Local mode\b0 .\line\par
{\pntext\f0 19.\tab}\b Move \b0 the Blade object by it\rquote s \b Red axis \b0 toward Pillar and pass it through.\line\line Pillar will be sliced. Top part will fall down, but if you try to manually slice the bottom part in the same way it won\rquote t be sliced. This is because your Depth property in Limitation properties set to 1. It means that objects can be demolished or sliced just once.\line\par
{\pntext\f0 20.\tab}\b Turn Off \b0 Play Mode, select Pillar and in Limitations properties set \b Depth \b0 to \b 2\b0 . \line\line Now every fragment which you will get after a slice will be possible to slice again. If you want to make slicing infinite just set Depth to 0.\line\par
{\pntext\f0 21.\tab}\b Start \b0 Play Mode, and \b move \b0 Blade object through Pillar again. \line\line\tab Pillar will be sliced and the top piece will start to fall down. After collision with ground it may be demolished in Runtime because it\rquote s Depth allows it to be demolished or sliced again. To prevent collision demolition increase Solidity property for Pillar. \line\tab If the top piece will touch Blade again it could be sliced again. To prevent such quick secondary slices or demolition you can increase \b Time \b0 property in Limitations. This property defines \ldblquote safe\rdblquote  time for all new fragments after their \ldblquote birth\rdblquote . Value 2 means that during two seconds after it\rquote s birth it won\rquote t be possible to Slice or Demolish it.\line\line\tab Let's say you want to slice an object without passing it through.\line\par
{\pntext\f0 22.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 23.\tab}\b Select \b0 Blade object.\line\par
{\pntext\f0 24.\tab}In Targets properties click on Plus button to create gameobject \b Target field\b0 .\line\par
{\pntext\f0 25.\tab}\b Drag and drop \b0 Pillar object from \b Hierarchy \b0 window to \b Target field \b0 in Blade component.\line\par
{\pntext\f0 26.\tab}\b Start \b0 Play Mode and click on \b Slice \b0 button on top of Blade component or use public \b SliceTarget() \b0 method\par

\pard\nowidctlpar\sl276\slmult1\par
\tab\tab Pillar will be sliced over Blade's XY plane.\par
\par
}
 