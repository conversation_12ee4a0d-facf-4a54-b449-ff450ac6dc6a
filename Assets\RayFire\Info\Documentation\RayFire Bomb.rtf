{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fnil\fcharset2 Symbol;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 RayFire Bomb\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22\lang9 RayFire Bomb\b0  allows to create physical explosion and apply damage to objects with Rigid component to demolish them accordingly to final explosion force.\fs24\lang1033\par
\fs28\lang9\tab\fs24\lang1033\par
\b\fs48\lang9\tab Range\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Show\b0\lang1033 : Show Bomb's gizmo and range when object is not selected..\fs24\par
\b\fs22\lang9\par
Type\b0\lang1033 : Explosion direction.\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f1\'b7\tab\b\f0\fs22 Spherical\b0 : Objects will be exploded in all directions starting from position of object with Bomb component. \fs24\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22\lang9 Range\b0 : \lang1033 Defines Explosion range. Only objects in Range distance will be affected by explosion. \b\fs48\lang9\tab\par
\fs22\par
Deletion\b0 : \lang1033 Allows to destroy all exploded gameobjects close to Bomb. Measures in percentage relative to Range value.\b\lang9\par
\par
\fs48\tab Impulse\fs22\par
\par
Fade\b0\lang1033 : Explosion strength decay over distance.\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f1\'b7\tab\b\f0\fs22 Linear\b0 : Explosion strength will be the highest at Bomb position and then decrease linearly to 0 over \b Range \b0 distance.\fs24\par
\f1\'b7\tab\b\f0\fs22 Exponential\b0 : Explosion strength will be the highest at Bomb position and then decrease exponentially to 0 over \b Range \b0 distance.\fs24\par
\f1\'b7\tab\b\f0\fs22 None\b0 : Explosion strength will be the same for all objects in \b Range \b0 distance.\fs24\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22 Strength\b0 : Maximum explosion impulse which will be applied to objects.\fs24\par
\par
\b\fs22 Variation\b0 : Add random variation to final explosion strength for every object in percents relative to \b Strength \b0 value.\fs24\par
\par
\b\fs22 Chaos\b0 : Add random rotation velocity to exploded objects.\fs24\par
\par
\b\fs22 Force By Mass\b0 : Allows to add different final explosion impulse to objects with different mass. Two objects with different mass at the same range from explosion will get different explosion impulse, heavier object will be affected by explosion less than another.\fs24\par
\par
\b\fs48\lang9\tab Activate\b0\fs24\lang1033\par
\par
\b\fs22 Inactive\b0 : Allows to activate Inactive objects and explode them as well.\fs24\par
\par
\b\fs22 Kinematic\b0 : Allows to activate Kinematic objects and explode them as well.\fs24\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Detonation\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Height Offset\b0 : Allows to offset downward Explosion position over global Y axis to make it look less realistic but more interesting. \fs24\lang1033\par
\par
\b\fs22\lang9 Delay\b0 : \lang1033 Defines explosion delay in seconds.\par
\par
\b\lang9 At Start\b0 : \lang1033 Explodes Bomb at component's Start.\fs24\par
\par
\b\fs22\lang9 Destroy\b0 : \lang1033 Destroy object with Bomb component after it's detonation.\fs24\par
\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Damage\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Apply\b0 : Allows to apply damage to objects with Rigid component in case they have enabled Damage feature. In this way you can explode object severl time and every time it will collect some damage, it will be demolished when it will reach it's maximum damage value.\fs24\lang1033\par
\par
\b\fs22\lang9 Value\b0 : \lang1033 Damage value  which will take object at explosion. Final damage value will be calculated accordingly to final explosion strength.\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Audio\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Play\b0 : Allows to play audio clip at explosion.\fs24\lang1033\par
\par
\b\fs22\lang9 Volume\b0 : \lang1033 Volume value for explosion audio clip.\fs24\par
\par
\b\fs22\lang9 Clip\b0 : \lang1033 Audio Clip to play at explosion.\fs24\par
\par
\fs22\tab\b\fs48\lang9 Filters\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Tag\b0 : \lang1033 Bomb will explode only objects with picked Tag. You can pick only one Tag.\fs24\par
\par
\b\fs22\lang9 Layer\b0 : \lang1033 Bomb will explode only objects with defined layer. You can define several Layers.\fs24\par
\par
}
 