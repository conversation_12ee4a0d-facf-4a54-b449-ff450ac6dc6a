using UnityEngine;
using UnityEditor;

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Custom editor for VoronoiGlassBreaker component
    /// </summary>
    [CustomEditor(typeof(VoronoiGlassBreaker))]
    public class VoronoiGlassBreakerEditor : Editor
    {
        private VoronoiGlassBreaker glassBreaker;
        private bool showVoronoiSettings = true;
        private bool showBreakSettings = true;
        private bool showProgressiveSettings = true;
        private bool showPhysicsSettings = true;
        private bool showAudioSettings = true;
        private bool showEffectSettings = true;
        private bool showDebugInfo = false;
        
        private void OnEnable()
        {
            glassBreaker = (VoronoiGlassBreaker)target;
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Voronoi Glass Breaker", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // Status information
            if (Application.isPlaying)
            {
                DrawStatusInfo();
                EditorGUILayout.Space();
            }
            
            // Settings sections
            DrawVoronoiSettings();
            DrawBreakSettings();
            DrawProgressiveSettings();
            DrawPhysicsSettings();
            DrawAudioSettings();
            DrawEffectSettings();
            
            if (Application.isPlaying)
            {
                DrawDebugInfo();
                DrawTestButtons();
            }
            
            serializedObject.ApplyModifiedProperties();
        }
        
        private void DrawStatusInfo()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Status", EditorStyles.boldLabel);
            
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.Toggle("Initialized", glassBreaker.IsInitialized);
            EditorGUILayout.Toggle("Breaking", glassBreaker.IsBreaking);
            EditorGUILayout.Toggle("Fully Broken", glassBreaker.IsFullyBroken);
            EditorGUILayout.Slider("Breakage %", glassBreaker.GetBreakagePercentage() * 100f, 0f, 100f);
            EditorGUI.EndDisabledGroup();
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawVoronoiSettings()
        {
            showVoronoiSettings = EditorGUILayout.Foldout(showVoronoiSettings, "Voronoi Generation", true);
            if (showVoronoiSettings)
            {
                EditorGUILayout.BeginVertical("box");
                
                SerializedProperty settings = serializedObject.FindProperty("settings");
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("cellCount"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("relaxationIterations"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("minCellSize"));
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawBreakSettings()
        {
            showBreakSettings = EditorGUILayout.Foldout(showBreakSettings, "Break Detection", true);
            if (showBreakSettings)
            {
                EditorGUILayout.BeginVertical("box");
                
                SerializedProperty settings = serializedObject.FindProperty("settings");
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("breakForce"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("breakRadius"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("breakableLayers"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("breakTag"));
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawProgressiveSettings()
        {
            showProgressiveSettings = EditorGUILayout.Foldout(showProgressiveSettings, "Progressive Breaking", true);
            if (showProgressiveSettings)
            {
                EditorGUILayout.BeginVertical("box");
                
                SerializedProperty settings = serializedObject.FindProperty("settings");
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("enableProgressiveBreaking"));
                
                SerializedProperty enableProgressive = settings.FindPropertyRelative("enableProgressiveBreaking");
                if (enableProgressive.boolValue)
                {
                    EditorGUI.indentLevel++;
                    EditorGUILayout.PropertyField(settings.FindPropertyRelative("propagationSpeed"));
                    EditorGUILayout.PropertyField(settings.FindPropertyRelative("propagationRadius"));
                    EditorGUILayout.PropertyField(settings.FindPropertyRelative("propagationFalloff"));
                    EditorGUI.indentLevel--;
                }
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawPhysicsSettings()
        {
            showPhysicsSettings = EditorGUILayout.Foldout(showPhysicsSettings, "Fragment Physics", true);
            if (showPhysicsSettings)
            {
                EditorGUILayout.BeginVertical("box");
                
                SerializedProperty settings = serializedObject.FindProperty("settings");
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("fragmentLifetime"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("fragmentMass"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("impactForceMultiplier"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("fragmentPhysicsMaterial"));
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawAudioSettings()
        {
            showAudioSettings = EditorGUILayout.Foldout(showAudioSettings, "Audio", true);
            if (showAudioSettings)
            {
                EditorGUILayout.BeginVertical("box");
                
                SerializedProperty settings = serializedObject.FindProperty("settings");
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("breakSound"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("crackSound"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("audioVolume"));
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawEffectSettings()
        {
            showEffectSettings = EditorGUILayout.Foldout(showEffectSettings, "Effects", true);
            if (showEffectSettings)
            {
                EditorGUILayout.BeginVertical("box");
                
                SerializedProperty settings = serializedObject.FindProperty("settings");
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("breakEffectPrefab"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("crackEffectPrefab"));
                EditorGUILayout.PropertyField(settings.FindPropertyRelative("effectDuration"));
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawDebugInfo()
        {
            showDebugInfo = EditorGUILayout.Foldout(showDebugInfo, "Debug Info", true);
            if (showDebugInfo)
            {
                EditorGUILayout.BeginVertical("box");
                
                if (glassBreaker.IsInitialized)
                {
                    EditorGUI.BeginDisabledGroup(true);
                    EditorGUILayout.IntField("Voronoi Cells", glassBreaker.Settings.cellCount);
                    EditorGUILayout.FloatField("Breakage Percentage", glassBreaker.GetBreakagePercentage() * 100f);
                    EditorGUI.EndDisabledGroup();
                }
                else
                {
                    EditorGUILayout.LabelField("Not initialized yet");
                }
                
                EditorGUILayout.EndVertical();
            }
        }
        
        private void DrawTestButtons()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Testing", EditorStyles.boldLabel);
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Break at Center"))
            {
                Vector3 center = glassBreaker.transform.position;
                glassBreaker.ForceBreak(center);
            }
            
            if (GUILayout.Button("Break Random"))
            {
                Bounds bounds = glassBreaker.GetComponent<MeshRenderer>().bounds;
                Vector3 randomPoint = new Vector3(
                    Random.Range(bounds.min.x, bounds.max.x),
                    bounds.center.y,
                    Random.Range(bounds.min.z, bounds.max.z)
                );
                glassBreaker.ForceBreak(randomPoint);
            }
            
            EditorGUILayout.EndHorizontal();
            
            if (GUILayout.Button("Repair Glass"))
            {
                glassBreaker.RepairGlass();
            }
            
            EditorGUILayout.EndVertical();
        }
        
        private void OnSceneGUI()
        {
            if (!glassBreaker.IsInitialized) return;
            
            // Draw break radius
            Handles.color = Color.yellow;
            Handles.DrawWireDisc(glassBreaker.transform.position, glassBreaker.transform.up, glassBreaker.Settings.breakRadius);
            
            // Draw propagation radius if progressive breaking is enabled
            if (glassBreaker.Settings.enableProgressiveBreaking)
            {
                Handles.color = Color.red;
                Handles.DrawWireDisc(glassBreaker.transform.position, glassBreaker.transform.up, glassBreaker.Settings.propagationRadius);
            }
        }
    }
}
