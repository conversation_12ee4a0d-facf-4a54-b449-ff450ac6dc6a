{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18540, "tid": 1061, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18540, "tid": 1061, "ts": 1754646243665983, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18540, "tid": 1061, "ts": 1754646243666000, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18540, "tid": 1, "ts": 1754646242804277, "dur": 1030, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754646242805309, "dur": 14840, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754646242820151, "dur": 17199, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18540, "tid": 1061, "ts": 1754646243666005, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 18540, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242804250, "dur": 118219, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922470, "dur": 743044, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922485, "dur": 35, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922522, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922526, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922796, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922832, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242922839, "dur": 2369, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925217, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925219, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925308, "dur": 2, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925312, "dur": 67, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925381, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925383, "dur": 56, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925444, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925452, "dur": 63, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925519, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925521, "dur": 37, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925560, "dur": 46, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925609, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925610, "dur": 33, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925645, "dur": 32, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925681, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925715, "dur": 38, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925754, "dur": 33, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925790, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925791, "dur": 39, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925833, "dur": 32, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925866, "dur": 27, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925897, "dur": 28, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925928, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925929, "dur": 33, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925964, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242925989, "dur": 28, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926020, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926044, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926066, "dur": 19, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926086, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926110, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926132, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926154, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926177, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926199, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926218, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926240, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926263, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926285, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926310, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926334, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926355, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926378, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926400, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926423, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926446, "dur": 21, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926468, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926485, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926509, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926533, "dur": 24, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926558, "dur": 22, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926582, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926604, "dur": 22, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926628, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926651, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926674, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926696, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926720, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926742, "dur": 23, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926767, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926789, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926812, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926833, "dur": 23, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926857, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926879, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926898, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926921, "dur": 29, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926953, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242926954, "dur": 43, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927000, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927001, "dur": 39, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927042, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927061, "dur": 14, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927076, "dur": 32, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927112, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927151, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927153, "dur": 33, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927187, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927189, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927215, "dur": 26, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927243, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927282, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927284, "dur": 30, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927315, "dur": 28, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927347, "dur": 37, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927387, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927413, "dur": 28, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927443, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927468, "dur": 23, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927492, "dur": 30, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927524, "dur": 36, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927564, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927566, "dur": 34, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927602, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927637, "dur": 31, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927671, "dur": 23, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927695, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927719, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927743, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927765, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927788, "dur": 22, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927811, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927834, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927858, "dur": 15, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927875, "dur": 20, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927897, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927920, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927942, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927968, "dur": 24, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242927993, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928015, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928041, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928065, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928088, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928111, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928135, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928158, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928181, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928204, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928228, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928251, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928273, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928291, "dur": 57, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928350, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928378, "dur": 25, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928404, "dur": 30, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928435, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928458, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928480, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928505, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928529, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928552, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928576, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928598, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928599, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928620, "dur": 20, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928642, "dur": 28, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928672, "dur": 22, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928696, "dur": 22, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928720, "dur": 22, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928743, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928766, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928788, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928809, "dur": 20, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928831, "dur": 21, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928853, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928875, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928897, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928920, "dur": 27, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928950, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242928976, "dur": 23, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929001, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929023, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929044, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929070, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929095, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929117, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929139, "dur": 33, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929174, "dur": 27, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929204, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929205, "dur": 30, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929238, "dur": 28, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929269, "dur": 31, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929302, "dur": 21, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929324, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929347, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929370, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929400, "dur": 34, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929437, "dur": 24, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929463, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929486, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929508, "dur": 24, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929534, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929557, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929582, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929604, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929627, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929651, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929673, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929696, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929721, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929743, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929766, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929789, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929811, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929834, "dur": 29, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929864, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929886, "dur": 21, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929908, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929931, "dur": 24, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929956, "dur": 21, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242929978, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930003, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930024, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930046, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930069, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930092, "dur": 21, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930115, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930138, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930161, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930182, "dur": 22, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930205, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930228, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930251, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930275, "dur": 21, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930297, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930321, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930341, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930364, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930388, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930411, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930433, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930456, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930478, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930500, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930523, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930545, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930569, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930591, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930613, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930635, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930658, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930682, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930704, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930726, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930749, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930773, "dur": 32, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930806, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930849, "dur": 20, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930872, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930898, "dur": 22, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930921, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930943, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242930984, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931006, "dur": 22, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931030, "dur": 20, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931052, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931074, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931096, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931116, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931137, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931160, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931182, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931202, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931224, "dur": 22, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931247, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931270, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931293, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931314, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931334, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931356, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931377, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931400, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931424, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931446, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931469, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931491, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931514, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931535, "dur": 33, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931570, "dur": 134, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931708, "dur": 48, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931758, "dur": 38, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931799, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931801, "dur": 32, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931836, "dur": 26, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931865, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931891, "dur": 27, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931921, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931923, "dur": 43, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931969, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242931970, "dur": 47, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932020, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932022, "dur": 55, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932079, "dur": 23, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932104, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932126, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932150, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932167, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932192, "dur": 23, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932217, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932244, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932265, "dur": 22, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932288, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932311, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932334, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932359, "dur": 32, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932395, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932397, "dur": 37, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932436, "dur": 32, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932472, "dur": 41, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932516, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932517, "dur": 26, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932545, "dur": 31, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932580, "dur": 24, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932606, "dur": 21, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932628, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932650, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932669, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932691, "dur": 22, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932714, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932738, "dur": 26, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932765, "dur": 35, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932802, "dur": 28, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932831, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932854, "dur": 23, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932879, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932902, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242932924, "dur": 137, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933063, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933085, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933108, "dur": 29, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933141, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933142, "dur": 38, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933182, "dur": 22, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933205, "dur": 28, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933237, "dur": 29, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933268, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933299, "dur": 32, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933334, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933336, "dur": 31, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933369, "dur": 27, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933399, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933401, "dur": 26, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933428, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933456, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933479, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933481, "dur": 37, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933521, "dur": 33, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933555, "dur": 28, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933585, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933615, "dur": 27, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933644, "dur": 33, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933679, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933680, "dur": 42, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933725, "dur": 26, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933752, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933775, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933802, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933821, "dur": 22, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933846, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933870, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933893, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933915, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933936, "dur": 19, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933956, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242933983, "dur": 32, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934017, "dur": 25, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934043, "dur": 25, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934071, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934072, "dur": 28, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934103, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934125, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934146, "dur": 23, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934173, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934199, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934221, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934247, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934270, "dur": 23, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934294, "dur": 23, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934318, "dur": 59, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934379, "dur": 30, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934411, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934435, "dur": 150, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934587, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934609, "dur": 21, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934632, "dur": 20, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934654, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934677, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934699, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934721, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934743, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934765, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934767, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934789, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934810, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934833, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934855, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934877, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934901, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934925, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934947, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934969, "dur": 21, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242934992, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935015, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935038, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935060, "dur": 22, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935083, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935105, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935125, "dur": 20, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935146, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935169, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935192, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935214, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935236, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935258, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935281, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935302, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935324, "dur": 20, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935346, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935369, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935392, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935414, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935439, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935462, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935484, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935511, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935535, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935558, "dur": 22, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935582, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935604, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935627, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935648, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935672, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935694, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935696, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935720, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935742, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935766, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935792, "dur": 25, "ph": "X", "name": "ReadAsync 18", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935819, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935841, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935874, "dur": 49, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935925, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935947, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935968, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242935991, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936013, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936037, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936059, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936081, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936104, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936122, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936144, "dur": 22, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936167, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936189, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936214, "dur": 22, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936237, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936260, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936282, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936307, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936331, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936355, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936377, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936399, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936421, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936446, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936470, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936496, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936517, "dur": 21, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936539, "dur": 20, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936560, "dur": 22, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936583, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936613, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936636, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936659, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936683, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936704, "dur": 55, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936760, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936784, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936808, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936831, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936853, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936876, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936898, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936921, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936942, "dur": 21, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936964, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242936983, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937030, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937053, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937078, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937100, "dur": 36, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937138, "dur": 41, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937180, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937203, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937226, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937255, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937256, "dur": 28, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937286, "dur": 26, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937316, "dur": 40, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937358, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937383, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937406, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937425, "dur": 46, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937472, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937495, "dur": 24, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937522, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937546, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937568, "dur": 26, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937595, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937618, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937640, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937664, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937685, "dur": 43, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937730, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937753, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937777, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937801, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937822, "dur": 32, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937855, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937878, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937901, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937923, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937944, "dur": 33, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242937979, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938002, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938025, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938048, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938070, "dur": 32, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938104, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938121, "dur": 275, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938398, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938399, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938424, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938448, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938470, "dur": 46, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938517, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938539, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938561, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938591, "dur": 31, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938625, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938648, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938672, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938693, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938715, "dur": 26, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938743, "dur": 34, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938779, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938801, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938824, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938846, "dur": 23, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938870, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938903, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938925, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938948, "dur": 20, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938969, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242938988, "dur": 50, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939042, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939092, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939095, "dur": 37, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939135, "dur": 25, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939162, "dur": 30, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939194, "dur": 25, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939221, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939223, "dur": 31, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939256, "dur": 21, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939279, "dur": 21, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939301, "dur": 36, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939338, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939365, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939389, "dur": 19, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939410, "dur": 20, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939432, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939477, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939513, "dur": 25, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939540, "dur": 21, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939564, "dur": 49, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939614, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939736, "dur": 30, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939767, "dur": 22, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939791, "dur": 22, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939814, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939836, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939891, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939916, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939940, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939962, "dur": 22, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242939985, "dur": 51, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940037, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940062, "dur": 24, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940087, "dur": 20, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940109, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940130, "dur": 39, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940172, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940201, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940233, "dur": 26, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940261, "dur": 57, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940319, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940344, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940372, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940396, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940416, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940453, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940474, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940476, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940498, "dur": 23, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940523, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940546, "dur": 41, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940589, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940611, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940635, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940658, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940683, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940706, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940730, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940752, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940770, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940772, "dur": 21, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940794, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940836, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940871, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940872, "dur": 33, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940908, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940911, "dur": 26, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940939, "dur": 26, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940966, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242940992, "dur": 24, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941018, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941042, "dur": 28, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941072, "dur": 31, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941105, "dur": 44, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941150, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941180, "dur": 24, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941205, "dur": 23, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941229, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941251, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941273, "dur": 42, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941317, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941339, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941363, "dur": 22, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941387, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941410, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941433, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941456, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941478, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941500, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941523, "dur": 23, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941548, "dur": 26, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941576, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941598, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941620, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941642, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941664, "dur": 35, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941700, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941722, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941744, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941766, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941789, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941812, "dur": 26, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941840, "dur": 20, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941861, "dur": 17, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941880, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941900, "dur": 20, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941922, "dur": 34, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941958, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242941983, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942006, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942028, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942049, "dur": 32, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942083, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942105, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942127, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942150, "dur": 29, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942180, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942208, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942231, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942254, "dur": 21, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942276, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942297, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942335, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942359, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942382, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942405, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942427, "dur": 34, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942462, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942485, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942523, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942546, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942567, "dur": 29, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942598, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942620, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942642, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942664, "dur": 20, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942686, "dur": 37, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942725, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942746, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942768, "dur": 21, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942791, "dur": 20, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942812, "dur": 40, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942855, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942884, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942907, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942930, "dur": 42, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942973, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242942999, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943023, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943045, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943065, "dur": 32, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943098, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943121, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943143, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943165, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943185, "dur": 36, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943223, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943245, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943267, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943289, "dur": 24, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943315, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943317, "dur": 35, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943353, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943376, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943399, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943422, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943443, "dur": 33, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943478, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943500, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943522, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943545, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943566, "dur": 32, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943599, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943621, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943648, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943670, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943692, "dur": 30, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943723, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943745, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943767, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943790, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943812, "dur": 37, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943851, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943873, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943874, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943896, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943918, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943936, "dur": 35, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943973, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242943996, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944019, "dur": 23, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944044, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944067, "dur": 34, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944103, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944125, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944148, "dur": 32, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944183, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944216, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944264, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944334, "dur": 2, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944337, "dur": 52, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944393, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944426, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944450, "dur": 21, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944473, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944494, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944553, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944581, "dur": 39, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944623, "dur": 44, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944669, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944671, "dur": 31, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944703, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944726, "dur": 24, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944751, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944774, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944797, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944819, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944871, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944894, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944918, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944944, "dur": 22, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944968, "dur": 23, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242944993, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945016, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945038, "dur": 35, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945074, "dur": 31, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945108, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945109, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945158, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945196, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945197, "dur": 30, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945228, "dur": 41, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945273, "dur": 25, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945299, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945325, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945352, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945375, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945402, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945424, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945471, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945496, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945519, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945543, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945566, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945588, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945611, "dur": 19, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945632, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945660, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945685, "dur": 60, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945747, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945770, "dur": 106, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945879, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945910, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945933, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242945963, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946004, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946006, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946065, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946069, "dur": 75, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946150, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946155, "dur": 50, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946207, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946210, "dur": 39, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946251, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946276, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946310, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946334, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946336, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946366, "dur": 23, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946393, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946420, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946452, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946502, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946505, "dur": 32, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946538, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946541, "dur": 40, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946583, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946586, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946636, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946639, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946682, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946684, "dur": 34, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946720, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946722, "dur": 23, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946747, "dur": 29, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946777, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946779, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946817, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946819, "dur": 27, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946848, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946879, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946881, "dur": 37, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946920, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946942, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242946971, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947001, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947035, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947059, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947061, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947089, "dur": 21, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947112, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947141, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947170, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947171, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947197, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947198, "dur": 29, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947229, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947254, "dur": 23, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947280, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947305, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947334, "dur": 21, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947357, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947380, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947405, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947406, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947431, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947433, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947463, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947486, "dur": 115, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947605, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947644, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947646, "dur": 43, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947694, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242947728, "dur": 1616, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949349, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949381, "dur": 234, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949617, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949641, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949669, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949670, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949699, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949871, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949873, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242949894, "dur": 1662, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951562, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951601, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951647, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951674, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951822, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951862, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951863, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951930, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951963, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951964, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242951992, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952025, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952054, "dur": 169, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952228, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952257, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952323, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952344, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952368, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952400, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952422, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952449, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952473, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952495, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952519, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952521, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952544, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952568, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952590, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952611, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952633, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952656, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952678, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952699, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952773, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952801, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952822, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952892, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242952915, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953033, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953055, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953082, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953084, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953111, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953133, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953168, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953195, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953217, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953246, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953269, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953297, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953318, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953348, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953371, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953392, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953418, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953451, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953481, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953511, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953534, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953555, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953656, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953680, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953707, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953747, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953778, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953817, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953842, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953870, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953898, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953900, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953923, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953952, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242953975, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954002, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954026, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954090, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954119, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954143, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954172, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954196, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954218, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954221, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954246, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954269, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954338, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954371, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954373, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954411, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954438, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954467, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954488, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954700, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954722, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954781, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242954805, "dur": 197, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955004, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955033, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955070, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955093, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955135, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955160, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955198, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955220, "dur": 295, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955520, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955551, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955608, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955632, "dur": 273, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955907, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955939, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242955941, "dur": 298, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242956244, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242956283, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646242956285, "dur": 254858, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243211154, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243211161, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243211196, "dur": 26, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243211223, "dur": 2410, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243213643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243213645, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243213675, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243213677, "dur": 281, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243213963, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243213995, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214038, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214065, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214067, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214165, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214196, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214258, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214282, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214334, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214356, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214694, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214716, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214776, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214810, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214863, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243214904, "dur": 230, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215139, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215174, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215176, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215229, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215260, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215317, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215347, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215472, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215505, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215508, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215539, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215644, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215683, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215721, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215763, "dur": 171, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215936, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243215965, "dur": 253, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216223, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216259, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216379, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216404, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216490, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216518, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216520, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216550, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216582, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216609, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216732, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216755, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216785, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216820, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216851, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216880, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216881, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216912, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216935, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243216994, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217022, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217050, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217076, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217100, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217123, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217145, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217171, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217203, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217227, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217252, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217275, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217299, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217321, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217346, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217377, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217404, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217405, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217433, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217459, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217486, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217555, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217587, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217614, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217644, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217722, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217751, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217805, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217833, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217859, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217861, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217887, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217909, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243217939, "dur": 74608, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243292556, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243292559, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243292601, "dur": 43, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243292645, "dur": 7211, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243299862, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243299864, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243299910, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243299912, "dur": 58241, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243358165, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243358169, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243358202, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243358205, "dur": 200038, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243558253, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243558257, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243558288, "dur": 916, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243559208, "dur": 32009, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243591227, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243591231, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243591287, "dur": 28, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243591316, "dur": 4227, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243595550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243595553, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243595616, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243595620, "dur": 1524, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243597149, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243597195, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243597219, "dur": 60369, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243657599, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243657603, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243657636, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243657639, "dur": 748, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243658393, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243658395, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243658447, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243658474, "dur": 1083, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243659561, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 38654705664, "ts": 1754646243659589, "dur": 5920, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 1061, "ts": 1754646243666011, "dur": 926, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18540, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18540, "tid": 34359738368, "ts": 1754646242804222, "dur": 33150, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18540, "tid": 34359738368, "ts": 1754646242837372, "dur": 85098, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18540, "tid": 34359738368, "ts": 1754646242922471, "dur": 55, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18540, "tid": 1061, "ts": 1754646243666939, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18540, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18540, "tid": 30064771072, "ts": 1754646242708035, "dur": 957518, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18540, "tid": 30064771072, "ts": 1754646242708117, "dur": 96060, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18540, "tid": 30064771072, "ts": 1754646243665555, "dur": 36, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18540, "tid": 30064771072, "ts": 1754646243665568, "dur": 11, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18540, "tid": 1061, "ts": 1754646243666943, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754646242922963, "dur": 1383, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646242924356, "dur": 786, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646242925251, "dur": 54, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754646242925306, "dur": 346, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646242925743, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_D2B66E0C49661945.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754646242932042, "dur": 146, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754646242934819, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754646242934913, "dur": 173, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754646242938593, "dur": 301, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754646242925668, "dur": 20589, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646242946269, "dur": 712668, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646243658939, "dur": 140, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646243659119, "dur": 61, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646243659960, "dur": 59, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646243660048, "dur": 985, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754646242925887, "dur": 20393, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242946287, "dur": 2567, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242948855, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242949056, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242949222, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242949423, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242949962, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242950311, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242950524, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242950798, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242951043, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242951288, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242951598, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242951803, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242951863, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242952093, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242952379, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646242952639, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646242952480, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646242952944, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646242953051, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646242953150, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646242953529, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646242953681, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646242954380, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242954528, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646242956037, "dur": 256869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243212907, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646243215313, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646243215864, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646243214460, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646243216061, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243216206, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646243217223, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243217385, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243217471, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243217630, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243217848, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243218108, "dur": 76600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243294712, "dur": 62301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646243294710, "dur": 63199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646243358503, "dur": 82, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646243358634, "dur": 232980, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646243595859, "dur": 62148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646243595858, "dur": 62152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646243658032, "dur": 851, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646242926068, "dur": 20283, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646242946729, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646242946727, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646242946828, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646242946967, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646242948048, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646242949487, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754646242947051, "dur": 3014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646242950138, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646242950633, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646242950366, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646242952129, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646242952348, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646242952418, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646242952975, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646242953118, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646242953176, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646242953622, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1754646242954136, "dur": 142, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646242954702, "dur": 256909, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1754646243212909, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646243214122, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646243215904, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646243215265, "dur": 1124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646243216429, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646243217530, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646243217924, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646243218318, "dur": 440615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242925959, "dur": 20355, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242946322, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646242946427, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A3CA169B49A7C799.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646242946673, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_EF7BA45317B31323.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646242946884, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646242947032, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754646242947157, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646242947309, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646242947405, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754646242947735, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646242948053, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242948188, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242948374, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242948583, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242948767, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242948985, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242949161, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242949334, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242949796, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242949988, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242950192, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242950373, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242950576, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242951089, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242951298, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242951475, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242951696, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242951860, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242952076, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242952358, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646242952454, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646242952854, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242953043, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242953163, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646242953303, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242953386, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646242954203, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242954324, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646242954406, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646242954600, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242954670, "dur": 1383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646242956054, "dur": 256933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646243212996, "dur": 1497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646243214531, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754646243214530, "dur": 1319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646243215849, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646243216148, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754646243215956, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646243217052, "dur": 1170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646243218222, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646243218326, "dur": 440605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242925921, "dur": 20367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242946444, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646242946659, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_34719EFEEA08287A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646242946722, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646242946722, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646242947114, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754646242947818, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754646242947941, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17285705621239497859.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754646242948104, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242948382, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242948540, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242948705, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242948928, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242949129, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242949344, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242949779, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242949948, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242950154, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242950349, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242950543, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242950739, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242950974, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242951152, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242951399, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242951626, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242951864, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242952070, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242952419, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646242952638, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646242952543, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646242953031, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646242953121, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646242953553, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242953938, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242954153, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646242954203, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242954374, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242954529, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646242954595, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646242954848, "dur": 1197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646242956045, "dur": 256932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243212983, "dur": 1595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646243214579, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243214744, "dur": 1383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646243216128, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243216208, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646243217422, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243217586, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243217892, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243218040, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646243218359, "dur": 440644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242925944, "dur": 20359, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242946693, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_EEF40264B201505D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646242946886, "dur": 341, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754646242947397, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754646242947516, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754646242947657, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754646242947976, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754646242948138, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242948471, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242948653, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242948842, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242949039, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242949217, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242949428, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242949899, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242950169, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242950368, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242950612, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242950954, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242951138, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242951372, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242951569, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242951925, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242952074, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242952360, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646242952445, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646242952798, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242952915, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646242952978, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646242953093, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646242953467, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242953632, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646242953815, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646242954371, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242954545, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646242956102, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646242956278, "dur": 256635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646243212921, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646243214117, "dur": 1311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646243215429, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646243215905, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646243215643, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646243217055, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646243217432, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646243216869, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646243218354, "dur": 440669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242926001, "dur": 20325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242946715, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754646242946714, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_55F435D8463A793C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646242947029, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754646242947098, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754646242947483, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754646242947724, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754646242947973, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754646242948091, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242948398, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242948580, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242948818, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242948995, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242949173, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242949362, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242949801, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242949988, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242950183, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242950359, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242950753, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242951090, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242951333, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242951513, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242951711, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242951859, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242952072, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242952364, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646242952477, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646242952917, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646242953066, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646242953534, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646242954095, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242954269, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646242954581, "dur": 1458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646242956039, "dur": 258047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646243214087, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646243215188, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646243216398, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646243216699, "dur": 1322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646243218075, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646243218414, "dur": 440569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242926034, "dur": 20304, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242946605, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5E2DACE274283492.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646242946717, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242947148, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754646242947403, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754646242947744, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754646242947871, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754646242947973, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2842047945745124412.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754646242948081, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242948382, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242948551, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242948779, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242949007, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242949205, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242949395, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242949888, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242950084, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242950298, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242950503, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242950729, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242950965, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242951134, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242951495, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242951695, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242951923, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242952084, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242952359, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646242952431, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242952959, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646242953172, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646242953267, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\RayFire\\Plugins\\Windows\\x86_x64\\RFLib_DotNet_2018.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646242953515, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646242953266, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242953888, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242953953, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242954283, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646242954526, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646242954602, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242954977, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242955690, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242956109, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242956321, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646242956735, "dur": 336281, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646243294912, "dur": 5294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646243294701, "dur": 5565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646243300316, "dur": 258413, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646243560136, "dur": 98838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242926086, "dur": 20279, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242946457, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646242946456, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646242946724, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242946795, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646242946952, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_825BAFEC5BACF8AB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646242947055, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754646242947210, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754646242947396, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754646242947485, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754646242947943, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242948059, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242948191, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242948410, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242948609, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242948774, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242949025, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242949275, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242949498, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242950008, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242950202, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242950417, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242950643, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242950839, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242951240, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242951420, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242951611, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242951957, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242952084, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242952361, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646242952441, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646242952845, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646242953475, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\ExternalLink.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754646242954193, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646242952955, "dur": 1406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646242954362, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242954533, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646242954619, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646242954839, "dur": 1224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646242956063, "dur": 256895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646243212965, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646243214104, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646243215305, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646243214539, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646243215863, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646243216141, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646243217454, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646243217906, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646243218097, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646243218417, "dur": 440542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242926119, "dur": 20257, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242946430, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_D2B66E0C49661945.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646242946533, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646242946688, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_FEEE89EA26456A87.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646242946758, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242946829, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754646242947013, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754646242947160, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754646242947453, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754646242947589, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754646242947664, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754646242947849, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754646242948052, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242948194, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242948449, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242948610, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242948778, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242949044, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242949205, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242949391, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242949877, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242950068, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242950234, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242950423, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242950683, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242950964, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242951165, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242951388, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242951856, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242952064, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242952354, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646242952419, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242952474, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646242952844, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242953036, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242953106, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646242953560, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646242953752, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242953861, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646242954012, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242954199, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646242954533, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242954683, "dur": 1367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646242956051, "dur": 256917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646243212974, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646243214701, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646243214112, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646243215267, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646243215530, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646243215905, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646243215375, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireEditorAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646243216837, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646243217314, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646243217000, "dur": 1354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646243218412, "dur": 440577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242926196, "dur": 20196, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242946611, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_06AF3D16206C6E73.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646242947097, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754646242947177, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754646242947376, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754646242947904, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646242947989, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9147286922948009921.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646242948129, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242948430, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242948622, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242948790, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242948994, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242949265, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242949443, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242949865, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242950047, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242950441, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242950694, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242950998, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242951164, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242951515, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242951685, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242951853, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242952063, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242952353, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646242952427, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646242953485, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242953563, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646242953715, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646242954524, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646242954598, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646242954903, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646242955267, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646242955489, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646242956048, "dur": 256976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646243213034, "dur": 1067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646243214153, "dur": 1147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646243215460, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646243215536, "dur": 334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646243216148, "dur": 609, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-localization-l1-2-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646243215351, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646243217561, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646243217934, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646243218320, "dur": 440615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242926234, "dur": 20173, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242946688, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_467A1CC760B32A9D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646242946767, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242946881, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754646242947405, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754646242947602, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646242947791, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646242947929, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3818876520345881085.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646242948039, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242948178, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242948434, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242948632, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242948857, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242949068, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242949260, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242949453, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242949900, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242950075, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242950262, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242950429, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242950943, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242951115, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242951363, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242951551, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242951745, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242951856, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242952062, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242952351, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646242952449, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646242952824, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242952937, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646242953081, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646242953557, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646242953798, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242954194, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646242954007, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646242954468, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242954551, "dur": 1481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646242956033, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646242956115, "dur": 256809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646243212931, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646243214436, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646243215530, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646243214549, "dur": 1115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646243216148, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.Common.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646243215712, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646243216879, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646243217041, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646243218226, "dur": 341916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646243560143, "dur": 98806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242926271, "dur": 20147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242946615, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C8666D03B273DA0D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646242946746, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646242946746, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646242946861, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646242947000, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646242947113, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646242947205, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646242947386, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646242947509, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646242947943, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/61918516800488742.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646242948064, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242948377, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242948588, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242948847, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242949035, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242949225, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242949393, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242949877, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242950063, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242950282, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242950468, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242950782, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242951019, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242951237, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242951401, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242951584, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242951866, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242952089, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242952424, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646242952536, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646242953013, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646242953553, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_GlyphPairAdjustmentRecordPropertyDrawer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754646242953143, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646242953878, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646242954035, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646242954410, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242954531, "dur": 1503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646242956034, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646242956111, "dur": 256828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646243214525, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646243212954, "dur": 1722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646243214676, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646243215530, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646243214829, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646243216060, "dur": 1264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646243217438, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Splines.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646243217437, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646243217506, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646243217818, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754646243217818, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754646243217954, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646243218365, "dur": 440665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242926313, "dur": 20120, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242946439, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_711C290004B55458.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646242946603, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6E85C30F177A7F51.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646242946943, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754646242947404, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754646242947851, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754646242947981, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754646242948118, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242948411, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242948626, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242948923, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242949111, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242949329, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242949848, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242950040, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242950232, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242950432, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242950758, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242951246, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242951424, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242951601, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242951875, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242952088, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242952368, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646242952469, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646242953028, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646242953132, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646242953948, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646242953550, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646242954350, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242954526, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242955275, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646242955572, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646242956038, "dur": 256870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646243212909, "dur": 1500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646243214447, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646243215627, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646243216702, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646243215811, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646243217226, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646243218348, "dur": 440665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242926355, "dur": 20090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242946450, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646242946517, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646242946616, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646242946693, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_539666F57CEC7FE9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646242946907, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754646242947191, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754646242947305, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646242947573, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646242947738, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646242947929, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646242948057, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242948181, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242948481, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242948683, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242948887, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242949069, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242949296, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242949470, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242949925, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242950102, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242950275, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242950496, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242950742, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242951020, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242951235, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242951420, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242951626, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242951857, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242952065, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242952353, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646242952414, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242952712, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646242953211, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242953584, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646242953698, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646242954249, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242954529, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646242956035, "dur": 256876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646243212913, "dur": 1145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646243214060, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646243214826, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646243215261, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646243214162, "dur": 1410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646243215838, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646243215638, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/OpenFracture.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646243217700, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Session.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646243216987, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646243218322, "dur": 440607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242926396, "dur": 20060, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242946468, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646242946463, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242946684, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_3B4AA1572BDDA284.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242946737, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646242946736, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242946821, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242946967, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242947031, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242948045, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646242949496, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Outline.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754646242947149, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646242949853, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242950047, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242950231, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242950447, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242950700, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242951031, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242951244, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242951473, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242951690, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242951968, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242952067, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242952396, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242952516, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646242952912, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242953009, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242953124, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646242953503, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242953593, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242953703, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646242954377, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646242954442, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646242954749, "dur": 1293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646242956043, "dur": 256962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646243213015, "dur": 1597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646243215345, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646243214657, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646243215890, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646243215995, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646243217678, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clrjit.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646243217820, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\coreclr.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646243217090, "dur": 1279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646243218402, "dur": 440592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242926439, "dur": 20034, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242946589, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646242946677, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_61E9542AD38DD443.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646242946791, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754646242946860, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754646242947111, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754646242947208, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754646242947404, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754646242947763, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754646242947872, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8654950501350094070.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754646242948044, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242948355, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242948529, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242948685, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242948939, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242949122, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242949388, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242949827, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242950077, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242950332, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242950850, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242951084, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242951333, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242951562, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242951790, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242951853, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242952066, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242952352, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646242952437, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646242952928, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242952996, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242953160, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646242953237, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242953721, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Extensions\\TrackExtensions.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754646242953296, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646242954368, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242954527, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646242956035, "dur": 256869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646243212906, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646243214424, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646243215089, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.ObjectPool.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646243215532, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646243215905, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646243214552, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646243216433, "dur": 1283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646243217717, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646243217920, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646243218235, "dur": 377628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646243595866, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754646243595865, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754646243596026, "dur": 1615, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754646243597645, "dur": 61294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646243664288, "dur": 1381, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18540, "tid": 1061, "ts": 1754646243666989, "dur": 1054, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18540, "tid": 1061, "ts": 1754646243672203, "dur": 734, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18540, "tid": 1061, "ts": 1754646243665992, "dur": 6976, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}