{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18540, "tid": 1056, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18540, "tid": 1056, "ts": 1754646666099327, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18540, "tid": 1056, "ts": 1754646666099348, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18540, "tid": 1, "ts": 1754646665310374, "dur": 1053, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754646665311430, "dur": 14601, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754646665326034, "dur": 17047, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18540, "tid": 1056, "ts": 1754646666099353, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 18540, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665310345, "dur": 122511, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665432857, "dur": 665919, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665432869, "dur": 34, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665432904, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665432906, "dur": 316, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665433229, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665433269, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665433276, "dur": 2210, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435491, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435566, "dur": 147, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435714, "dur": 57, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435773, "dur": 1, "ph": "X", "name": "ProcessMessages 2819", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435775, "dur": 35, "ph": "X", "name": "ReadAsync 2819", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435815, "dur": 33, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435851, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435853, "dur": 35, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435889, "dur": 53, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435946, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665435985, "dur": 23, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436009, "dur": 28, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436040, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436042, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436080, "dur": 27, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436109, "dur": 36, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436148, "dur": 32, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436182, "dur": 26, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436211, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436236, "dur": 15, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436253, "dur": 12, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436266, "dur": 20, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436288, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436311, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436334, "dur": 39, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436374, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436397, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436419, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436443, "dur": 30, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436476, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436477, "dur": 32, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436511, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436513, "dur": 32, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436549, "dur": 31, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436581, "dur": 24, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436607, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436635, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436660, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436683, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436704, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436726, "dur": 22, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436749, "dur": 20, "ph": "X", "name": "ReadAsync 14", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436771, "dur": 65, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436843, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436846, "dur": 92, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436941, "dur": 1, "ph": "X", "name": "ProcessMessages 1408", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436944, "dur": 52, "ph": "X", "name": "ReadAsync 1408", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665436999, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437001, "dur": 30, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437032, "dur": 23, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437057, "dur": 24, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437085, "dur": 25, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437112, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437136, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437159, "dur": 33, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437195, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437226, "dur": 24, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437252, "dur": 24, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437277, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437302, "dur": 42, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437347, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437349, "dur": 30, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437381, "dur": 23, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437405, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437430, "dur": 23, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437454, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437480, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437481, "dur": 32, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437515, "dur": 23, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437540, "dur": 31, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437573, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437575, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437608, "dur": 22, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437631, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437654, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437677, "dur": 15, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437693, "dur": 18, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437712, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437735, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437758, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437782, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437804, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437827, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437846, "dur": 24, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437871, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437894, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437916, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437938, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437961, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665437984, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438007, "dur": 23, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438032, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438054, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438077, "dur": 22, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438099, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438123, "dur": 56, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438181, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438203, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438226, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438247, "dur": 17, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438266, "dur": 22, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438289, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438315, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438340, "dur": 22, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438363, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438387, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438411, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438434, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438455, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438478, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438501, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438525, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438548, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438572, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438595, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438617, "dur": 57, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438677, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438701, "dur": 25, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438727, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438750, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438772, "dur": 22, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438795, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438818, "dur": 22, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438841, "dur": 28, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438870, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438894, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438917, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438940, "dur": 18, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438959, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665438981, "dur": 22, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439004, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439028, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439051, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439075, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439094, "dur": 20, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439115, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439137, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439160, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439186, "dur": 23, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439211, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439233, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439256, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439274, "dur": 21, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439297, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439319, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439342, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439364, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439387, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439406, "dur": 21, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439428, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439450, "dur": 25, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439476, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439500, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439522, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439541, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439564, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439586, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439608, "dur": 21, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439631, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439653, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439676, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439699, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439723, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439744, "dur": 17, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439763, "dur": 21, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439787, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439810, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439833, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439856, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439879, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439898, "dur": 26, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439925, "dur": 23, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439950, "dur": 22, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439973, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665439996, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440020, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440043, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440064, "dur": 22, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440087, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440111, "dur": 22, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440134, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440157, "dur": 22, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440181, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440203, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440222, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440246, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440269, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440292, "dur": 15, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440308, "dur": 27, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440340, "dur": 28, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440369, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440393, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440416, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440417, "dur": 40, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440460, "dur": 26, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440488, "dur": 30, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440521, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440522, "dur": 40, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440565, "dur": 24, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440590, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440613, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440636, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440659, "dur": 20, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440680, "dur": 21, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440702, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440725, "dur": 43, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440773, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440776, "dur": 61, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440839, "dur": 1, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440841, "dur": 29, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440871, "dur": 30, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440905, "dur": 32, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440939, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440940, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440964, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665440988, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441010, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441030, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441055, "dur": 29, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441086, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441110, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441140, "dur": 28, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441170, "dur": 20, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441191, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441214, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441237, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441261, "dur": 95, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441361, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441397, "dur": 1, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441398, "dur": 20, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441420, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441441, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441463, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441485, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441507, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441530, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441551, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441571, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441594, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441616, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441638, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441659, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441677, "dur": 20, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441699, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441719, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441721, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441744, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441766, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441789, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441813, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441839, "dur": 23, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441863, "dur": 20, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441885, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441908, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441929, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441952, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441973, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665441993, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442015, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442038, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442061, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442083, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442105, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442125, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442147, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442170, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442192, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442213, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442235, "dur": 21, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442257, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442281, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442302, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442324, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442346, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442371, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442393, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442415, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442438, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442467, "dur": 31, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442500, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442528, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442529, "dur": 33, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442565, "dur": 24, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442591, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442612, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442635, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442659, "dur": 21, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442681, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442703, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442726, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442748, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442769, "dur": 96, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442866, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442889, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442912, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442934, "dur": 35, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442972, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665442995, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443018, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443043, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443072, "dur": 22, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443095, "dur": 21, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443118, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443141, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443165, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443188, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443206, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443227, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443249, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443271, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443290, "dur": 19, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443311, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443333, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443358, "dur": 20, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443379, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443402, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443425, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443449, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443472, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443493, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443515, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443539, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443557, "dur": 20, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443579, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443598, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443620, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443642, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443664, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443685, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443706, "dur": 20, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443728, "dur": 20, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443750, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443773, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443795, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443816, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443837, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443859, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443882, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443904, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443926, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443948, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443970, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665443992, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444014, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444036, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444057, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444078, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444100, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444122, "dur": 43, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444166, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444167, "dur": 21, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444190, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444211, "dur": 21, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444233, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444255, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444278, "dur": 20, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444300, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444321, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444344, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444363, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444384, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444407, "dur": 44, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444452, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444475, "dur": 22, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444500, "dur": 29, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444531, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444532, "dur": 29, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444563, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444587, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444610, "dur": 23, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444634, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444656, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444678, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444700, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444720, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444743, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444768, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444789, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444811, "dur": 26, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444839, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444865, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444887, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444910, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444933, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444956, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665444978, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445001, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445022, "dur": 20, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445043, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445045, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445069, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445092, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445114, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445135, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445156, "dur": 20, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445178, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445204, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445226, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445247, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445268, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445291, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445313, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445350, "dur": 23, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445374, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445396, "dur": 21, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445419, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445441, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445464, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445486, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445508, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445529, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445553, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445577, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445600, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445620, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445643, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445664, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445686, "dur": 21, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445710, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445732, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445754, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445777, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445799, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445822, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445845, "dur": 38, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445884, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445907, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445931, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445954, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665445977, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446000, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446023, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446041, "dur": 28, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446071, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446093, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446115, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446150, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446180, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446182, "dur": 24, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446208, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446232, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446254, "dur": 64, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446320, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446343, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446368, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446391, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446414, "dur": 30, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446447, "dur": 52, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446501, "dur": 37, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446539, "dur": 38, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446579, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446617, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446674, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446675, "dur": 25, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446702, "dur": 14, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446717, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446759, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446806, "dur": 36, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446844, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446846, "dur": 28, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446876, "dur": 47, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446925, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665446963, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447005, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447030, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447049, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447114, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447137, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447160, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447181, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447203, "dur": 98, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447302, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447326, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447348, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447370, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447392, "dur": 113, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447509, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447559, "dur": 34, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447596, "dur": 41, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447638, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447661, "dur": 20, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447683, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447707, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447726, "dur": 42, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447770, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447792, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447815, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447837, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447859, "dur": 35, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447895, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447918, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447940, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447963, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665447983, "dur": 52, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448036, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448059, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448081, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448103, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448125, "dur": 33, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448159, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448180, "dur": 20, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448202, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448223, "dur": 20, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448246, "dur": 39, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448287, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448308, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448329, "dur": 27, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448358, "dur": 25, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448386, "dur": 30, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448419, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448448, "dur": 32, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448482, "dur": 23, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448507, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448543, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448574, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448597, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448619, "dur": 22, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448643, "dur": 20, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448665, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448687, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448709, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448732, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448753, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448776, "dur": 32, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448810, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448832, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448856, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448878, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448900, "dur": 34, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448936, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448958, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665448980, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449005, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449028, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449062, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449083, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449106, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449128, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449149, "dur": 37, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449187, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449209, "dur": 21, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449231, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449253, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449274, "dur": 41, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449317, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449339, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449361, "dur": 30, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449392, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449415, "dur": 30, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449447, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449469, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449492, "dur": 21, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449515, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449537, "dur": 34, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449572, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449597, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449620, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449642, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449663, "dur": 36, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449700, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449722, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449745, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449767, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449788, "dur": 33, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449822, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449844, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449866, "dur": 21, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449888, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449909, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449910, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449950, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665449992, "dur": 21, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450015, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450037, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450059, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450084, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450106, "dur": 33, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450140, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450160, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450182, "dur": 16, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450199, "dur": 28, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450229, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450251, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450273, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450296, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450318, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450343, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450365, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450387, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450410, "dur": 28, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450439, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450461, "dur": 27, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450489, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450510, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450534, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450555, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450577, "dur": 21, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450600, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450618, "dur": 31, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450650, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450672, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450694, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450717, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450739, "dur": 22, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450763, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450785, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450807, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450829, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450850, "dur": 20, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450871, "dur": 38, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450910, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450933, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450955, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450977, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665450998, "dur": 33, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451032, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451053, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451075, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451097, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451120, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451142, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451164, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451186, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451208, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451230, "dur": 21, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451252, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451293, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451317, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451342, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451363, "dur": 19, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451384, "dur": 35, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451423, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451455, "dur": 24, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451480, "dur": 24, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451506, "dur": 41, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451548, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451572, "dur": 2, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451575, "dur": 26, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451603, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451628, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451652, "dur": 32, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451686, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451706, "dur": 20, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451728, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451749, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451771, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451810, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451831, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451852, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451874, "dur": 42, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451922, "dur": 2, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451928, "dur": 55, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665451987, "dur": 43, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452033, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452035, "dur": 49, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452087, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452088, "dur": 53, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452147, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452149, "dur": 47, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452198, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452201, "dur": 41, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452244, "dur": 40, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452285, "dur": 32, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452319, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452357, "dur": 35, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452394, "dur": 29, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452427, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452430, "dur": 36, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452468, "dur": 34, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452504, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452506, "dur": 37, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452546, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452547, "dur": 35, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452586, "dur": 32, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452622, "dur": 38, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452663, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452688, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452709, "dur": 65, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452776, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452802, "dur": 26, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452831, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452832, "dur": 29, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452863, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452900, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452961, "dur": 27, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665452991, "dur": 20, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453013, "dur": 53, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453068, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453092, "dur": 19, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453112, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453133, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453155, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453193, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453216, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453238, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453260, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453278, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453318, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453341, "dur": 20, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453362, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453384, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453407, "dur": 49, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453458, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453485, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453507, "dur": 29, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453541, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453568, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453594, "dur": 22, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453617, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453618, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453643, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453667, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453689, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453712, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453734, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453757, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453778, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453803, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453844, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453867, "dur": 29, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453898, "dur": 29, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453930, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453931, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453958, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665453980, "dur": 22, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454003, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454026, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454046, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454088, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454110, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454134, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454157, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454177, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454195, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454216, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454238, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454261, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454283, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454305, "dur": 17, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454323, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454356, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454379, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454401, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454425, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454452, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454476, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454505, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454535, "dur": 25, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454562, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454585, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454617, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454639, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454661, "dur": 25, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454689, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454713, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454736, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454757, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454779, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454801, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454824, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454846, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454867, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454913, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454934, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454956, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665454979, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455002, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455025, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455048, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455068, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455090, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455111, "dur": 20, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455133, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455179, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455201, "dur": 97, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455302, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455337, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455366, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455390, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455415, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455438, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455488, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455491, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455537, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455542, "dur": 38, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455585, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455587, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455627, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455629, "dur": 32, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455664, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455667, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455704, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455738, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455739, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455771, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455796, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455821, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455850, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455878, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455903, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455905, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455935, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455963, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455965, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665455993, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456021, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456023, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456050, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456052, "dur": 23, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456077, "dur": 26, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456106, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456130, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456132, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456159, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456187, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456217, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456242, "dur": 28, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456273, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456303, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456328, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456355, "dur": 23, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456380, "dur": 18, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456401, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456449, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456481, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456483, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456525, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456526, "dur": 28, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456556, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456582, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456612, "dur": 28, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456643, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456644, "dur": 36, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456682, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456684, "dur": 25, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456712, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456714, "dur": 33, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456747, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456749, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456775, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456778, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456807, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456833, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456861, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456887, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456971, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665456998, "dur": 1859, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665458861, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665458895, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665458897, "dur": 302, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665459201, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665459229, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665459253, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665459420, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665459457, "dur": 1187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460649, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460682, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460707, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460729, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460895, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460929, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665460931, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461021, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461056, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461058, "dur": 27, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461087, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461112, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461136, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461372, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461405, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461434, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461461, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461465, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461499, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461533, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461562, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461564, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461597, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461630, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461655, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461681, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461711, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461735, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461756, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461789, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461813, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461837, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665461859, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462036, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462057, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462118, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462139, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462223, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462252, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462274, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462277, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462299, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462321, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462343, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462379, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462401, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462427, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462449, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462474, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462500, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462522, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462548, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462573, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462594, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462620, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462645, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462668, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462689, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462712, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462752, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462777, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462863, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462886, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462914, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462942, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462967, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665462991, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463015, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463037, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463058, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463081, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463104, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463152, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463174, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463251, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463273, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463276, "dur": 33, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463311, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463333, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463462, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463486, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463543, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463562, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463582, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463605, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463655, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463704, "dur": 162, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463869, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463900, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463948, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665463979, "dur": 415, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464397, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464423, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464481, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464513, "dur": 369, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464887, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464924, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665464974, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465012, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465041, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465183, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465214, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465369, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465395, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465549, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465575, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665465578, "dur": 246932, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665712521, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665712525, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665712561, "dur": 26, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665712588, "dur": 2175, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714773, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714805, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714807, "dur": 23, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714833, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714834, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714865, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714948, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714986, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665714987, "dur": 480, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715470, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715496, "dur": 281, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715781, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715784, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715820, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715850, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715887, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715912, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665715937, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716026, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716048, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716072, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716134, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716154, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716181, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716203, "dur": 635, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716843, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665716888, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717029, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717059, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717060, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717146, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717178, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717241, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717268, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717298, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717319, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717377, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717405, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717426, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717449, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717474, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717511, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717547, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717602, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717623, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717625, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717648, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717650, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717708, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717814, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717848, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717882, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717904, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717936, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717938, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665717986, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718009, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718043, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718073, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718102, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718134, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718165, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718189, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718212, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718234, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718258, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718289, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718314, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718342, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718376, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718378, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718410, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718433, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718458, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718460, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718494, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718525, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718584, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718623, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718654, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718687, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718717, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718738, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718758, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718789, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718818, "dur": 109, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718929, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718955, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665718958, "dur": 32613, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665751581, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665751585, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665751619, "dur": 40, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665751660, "dur": 65056, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665816728, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665816733, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665816772, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646665816777, "dur": 209781, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666026568, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666026572, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666026603, "dur": 23, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666026627, "dur": 4316, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666030950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666030953, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666030984, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666030987, "dur": 1339, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666032332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666032334, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666032375, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666032399, "dur": 58634, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091043, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091047, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091098, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091105, "dur": 831, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091950, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666091990, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666092019, "dur": 456, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666092480, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 25769803776, "ts": 1754646666092503, "dur": 6269, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 1056, "ts": 1754646666099360, "dur": 927, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18540, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18540, "tid": 21474836480, "ts": 1754646665310311, "dur": 32793, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18540, "tid": 21474836480, "ts": 1754646665343106, "dur": 89755, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18540, "tid": 21474836480, "ts": 1754646665432862, "dur": 67, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18540, "tid": 1056, "ts": 1754646666100289, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18540, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18540, "tid": 17179869184, "ts": 1754646665213355, "dur": 885465, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18540, "tid": 17179869184, "ts": 1754646665213444, "dur": 96831, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18540, "tid": 17179869184, "ts": 1754646666098822, "dur": 56, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18540, "tid": 17179869184, "ts": 1754646666098836, "dur": 11, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18540, "tid": 1056, "ts": 1754646666100295, "dur": 35, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754646665432894, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646665432984, "dur": 1262, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646665434257, "dur": 788, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646665435152, "dur": 56, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754646665435208, "dur": 355, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646665444237, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754646665444506, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754646665435578, "dur": 19734, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646665455323, "dur": 636815, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646666092139, "dur": 127, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646666092266, "dur": 125, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646666092510, "dur": 52, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646666092591, "dur": 1428, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754646665435789, "dur": 19555, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665455655, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665455744, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_20F5B884F62AB761.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665455843, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665456037, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754646665456198, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754646665456518, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754646665456635, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754646665456945, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665457278, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665457433, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665457653, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665457829, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665457974, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665458199, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665458365, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665458817, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665458965, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665459129, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665459339, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665459514, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665459717, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665459943, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665460159, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665460357, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665460535, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665460801, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665461055, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665461134, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665461199, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665461730, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665461839, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665462330, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665462394, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665462519, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665463013, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665463123, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646665463184, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665463379, "dur": 1656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665465035, "dur": 248693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665713736, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665714865, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665716609, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646665715922, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireEditorAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665717595, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646665717258, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646665718711, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646665718901, "dur": 373265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665435860, "dur": 19505, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665455373, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646665455581, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646665455656, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1703D820AF2EEFB1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646665455811, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665455955, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754646665456239, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754646665456399, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754646665456657, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754646665456942, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665457086, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665457292, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665457478, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665458032, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665458291, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665458808, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665458982, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665459131, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665459348, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665459503, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665459698, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665459872, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665460045, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665460237, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665460446, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665460534, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665460778, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665461054, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646665461146, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646665461585, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665461718, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646665461779, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646665461958, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646665462955, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665463130, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665465032, "dur": 248726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665713764, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646665714892, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646665716608, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646665716043, "dur": 1243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646665717287, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665718076, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646665717361, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646665718724, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646665718899, "dur": 373288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665435890, "dur": 19486, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665455638, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5F2BB7F24639C806.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665455735, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1B8FAEC3513ADBA8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665455866, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665455930, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665456054, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665456944, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754646665458420, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754646665456128, "dur": 2704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665458833, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665458996, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665459215, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665459653, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665459880, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665460052, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665460270, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665460422, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665460607, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665460791, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665461085, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665461164, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665461560, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665461741, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665461929, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665462467, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665462528, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646665462621, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665462688, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665463078, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665463137, "dur": 1897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665465034, "dur": 248684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665713725, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665714907, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665716050, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646665716609, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754646665716254, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665718102, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Binder.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754646665717540, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646665718844, "dur": 373287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665435957, "dur": 19481, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665455450, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B6DB781081D3DD3C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665455636, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_3D201F391D2079F2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665455781, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_738A9C5A5D284ED5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665455872, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665455934, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665456018, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754646665456272, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754646665456375, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754646665456530, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754646665456942, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665457249, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665457404, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665457651, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665457838, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665457990, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665458240, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665458418, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665458872, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665459042, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665459248, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665459432, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665459678, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665459833, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665460012, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665460204, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665460410, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665460694, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665460791, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665461060, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665461135, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665461187, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646665461549, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665461709, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665461803, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646665462175, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665462253, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646665462655, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646665462375, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646665462839, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665463041, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665463133, "dur": 1900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665465033, "dur": 248706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665713745, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646665714840, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665714909, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646665715984, "dur": 1148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646665718140, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646665717151, "dur": 1554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646665718768, "dur": 373361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665435927, "dur": 19491, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665455433, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_370C590F53957EFB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646665455734, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C8666D03B273DA0D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646665455811, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665456394, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754646665456637, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754646665456905, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2842047945745124412.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754646665456959, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665457286, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665457515, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665457682, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665457835, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665457988, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665458220, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665458716, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665458961, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665459126, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665459365, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665459543, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665459741, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665459939, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665460143, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665460407, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665460647, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665460792, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665461064, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646665461145, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646665461536, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646665461731, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646665462139, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665462574, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646665462352, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646665462888, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665463126, "dur": 1896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665465022, "dur": 248661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665713685, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646665715098, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646665714875, "dur": 1109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646665715985, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665716971, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646665717309, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646665717575, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646665716145, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646665717885, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665718492, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665718618, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665718722, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646665718894, "dur": 373311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665435848, "dur": 19508, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665455658, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646665455809, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754646665455808, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646665456045, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754646665456225, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754646665456415, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754646665456513, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754646665456910, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754646665456975, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665457294, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665457498, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665457708, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665458109, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665458392, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665458834, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665458999, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665459188, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665459367, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665459557, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665459748, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665459930, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665460173, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665460348, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665460544, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665460776, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665461052, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646665461136, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665461429, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665461541, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646665461640, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646665461747, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665462166, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665462370, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665462900, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665463021, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665463125, "dur": 1895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665465027, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646665465120, "dur": 248576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665713705, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665714877, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665715063, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665716610, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754646665716300, "dur": 1410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646665717790, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665717993, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665718325, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665718386, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665718614, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665718692, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646665718778, "dur": 373449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665435782, "dur": 19551, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665455341, "dur": 2261, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665457603, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665457817, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665457980, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665458210, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665458408, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665458883, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665459087, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665459296, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665459488, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665459654, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665459964, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665460183, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665460371, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665460527, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665460779, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665461049, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646665461131, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646665461234, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665461682, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646665461837, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665462258, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665462348, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665462827, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665463066, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665463124, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646665463197, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665463593, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646665463661, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665464525, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646665464592, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665465082, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665465292, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665465476, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665465671, "dur": 285987, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665752928, "dur": 62278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646665752926, "dur": 63140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754646665816666, "dur": 87, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646665816809, "dur": 209843, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754646666030903, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754646666030902, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754646666031044, "dur": 1403, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754646666032449, "dur": 59687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665435917, "dur": 19478, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665455413, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665455507, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665455580, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665455825, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646665455824, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665456199, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754646665456444, "dur": 289, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754646665456982, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665457298, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665457526, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665457705, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665457943, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665458130, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665458278, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665458703, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665458918, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665459111, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665459268, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665459482, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665459701, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665459900, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665460069, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665460288, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665460440, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665460540, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665460787, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665461070, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665461159, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665461219, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665461931, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646665461757, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665462590, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665462711, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665462822, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665463154, "dur": 1864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665465019, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646665465108, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665465288, "dur": 248398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665713694, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665714883, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665716084, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646665716609, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646665716306, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665717549, "dur": 599, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646665717495, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646665719058, "dur": 373118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665435994, "dur": 19456, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665455587, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E0E4ACDF62B3A25F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646665455681, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_06AF3D16206C6E73.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646665455809, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665455919, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_3B326C6243819FB3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646665456165, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754646665456415, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754646665456927, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665457233, "dur": 146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665457379, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665457609, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665457795, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665458102, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665458281, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665458736, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665458923, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665459086, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665459258, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665459417, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665459585, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665459879, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665460082, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665460256, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665460537, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665460785, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665461065, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646665461148, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665461556, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646665461765, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665462341, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665463071, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665463131, "dur": 1892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665465023, "dur": 248759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665713784, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665714858, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665714945, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665715922, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646665716621, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646665716003, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665717595, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646665717547, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646665718905, "dur": 373289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665436036, "dur": 19426, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665455641, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5E2DACE274283492.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646665455822, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646665455821, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646665455974, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646665456143, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646665456274, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754646665456509, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646665456717, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646665456935, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665457233, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665457416, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665457663, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665457850, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665458093, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665458358, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665458919, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665459080, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665459262, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665459430, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665459593, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665459857, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665460115, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665460267, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665460481, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665460543, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665460781, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665461049, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646665461156, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665461677, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665461751, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646665461846, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665461917, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665462329, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665462397, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646665462514, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665462738, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646665462583, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665463139, "dur": 1887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665465026, "dur": 248655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665713683, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665714851, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665714921, "dur": 968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665716622, "dur": 367, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscorrc.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646665715929, "dur": 1610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/OpenFracture.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646665717647, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665717775, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665718173, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665718310, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665718627, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646665718732, "dur": 312178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646666030912, "dur": 60159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646666030911, "dur": 60162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646666091097, "dur": 959, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646665436073, "dur": 19401, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665455522, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646665455521, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646665455608, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646665455742, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_5DAAF2C2D061984C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646665455844, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665456092, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754646665456265, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754646665456395, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754646665456912, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646665456963, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665457314, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665457479, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665457705, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665457882, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665458123, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665458321, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665458805, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665459011, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665459183, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665459365, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665459526, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665459808, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665460030, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665460233, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665460416, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665460594, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665460789, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665461060, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646665461144, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665461520, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646665461649, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665462702, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665462779, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665463010, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646665463092, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665463268, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665465031, "dur": 248647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646665713680, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665714881, "dur": 1000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665716609, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646665717155, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646665715933, "dur": 1438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665717876, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646665717417, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646665718760, "dur": 373367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665436090, "dur": 19409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665455520, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646665455510, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665455646, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AB4BD236749C82D4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665455815, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646665455814, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665455957, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456034, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456145, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456379, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456480, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456544, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456643, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646665456938, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665457249, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665457416, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665457645, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665457850, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665458033, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665458700, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665459019, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665459411, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665459684, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665459914, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665460146, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665460324, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665460476, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665460528, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665460777, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665461051, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665461122, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665461211, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665461666, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665461764, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665461903, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665462383, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665462492, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665462586, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665463035, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665463124, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665463716, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646665463779, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665464069, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665465025, "dur": 248655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665713681, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665714830, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665714905, "dur": 1221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665716971, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.Core.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646665716148, "dur": 1779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646665718098, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665718276, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665718364, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665718630, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646665718757, "dur": 373377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665436113, "dur": 19407, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665455585, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665455766, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EE72F1C640402F2B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665455836, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646665455835, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665455915, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665456054, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665456939, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646665458234, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754646665458420, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754646665456131, "dur": 3152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665459346, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665459525, "dur": 1204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665460843, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665461059, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665461160, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665461585, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665461698, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646665461830, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646665461789, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665462577, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646665462344, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1754646665462886, "dur": 100, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665463370, "dur": 249236, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1754646665713681, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665714863, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665716612, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646665715893, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665717164, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646665718391, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665718594, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665718705, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646665718863, "dur": 373358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665436159, "dur": 19370, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665455584, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3C3F89D87E20F844.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665455852, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665456016, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_8573C5AEBE7FAC0E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665456110, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646665456189, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646665456462, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754646665456648, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754646665456953, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665457292, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665457484, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665457674, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665457934, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665458163, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665458330, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665458793, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665458964, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665459123, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665459347, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665459518, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665459766, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665459974, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665460208, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665460367, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665460536, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665460777, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665461053, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665461123, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665461176, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665461479, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665461619, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665461756, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665461851, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665462432, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646665462742, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646665462613, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665463149, "dur": 1880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665465029, "dur": 248739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665713773, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665714888, "dur": 698, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665716609, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646665715588, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665716834, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665716974, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646665718443, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646665716955, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646665718727, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646665719063, "dur": 373084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665436183, "dur": 19356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665455579, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5F9E4D3CAA1E8246.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646665455816, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665456282, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754646665456387, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754646665456966, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665457334, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665457522, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665457679, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665457856, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665458038, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665458245, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665458760, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665458917, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665459079, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665459235, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665459487, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665459651, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665459847, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665460018, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665460238, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665460430, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665460583, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665460795, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665461066, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646665461159, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665461484, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665461613, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646665461732, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665461809, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646665461947, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665462331, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665462538, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665462653, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665462813, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665463127, "dur": 1892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665465020, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646665465103, "dur": 248605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665713715, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665714842, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646665714965, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665716608, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-conio-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646665716178, "dur": 1263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665718140, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Stores.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646665717489, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646665718908, "dur": 373248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665436244, "dur": 19312, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665455612, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646665455902, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754646665456017, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_99AE285BDA61FA2E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646665456128, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754646665456221, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754646665456326, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754646665456936, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665457292, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665457484, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665457695, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665457947, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665458161, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665458415, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665459039, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665459224, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665459400, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665459546, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665459809, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665459983, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665460198, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665460386, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665460578, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665460786, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665461048, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646665461166, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665462171, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646665462359, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665463190, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665463427, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665463757, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665463977, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665465033, "dur": 248716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665713754, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665714869, "dur": 1021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665715891, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665716610, "dur": 370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Authorization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646665717309, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646665717595, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646665716151, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646665717960, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665718038, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646665718037, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646665718292, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665718556, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665718708, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646665718896, "dur": 373316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646666097304, "dur": 1251, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18540, "tid": 1056, "ts": 1754646666100372, "dur": 828, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18540, "tid": 1056, "ts": 1754646666103151, "dur": 624, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18540, "tid": 1056, "ts": 1754646666099338, "dur": 4465, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}