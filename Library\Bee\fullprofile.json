{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18540, "tid": 1067, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18540, "tid": 1067, "ts": 1754645128469131, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18540, "tid": 1067, "ts": 1754645128469152, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18540, "tid": 1, "ts": 1754645127697711, "dur": 1215, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754645127698931, "dur": 16043, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754645127714977, "dur": 17295, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18540, "tid": 1067, "ts": 1754645128469156, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 18540, "tid": 115964116992, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127697687, "dur": 120633, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127818323, "dur": 650310, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127818337, "dur": 707, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127819055, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127819058, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127819120, "dur": 7, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127819128, "dur": 2052, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821185, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821265, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821268, "dur": 38, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821309, "dur": 39, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821350, "dur": 30, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821382, "dur": 29, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821413, "dur": 26, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821440, "dur": 41, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821486, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821490, "dur": 56, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821548, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821550, "dur": 46, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821599, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821601, "dur": 36, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821641, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821644, "dur": 43, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821692, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821695, "dur": 48, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821746, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821785, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821787, "dur": 39, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821829, "dur": 35, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821866, "dur": 3, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821870, "dur": 32, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821905, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821908, "dur": 42, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821952, "dur": 30, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821985, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127821987, "dur": 61, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822051, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822054, "dur": 40, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822098, "dur": 31, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822131, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822152, "dur": 25, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822179, "dur": 25, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822208, "dur": 30, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822240, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822261, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822285, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822308, "dur": 31, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822341, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822343, "dur": 35, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822381, "dur": 24, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822407, "dur": 23, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822431, "dur": 23, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822456, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822478, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822503, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822526, "dur": 22, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822550, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822573, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822596, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822619, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822638, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822640, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822663, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822686, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822709, "dur": 22, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822733, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822756, "dur": 22, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822779, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822802, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822825, "dur": 14, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822841, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822863, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822888, "dur": 27, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822917, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822919, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822942, "dur": 15, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822958, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822981, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127822983, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823008, "dur": 22, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823031, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823050, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823074, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823097, "dur": 21, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823120, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823145, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823168, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823191, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823211, "dur": 18, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823229, "dur": 14, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823245, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823267, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823290, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823313, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823333, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823352, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823374, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823396, "dur": 18, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823415, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823438, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823457, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823479, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823503, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823525, "dur": 24, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823550, "dur": 34, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823586, "dur": 27, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823614, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823639, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823662, "dur": 20, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823683, "dur": 24, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823709, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823728, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823752, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823775, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823796, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823818, "dur": 22, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823842, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823865, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823889, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823908, "dur": 21, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823930, "dur": 28, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823960, "dur": 20, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127823981, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824005, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824027, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824052, "dur": 22, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824075, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824098, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824121, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824143, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824167, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824189, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824212, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824235, "dur": 18, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824255, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824277, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824297, "dur": 62, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824361, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824387, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824410, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824444, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824463, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824488, "dur": 25, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824515, "dur": 23, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824539, "dur": 25, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824565, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824586, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824609, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824633, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824654, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824678, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824701, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824724, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824746, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824766, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824787, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824807, "dur": 21, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824829, "dur": 21, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824852, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824875, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824898, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824922, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824944, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824967, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127824990, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825013, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825037, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825061, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825083, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825105, "dur": 14, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825121, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825142, "dur": 23, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825166, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825191, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825213, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825235, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825254, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825278, "dur": 17, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825299, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825321, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825346, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825369, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825391, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825411, "dur": 18, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825429, "dur": 18, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825449, "dur": 28, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825479, "dur": 26, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825507, "dur": 19, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825527, "dur": 30, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825560, "dur": 30, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825592, "dur": 20, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825614, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825639, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825663, "dur": 22, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825687, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825709, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825732, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825755, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825782, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825805, "dur": 22, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825828, "dur": 14, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825844, "dur": 20, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825866, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825888, "dur": 36, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825926, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825945, "dur": 14, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825961, "dur": 17, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127825979, "dur": 27, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826008, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826030, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826051, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826053, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826076, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826095, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826118, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826140, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826162, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826182, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826205, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826228, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826246, "dur": 20, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826267, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826291, "dur": 29, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826322, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826344, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826367, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826390, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826412, "dur": 21, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826436, "dur": 23, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826460, "dur": 23, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826484, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826508, "dur": 23, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826532, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826554, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826569, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826589, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826610, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826629, "dur": 18, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826649, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826672, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826695, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826717, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826741, "dur": 25, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826768, "dur": 22, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826791, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826814, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826836, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826858, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826880, "dur": 22, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826903, "dur": 22, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826926, "dur": 23, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826951, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826953, "dur": 27, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127826982, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827006, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827030, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827049, "dur": 18, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827068, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827091, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827113, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827136, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827160, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827182, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827201, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827223, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827243, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827262, "dur": 22, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827286, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827309, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827333, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827354, "dur": 22, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827377, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827400, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827423, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827446, "dur": 19, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827466, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827488, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827509, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827533, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827556, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827579, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827604, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827627, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827650, "dur": 80, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827732, "dur": 22, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827755, "dur": 15, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827772, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827794, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827817, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827841, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827860, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827879, "dur": 14, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827895, "dur": 26, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827922, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827946, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827969, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127827988, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828007, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828030, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828052, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828074, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828097, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828123, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828142, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828165, "dur": 20, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828187, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828209, "dur": 24, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828234, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828258, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828276, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828301, "dur": 24, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828327, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828350, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828374, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828397, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828422, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828442, "dur": 103, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828546, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828570, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828593, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828612, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828630, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828653, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828677, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828698, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828721, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828744, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828768, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828787, "dur": 26, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828815, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828838, "dur": 18, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828857, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828880, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828899, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828918, "dur": 14, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828934, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828956, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127828980, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829003, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829022, "dur": 18, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829042, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829060, "dur": 21, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829083, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829106, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829125, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829151, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829179, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829213, "dur": 28, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829242, "dur": 22, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829265, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829286, "dur": 23, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829310, "dur": 22, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829334, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829356, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829380, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829400, "dur": 26, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829427, "dur": 24, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829455, "dur": 30, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829487, "dur": 24, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829512, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829536, "dur": 17, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829554, "dur": 17, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829572, "dur": 21, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829595, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829617, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829640, "dur": 17, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829659, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829681, "dur": 18, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829700, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829724, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829746, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829769, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829792, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829815, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829837, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829859, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829880, "dur": 18, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829900, "dur": 33, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829934, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829957, "dur": 14, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829972, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127829994, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830017, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830036, "dur": 21, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830058, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830074, "dur": 27, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830102, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830128, "dur": 29, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830158, "dur": 19, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830178, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830199, "dur": 24, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830225, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830250, "dur": 22, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830274, "dur": 35, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830312, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830313, "dur": 34, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830350, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830353, "dur": 28, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830382, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830384, "dur": 21, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830407, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830431, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830454, "dur": 23, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830481, "dur": 32, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830515, "dur": 24, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830540, "dur": 22, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830564, "dur": 22, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830588, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830610, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830634, "dur": 17, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830653, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830671, "dur": 21, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830694, "dur": 25, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830720, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830743, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830774, "dur": 22, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830798, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830817, "dur": 19, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830837, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830861, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830884, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830908, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830931, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830953, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127830980, "dur": 22, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831003, "dur": 29, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831037, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831042, "dur": 55, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831100, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831101, "dur": 33, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831136, "dur": 19, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831156, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831176, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831204, "dur": 30, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831236, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831257, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831280, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831303, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831323, "dur": 23, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831347, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831371, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831395, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831417, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831440, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831464, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831490, "dur": 40, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831533, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831535, "dur": 43, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831581, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831582, "dur": 36, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831620, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831641, "dur": 28, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831672, "dur": 30, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831705, "dur": 21, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831728, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831749, "dur": 22, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831773, "dur": 18, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831793, "dur": 18, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831812, "dur": 21, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831834, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831852, "dur": 19, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831873, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831896, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831920, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831948, "dur": 22, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127831972, "dur": 58, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832031, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832055, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832079, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832101, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832120, "dur": 27, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832149, "dur": 30, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832181, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832204, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832205, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832229, "dur": 20, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832251, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832305, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832336, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832359, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832383, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832405, "dur": 30, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832436, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832461, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832485, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832508, "dur": 17, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832527, "dur": 20, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832549, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832597, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832621, "dur": 24, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832646, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832670, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832689, "dur": 32, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832722, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832745, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832767, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832790, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832809, "dur": 34, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832845, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832870, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832891, "dur": 21, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832915, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832937, "dur": 48, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127832987, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833012, "dur": 27, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833040, "dur": 21, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833063, "dur": 20, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833084, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833111, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833134, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833156, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833179, "dur": 17, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833198, "dur": 33, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833232, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833255, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833277, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833300, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833321, "dur": 33, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833355, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833378, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833402, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833424, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833446, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833492, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833515, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833539, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833562, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833581, "dur": 37, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833619, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833642, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833665, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833690, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833709, "dur": 34, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833744, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833767, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833789, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833812, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833831, "dur": 37, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833870, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833892, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833915, "dur": 32, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833948, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833966, "dur": 30, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127833998, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834020, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834039, "dur": 14, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834054, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834075, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834098, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834120, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834143, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834166, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834185, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834206, "dur": 20, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834227, "dur": 30, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834259, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834282, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834306, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834328, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834349, "dur": 34, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834385, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834416, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834438, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834460, "dur": 19, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834481, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834509, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834532, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834555, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834578, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834599, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834633, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834656, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834678, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834701, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834720, "dur": 34, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834755, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834777, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834799, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834818, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834840, "dur": 43, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834884, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834907, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834930, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834953, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127834972, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835013, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835033, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835056, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835079, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835101, "dur": 43, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835145, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835169, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835193, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835215, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835237, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835267, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835290, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835304, "dur": 21, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835326, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835349, "dur": 47, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835398, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835424, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835447, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835470, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835492, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835515, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835537, "dur": 25, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835564, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835582, "dur": 18, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835602, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835645, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835668, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835691, "dur": 26, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835719, "dur": 21, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835742, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835764, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835786, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835809, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835827, "dur": 17, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835846, "dur": 17, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835865, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835900, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835923, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835944, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835966, "dur": 25, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127835993, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836015, "dur": 40, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836056, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836078, "dur": 21, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836101, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836123, "dur": 25, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836150, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836172, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836194, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836217, "dur": 36, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836254, "dur": 28, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836284, "dur": 25, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836310, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836337, "dur": 41, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836382, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836385, "dur": 57, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836446, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836450, "dur": 53, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836505, "dur": 28, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836535, "dur": 39, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836577, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836579, "dur": 44, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836625, "dur": 29, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836657, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836682, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836722, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836751, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836775, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836797, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836843, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836866, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836889, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836912, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836939, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836962, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127836985, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837007, "dur": 21, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837030, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837049, "dur": 39, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837090, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837115, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837138, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837161, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837187, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837189, "dur": 53, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837245, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837281, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837283, "dur": 33, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837318, "dur": 28, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837348, "dur": 45, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837397, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837423, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837424, "dur": 23, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837448, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837472, "dur": 43, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837517, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837542, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837565, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837587, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837614, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837616, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837666, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837693, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837694, "dur": 29, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837726, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837749, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837792, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837815, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837839, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837861, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837880, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837916, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837939, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837962, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127837985, "dur": 18, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838004, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838043, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838070, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838092, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838116, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838138, "dur": 36, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838175, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838199, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838226, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838248, "dur": 23, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838272, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838297, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838316, "dur": 21, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838338, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838365, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838387, "dur": 43, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838431, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838456, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838478, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838501, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838522, "dur": 31, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838555, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838577, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838599, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838622, "dur": 18, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838641, "dur": 42, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838684, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838704, "dur": 24, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838730, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838752, "dur": 17, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838771, "dur": 38, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838812, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838839, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838861, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838884, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838906, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838930, "dur": 55, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127838986, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839015, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839033, "dur": 18, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839052, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839095, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839117, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839139, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839166, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839185, "dur": 32, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839218, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839241, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839260, "dur": 21, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839282, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839301, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839344, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839367, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839389, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839408, "dur": 21, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839431, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839454, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839477, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839495, "dur": 20, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839517, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839536, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839556, "dur": 17, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839574, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839607, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839629, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839651, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839674, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839698, "dur": 26, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839726, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839749, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839771, "dur": 20, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839792, "dur": 21, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839815, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839834, "dur": 30, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839865, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839887, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839906, "dur": 28, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839935, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839958, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127839981, "dur": 26, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840008, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840031, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840054, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840075, "dur": 17, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840094, "dur": 19, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840115, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840150, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840174, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840200, "dur": 24, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840226, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840255, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840256, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840295, "dur": 43, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840340, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840361, "dur": 18, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840380, "dur": 41, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840423, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840444, "dur": 17, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840463, "dur": 89, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840554, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840576, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840621, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840624, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840672, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840673, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840708, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840750, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840752, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840782, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840784, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840812, "dur": 32, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840849, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840853, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840904, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840905, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840928, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840962, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127840965, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841010, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841036, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841037, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841073, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841076, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841129, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841131, "dur": 29, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841163, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841164, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841191, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841193, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841220, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841221, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841245, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841270, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841296, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841329, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841356, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841381, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841383, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841411, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841436, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841467, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841498, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841529, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841530, "dur": 28, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841561, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841563, "dur": 33, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841599, "dur": 25, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841627, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841652, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841653, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841682, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841709, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841735, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841760, "dur": 77, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841839, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841840, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841871, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841873, "dur": 24, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841899, "dur": 26, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841928, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127841957, "dur": 123, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127842083, "dur": 34, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127842120, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127842125, "dur": 36, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127842163, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127842192, "dur": 1898, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844095, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844131, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844216, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844248, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844412, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844439, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844464, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844490, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844491, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844570, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127844592, "dur": 1302, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127845896, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127845929, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127845958, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127845987, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846007, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846161, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846189, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846271, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846296, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846321, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846346, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846367, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846525, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846553, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846577, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846652, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846674, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846695, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846723, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846749, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846771, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846793, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846815, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846836, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846867, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846890, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846912, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846943, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846965, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127846984, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847019, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847042, "dur": 72, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847115, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847137, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847164, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847183, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847248, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847269, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847307, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847328, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847377, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847397, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847435, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847456, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847479, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847515, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847543, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847565, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847587, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847609, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847629, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847650, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847673, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847694, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847731, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847754, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847774, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847794, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847822, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847846, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127847998, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848035, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848064, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848093, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848104, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848129, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848152, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848204, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848245, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848270, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848272, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848298, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848327, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848367, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848368, "dur": 63, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848434, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848462, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848488, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848574, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848593, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848615, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848638, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848724, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848754, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848804, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848824, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848827, "dur": 40, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848869, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848894, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848953, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127848978, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849228, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849255, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849296, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849330, "dur": 246, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849581, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849610, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849638, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849795, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849811, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849857, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127849878, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850172, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850204, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850269, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850302, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850329, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850440, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850474, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850498, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850666, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850690, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850841, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850861, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645127850864, "dur": 243248, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128094123, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128094127, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128094163, "dur": 29, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128094193, "dur": 2132, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096334, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096385, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096428, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096430, "dur": 107, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096542, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096576, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096578, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096615, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096646, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096678, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096708, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128096731, "dur": 723, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097459, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097497, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097499, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097534, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097608, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097635, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097665, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097734, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097770, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097811, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097837, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097875, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097932, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128097963, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098087, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098111, "dur": 600, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098717, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098751, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098897, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098923, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098925, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098959, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128098985, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099034, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099060, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099088, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099117, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099119, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099150, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099152, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099181, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099208, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099231, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099254, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099279, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099302, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099326, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099351, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099373, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099402, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099404, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099434, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099458, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099481, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099504, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099525, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099549, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099576, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099602, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099625, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099649, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099673, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099699, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099723, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099747, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099771, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099793, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099819, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099846, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099869, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099946, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128099977, "dur": 107, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100087, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100124, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100126, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100160, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100205, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100246, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100274, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100276, "dur": 93, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100373, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128100410, "dur": 27529, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128127950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128127953, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128127984, "dur": 47, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128128033, "dur": 63198, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128191241, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128191249, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128191279, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128191282, "dur": 204270, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128395563, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128395567, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128395602, "dur": 24, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128395627, "dur": 5183, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128400818, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128400821, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128400886, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128400889, "dur": 1920, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128402816, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128402819, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128402866, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128402897, "dur": 57542, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128460452, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128460456, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128460521, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128460526, "dur": 733, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128461268, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128461271, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128461329, "dur": 35, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128461366, "dur": 542, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128461912, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 115964116992, "ts": 1754645128461946, "dur": 6682, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 1067, "ts": 1754645128469165, "dur": 1121, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18540, "tid": 111669149696, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18540, "tid": 111669149696, "ts": 1754645127697666, "dur": 34629, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18540, "tid": 111669149696, "ts": 1754645127732296, "dur": 86025, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18540, "tid": 111669149696, "ts": 1754645127818323, "dur": 45, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18540, "tid": 1067, "ts": 1754645128470289, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18540, "tid": 107374182400, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18540, "tid": 107374182400, "ts": 1754645127600035, "dur": 868641, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18540, "tid": 107374182400, "ts": 1754645127600114, "dur": 97515, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18540, "tid": 107374182400, "ts": 1754645128468679, "dur": 36, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18540, "tid": 107374182400, "ts": 1754645128468690, "dur": 11, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18540, "tid": 1067, "ts": 1754645128470298, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754645127819084, "dur": 1430, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645127820526, "dur": 882, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645127821527, "dur": 56, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754645127821583, "dur": 341, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645127822289, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2933F28E75319B3A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754645127821939, "dur": 19290, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645127841241, "dur": 620860, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645128462103, "dur": 116, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645128462219, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645128462271, "dur": 112, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645128462581, "dur": 69, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645128462688, "dur": 1255, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754645127822128, "dur": 19123, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127841259, "dur": 2259, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127843519, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127843689, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127843868, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127844039, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127844247, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127844709, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127844961, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127845129, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127845352, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127845535, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127845761, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127845927, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127846119, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127846321, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127846497, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127846715, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127847006, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645127847143, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127847438, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645127847964, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645127848093, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645127848260, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645127848494, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645127848462, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645127849011, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645127849105, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645127849368, "dur": 1630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645127850998, "dur": 245149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645128096148, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645128097195, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645128097403, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645128098551, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645128099486, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645128098540, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645128099710, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645128099819, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645128101033, "dur": 361120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127822399, "dur": 18935, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127841405, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_504B268BBB0FC6D0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645127841511, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_9B44DAA96A1B4B8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645127841674, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127841888, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_99AE285BDA61FA2E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645127842127, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754645127842551, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754645127842940, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127843294, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127843493, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127843701, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127843968, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127844187, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127844367, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127844833, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127845061, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127845264, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127845518, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127845802, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127845982, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127846141, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127846350, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127846579, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127846701, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127846981, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645127847310, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645127847063, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645127847667, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645127848114, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645127847819, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645127848257, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127848340, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127848577, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127848669, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127848726, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127848990, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127849155, "dur": 1827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645127850982, "dur": 244919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645128095910, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645128097128, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645128098290, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645128098628, "dur": 691, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645128098420, "dur": 2121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645128100586, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645128100909, "dur": 361198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127822193, "dur": 19086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127841447, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41DA9226DA4902FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127841570, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_A00BE41311F50426.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127841724, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127841829, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_12BA7C2C14719C45.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127842135, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645127842367, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645127842542, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645127842665, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1064914946794821560.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754645127842797, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754645127842942, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127843298, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127843507, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127843654, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127844138, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127844321, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127844837, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127845036, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127845259, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127845473, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127845667, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127845941, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127846159, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127846367, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127846563, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127846715, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127847004, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127847097, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645127847498, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127847601, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127847741, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127847901, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645127848466, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645127848640, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645127848994, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127849054, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127849160, "dur": 1818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645127851039, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645127851209, "dur": 244726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645128095937, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645128097466, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645128097104, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645128098188, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645128098284, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645128099392, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645128099764, "dur": 1215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645128101020, "dur": 361092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127822184, "dur": 19080, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127841507, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_D66E5D73A81B547E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645127841660, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127841888, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645127842091, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754645127842195, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645127842365, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754645127842521, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754645127842850, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127843191, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127843393, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127843649, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127843974, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127844172, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127844347, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127844915, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127845116, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127845329, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127845495, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127845743, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127845943, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127846161, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127846450, "dur": 78, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127846528, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127846706, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127847000, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645127847120, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645127847476, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127847564, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645127847674, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645127848266, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645127848724, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127848989, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127849154, "dur": 860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127850073, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645127850360, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645127851004, "dur": 245028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645128096033, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645128098193, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645128098288, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645128097142, "dur": 1322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645128098515, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645128100357, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645128099733, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645128101030, "dur": 361100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127822247, "dur": 19042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127841296, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645127841453, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_711C290004B55458.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645127841675, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1754645127841665, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645127841757, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645127842027, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754645127842278, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754645127842672, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754645127842772, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3867973322336625480.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754645127842858, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127843284, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127843463, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127843647, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127843821, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127843980, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127844276, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127844722, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127844911, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127845111, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127845341, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127845510, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127845700, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127845914, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127846078, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127846250, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127846496, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127846699, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127846983, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645127847049, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127847630, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645127847339, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645127847967, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127848049, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645127848163, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645127849021, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127849231, "dur": 1759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645127850990, "dur": 244906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645128095898, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645128097088, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645128098194, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645128098288, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645128098385, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645128097182, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645128098716, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645128099785, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645128099861, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645128100591, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645128100904, "dur": 361193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127822335, "dur": 18965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127841527, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4190EB41F081C965.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645127841667, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127841891, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754645127842094, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754645127842228, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645127842782, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645127843258, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645127844394, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754645127842302, "dur": 2530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645127844871, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1754645127844869, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645127844939, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127845003, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127845226, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127845473, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127845658, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127845865, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127846053, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127846220, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127846505, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127846704, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127846997, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645127847083, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645127847467, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645127847625, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645127847797, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645127848177, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645127848723, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127848820, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127848995, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127849156, "dur": 1827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645127850983, "dur": 244916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645128095900, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645128097408, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645128098551, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645128097486, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645128098745, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645128098872, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645128100001, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645128100453, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645128100741, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645128101168, "dur": 360976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127822370, "dur": 18945, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127841327, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127841680, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645127841678, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127841783, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127841927, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754645127842129, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754645127842365, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754645127842522, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754645127842929, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127843266, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127843416, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127843588, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127843760, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127844026, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127844203, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127844690, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127844931, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127845119, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127845387, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127845553, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127845782, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127845938, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127846112, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127846358, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127846554, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127846710, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127847007, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127847113, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127847585, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127847746, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127847807, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127848272, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127848419, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127848842, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127848984, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645127849154, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127849243, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127849667, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127849735, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127850634, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127850990, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645127851063, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127851273, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127851450, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645127851642, "dur": 277044, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645128130007, "dur": 60443, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645128130006, "dur": 61207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645128191858, "dur": 96, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645128191998, "dur": 204310, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645128401368, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754645128401366, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754645128401560, "dur": 2032, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754645128403594, "dur": 58537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127822455, "dur": 18897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127841498, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E0E4ACDF62B3A25F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645127841666, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127841810, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842101, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842269, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842369, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842616, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842690, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842777, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754645127842845, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127843187, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127843410, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127843628, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127843808, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127843977, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127844179, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127844388, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127844880, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127845053, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127845302, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127845520, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127845698, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127845915, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127846093, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127846251, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127846414, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127846503, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127846709, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127846997, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645127847093, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645127847417, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127847558, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645127847707, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645127847700, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645127848168, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127848295, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645127848506, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645127848912, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127848992, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127849157, "dur": 1823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645127850981, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645127851070, "dur": 245046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128096118, "dur": 1203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645128097321, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128097452, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645128098541, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128098602, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645128099918, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128100106, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128100361, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Tutorials.Core.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645128100360, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Tutorials.Core.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645128100439, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128100600, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645128101027, "dur": 361107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127822495, "dur": 18868, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127841450, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127841677, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1754645127841666, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127841884, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754645127841977, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754645127842075, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754645127842240, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754645127842498, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754645127842843, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127843226, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127843395, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127843636, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127843824, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127844174, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127844335, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127844809, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127844974, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127845172, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127845400, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127845604, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127845813, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127845979, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127846155, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127846361, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127846543, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127846719, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127846985, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127847071, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645127847420, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127847684, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645127848163, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127848299, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127848427, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127848508, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645127848894, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127849006, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127849153, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127849246, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645127849514, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127849583, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645127850010, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645127850085, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645127850406, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645127851005, "dur": 244962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645128095977, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645128097101, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645128098237, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645128099380, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645128099680, "dur": 1263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645128101021, "dur": 361094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127822554, "dur": 18821, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127841428, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645127841428, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127841497, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127841622, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_20F5B884F62AB761.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127841788, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127842067, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754645127842345, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754645127842610, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754645127842765, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754645127842854, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127843136, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127843466, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127843650, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127843841, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127844014, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127844275, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127844755, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127844911, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127845065, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127845261, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127845487, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127845656, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127845847, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127846041, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127846239, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127846522, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127846705, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127846991, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127847106, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645127847423, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127847545, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645127847654, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127847802, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645127848486, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127848682, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127848785, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127848894, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645127849229, "dur": 1752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645127850981, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645127851082, "dur": 244840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645128095924, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645128097403, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645128097319, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645128098597, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645128099641, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645128099964, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645128100144, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645128100419, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645128100630, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645128101015, "dur": 361093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127822596, "dur": 18790, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127841453, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645127841665, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127841782, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645127841993, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645127842270, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754645127842400, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645127842718, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645127842882, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127843303, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127843478, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127843716, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127843872, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127844034, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127844286, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127844788, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127844950, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127845153, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127845392, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127845566, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127845830, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127846021, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127846229, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127846433, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127846498, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127846698, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127846980, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645127847052, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645127848186, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127848270, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645127848445, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645127849055, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127849158, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645127849219, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645127849404, "dur": 1593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645127850997, "dur": 244948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645128095962, "dur": 1104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645128097110, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645128098192, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645128098627, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-handle-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645128098309, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645128099618, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645128099743, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645128100892, "dur": 361206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127822637, "dur": 18759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127841558, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1703D820AF2EEFB1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645127841631, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_EF7BA45317B31323.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645127841782, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754645127842021, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754645127842298, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754645127842486, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754645127842878, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127843263, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127843417, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127843688, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127843846, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127844023, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127844235, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127844736, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127844925, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127845093, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127845346, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127845511, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127845756, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127845996, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127846165, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127846349, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127846513, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127846703, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127846982, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645127847082, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645127847459, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645127847575, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645127848816, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127848892, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645127848978, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645127849248, "dur": 1745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645127850993, "dur": 244911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645128095939, "dur": 1109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645128097097, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645128099487, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645128098257, "dur": 1680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireEditorAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645128100214, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645128100568, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645128100733, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645128101016, "dur": 361113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127822673, "dur": 18735, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127841413, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645127841673, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127841777, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645127841974, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645127842261, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754645127842531, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754645127842698, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645127842758, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127842916, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127843314, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127843464, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127843675, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127843832, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127844042, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127844248, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127844674, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127844913, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127845105, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127845267, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127845502, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127845750, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127845918, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127846106, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127846321, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127846501, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127846706, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127847003, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645127847138, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645127847518, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127847755, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645127848461, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645127848628, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645127848954, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127849016, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127849159, "dur": 1832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645127850991, "dur": 244903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645128095896, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645128097083, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645128097175, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645128098551, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645128098432, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645128099694, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645128099893, "dur": 472, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645128099835, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645128101162, "dur": 360964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645127822714, "dur": 18705, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645127841476, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127841631, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_34719EFEEA08287A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127841696, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645127841695, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127841780, "dur": 441, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127842925, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645127843142, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645127844393, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754645127842283, "dur": 2886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645127845217, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127846426, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\TestListBuilder\\RenderingOptions.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754645127845354, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645127846768, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645127846996, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127847106, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645127847308, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645127847882, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645127848242, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645127848548, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645127848602, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645127848976, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1754645127849608, "dur": 245270, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1754645128095905, "dur": 1157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645128097096, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645128097186, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645128098260, "dur": 1068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/OpenFracture.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645128099329, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645128099485, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645128100745, "dur": 300630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645128401376, "dur": 59730, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645128401376, "dur": 59732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645128461137, "dur": 907, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645127822756, "dur": 18673, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127841516, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_75656E38889B442A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645127841727, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127841778, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754645127842117, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754645127842199, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754645127842360, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754645127842543, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754645127842860, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12845725702279794616.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754645127842937, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127843316, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127843455, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127843717, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127843885, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127844087, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127844292, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127844878, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127845084, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127845263, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127845559, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127845760, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127845956, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127846191, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127846435, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127846490, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127846698, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127846985, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645127847074, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645127847464, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645127847582, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645127847688, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645127848161, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127848258, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645127848422, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645127849108, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127849158, "dur": 1841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645127850999, "dur": 244983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645128095985, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645128097264, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645128097459, "dur": 1124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645128098618, "dur": 1118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645128099766, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645128100886, "dur": 361218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127822795, "dur": 18644, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127841519, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_B76D6751422DDE5E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645127841664, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127841890, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_8573C5AEBE7FAC0E.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645127842074, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754645127842141, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754645127842325, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754645127842447, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754645127842605, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754645127842881, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127843267, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127843413, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127843617, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127843808, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127843970, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127844676, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127844839, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127845055, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127845263, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127845459, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127845651, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127845860, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127846037, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127846194, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127846385, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127846535, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127846711, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127847002, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645127847117, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645127847572, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645127847722, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645127848149, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127848234, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645127848771, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127849002, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127849155, "dur": 1829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645127850985, "dur": 244957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645128095986, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645128095944, "dur": 1413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645128097399, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645128098600, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645128098885, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645128100267, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645128100415, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645128100615, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645128101024, "dur": 361103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645128467736, "dur": 1294, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18540, "tid": 1067, "ts": 1754645128470338, "dur": 530, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18540, "tid": 1067, "ts": 1754645128474344, "dur": 642, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18540, "tid": 1067, "ts": 1754645128469141, "dur": 5877, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}