{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18540, "tid": 1055, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18540, "tid": 1055, "ts": 1754645982826832, "dur": 303, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754645982828951, "dur": 374, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18540, "tid": 1, "ts": 1754645980982245, "dur": 3139, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754645980985388, "dur": 18453, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754645981003848, "dur": 19669, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754645982829329, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 18540, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645980981263, "dur": 109738, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981091002, "dur": 1730405, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981091821, "dur": 820, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981092648, "dur": 629, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093279, "dur": 108, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093388, "dur": 206, "ph": "X", "name": "ProcessMessages 12174", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093596, "dur": 46, "ph": "X", "name": "ReadAsync 12174", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093645, "dur": 1, "ph": "X", "name": "ProcessMessages 4068", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093647, "dur": 29, "ph": "X", "name": "ReadAsync 4068", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093679, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093705, "dur": 24, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093731, "dur": 25, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093757, "dur": 22, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093781, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093805, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093830, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093854, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093877, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093900, "dur": 27, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093929, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093951, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093975, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981093998, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094021, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094044, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094063, "dur": 23, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094088, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094112, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094135, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094159, "dur": 25, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094186, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094188, "dur": 34, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094224, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094226, "dur": 30, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094258, "dur": 23, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094283, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094306, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094328, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094350, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094373, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094395, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094418, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094442, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094463, "dur": 24, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094489, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094518, "dur": 21, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094541, "dur": 21, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094563, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094586, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094610, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094632, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094656, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094678, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094701, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094723, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094745, "dur": 22, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094768, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094791, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094820, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094821, "dur": 32, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094856, "dur": 29, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094889, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094890, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094920, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094945, "dur": 23, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094970, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981094994, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095019, "dur": 23, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095043, "dur": 22, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095067, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095088, "dur": 18, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095107, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095131, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095155, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095178, "dur": 23, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095203, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095226, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095245, "dur": 22, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095268, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095290, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095313, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095336, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095359, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095382, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095404, "dur": 21, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095426, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095486, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095510, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095534, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095557, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095578, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095600, "dur": 22, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095623, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095647, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095670, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095695, "dur": 33, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095731, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095733, "dur": 56, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095795, "dur": 2, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095798, "dur": 58, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095859, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095860, "dur": 46, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095910, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095912, "dur": 35, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095948, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095981, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981095983, "dur": 39, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096024, "dur": 23, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096048, "dur": 20, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096070, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096093, "dur": 26, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096121, "dur": 22, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096144, "dur": 24, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096171, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096191, "dur": 21, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096215, "dur": 28, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096246, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096280, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096304, "dur": 19, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096324, "dur": 21, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096346, "dur": 18, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096366, "dur": 20, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096387, "dur": 21, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096410, "dur": 22, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096435, "dur": 37, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096474, "dur": 28, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096503, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096505, "dur": 27, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096533, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096556, "dur": 29, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096588, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096620, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096643, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096666, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096685, "dur": 19, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096706, "dur": 23, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096731, "dur": 21, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096754, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096774, "dur": 20, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096796, "dur": 17, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096814, "dur": 22, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096837, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096860, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096882, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096903, "dur": 20, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096924, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096948, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096970, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981096993, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097015, "dur": 31, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097051, "dur": 28, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097081, "dur": 30, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097115, "dur": 31, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097148, "dur": 27, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097178, "dur": 30, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097210, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097211, "dur": 33, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097247, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097249, "dur": 68, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097319, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097342, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097343, "dur": 34, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097380, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097381, "dur": 34, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097418, "dur": 25, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097445, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097468, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097490, "dur": 33, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097525, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097553, "dur": 27, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097582, "dur": 39, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097624, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097626, "dur": 40, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097668, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097704, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097706, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097740, "dur": 33, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097776, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097777, "dur": 44, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097824, "dur": 31, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097857, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097859, "dur": 30, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097890, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097918, "dur": 28, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097947, "dur": 31, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097980, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981097982, "dur": 31, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098017, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098019, "dur": 26, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098047, "dur": 27, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098076, "dur": 28, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098106, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098128, "dur": 24, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098154, "dur": 24, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098180, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098202, "dur": 21, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098224, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098253, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098255, "dur": 41, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098299, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098301, "dur": 35, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098340, "dur": 29, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098372, "dur": 34, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098408, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098437, "dur": 21, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098461, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098462, "dur": 28, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098492, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098517, "dur": 33, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098554, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098592, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098618, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098642, "dur": 27, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098672, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098674, "dur": 28, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098703, "dur": 29, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098736, "dur": 32, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098770, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098772, "dur": 29, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098802, "dur": 19, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098823, "dur": 18, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098842, "dur": 29, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098874, "dur": 35, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098911, "dur": 24, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098939, "dur": 25, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098967, "dur": 29, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981098999, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099001, "dur": 47, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099049, "dur": 28, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099079, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099081, "dur": 36, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099119, "dur": 23, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099144, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099166, "dur": 30, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099198, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099220, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099242, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099265, "dur": 25, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099292, "dur": 31, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099325, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099353, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099355, "dur": 31, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099388, "dur": 26, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099416, "dur": 19, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099436, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099458, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099481, "dur": 23, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099505, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099526, "dur": 27, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099554, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099577, "dur": 29, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099608, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099610, "dur": 33, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099647, "dur": 33, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099682, "dur": 30, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099716, "dur": 31, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099748, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099771, "dur": 21, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099793, "dur": 30, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099825, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099827, "dur": 38, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099867, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099869, "dur": 38, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099912, "dur": 38, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099951, "dur": 23, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099976, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981099997, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100021, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100046, "dur": 25, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100072, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100097, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100120, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100143, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100166, "dur": 23, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100190, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100213, "dur": 22, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100236, "dur": 27, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100265, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100267, "dur": 28, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100296, "dur": 25, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100323, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100325, "dur": 31, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100358, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100382, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100405, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100431, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100455, "dur": 26, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100484, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100485, "dur": 29, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100516, "dur": 23, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100540, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100567, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100591, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100616, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100641, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100665, "dur": 132, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100801, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100834, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100858, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100883, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100907, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100932, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100954, "dur": 21, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981100977, "dur": 22, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101001, "dur": 15, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101017, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101039, "dur": 22, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101062, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101086, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101111, "dur": 28, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101141, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101165, "dur": 24, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101190, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101215, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101240, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101269, "dur": 39, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101310, "dur": 23, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101334, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101356, "dur": 27, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101385, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101410, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101432, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101454, "dur": 25, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101481, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101503, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101525, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101549, "dur": 26, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101576, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101601, "dur": 20, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101622, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101649, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101681, "dur": 22, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101704, "dur": 22, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101728, "dur": 21, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101751, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101773, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101795, "dur": 23, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101820, "dur": 94, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101916, "dur": 1, "ph": "X", "name": "ProcessMessages 1351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101917, "dur": 21, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101940, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101962, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981101985, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102008, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102031, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102055, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102077, "dur": 21, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102100, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102119, "dur": 25, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102148, "dur": 29, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102180, "dur": 23, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102204, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102229, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102251, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102274, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102295, "dur": 20, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102317, "dur": 24, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102343, "dur": 32, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102378, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102379, "dur": 41, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102423, "dur": 22, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102446, "dur": 23, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102471, "dur": 34, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102507, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102508, "dur": 37, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102548, "dur": 18, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102568, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102591, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102618, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102643, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102668, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102693, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102718, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102742, "dur": 19, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102762, "dur": 21, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102784, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102807, "dur": 23, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102832, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102856, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102879, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102902, "dur": 25, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102928, "dur": 24, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102955, "dur": 24, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981102981, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103005, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103027, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103043, "dur": 20, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103065, "dur": 22, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103088, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103111, "dur": 23, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103136, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103159, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103179, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103202, "dur": 27, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103241, "dur": 28, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103270, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103271, "dur": 26, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103299, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103329, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103331, "dur": 38, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103371, "dur": 33, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103405, "dur": 29, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103438, "dur": 37, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103477, "dur": 37, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103518, "dur": 32, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103552, "dur": 31, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103586, "dur": 26, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103614, "dur": 25, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103642, "dur": 31, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103675, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103677, "dur": 26, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103705, "dur": 28, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103734, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103760, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103761, "dur": 27, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103792, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103794, "dur": 89, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103884, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103930, "dur": 1, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103932, "dur": 49, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103984, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981103985, "dur": 48, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104036, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104038, "dur": 37, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104076, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104103, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104126, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104156, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104200, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104202, "dur": 37, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104240, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104241, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104265, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104291, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104353, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104381, "dur": 53, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104437, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104439, "dur": 31, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104471, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104498, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104522, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104544, "dur": 24, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104571, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104605, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104654, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104682, "dur": 22, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104705, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104729, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104754, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104777, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104798, "dur": 29, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104829, "dur": 29, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104860, "dur": 32, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104894, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104896, "dur": 27, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104926, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104957, "dur": 32, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981104990, "dur": 22, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105014, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105031, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105098, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105138, "dur": 24, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105164, "dur": 20, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105186, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105215, "dur": 25, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105241, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105264, "dur": 23, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105290, "dur": 24, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105316, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105338, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105383, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105405, "dur": 23, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105430, "dur": 103, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105535, "dur": 25, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105561, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105583, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105606, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105629, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105650, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105682, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105706, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105728, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105750, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105769, "dur": 44, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105815, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105837, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105860, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105882, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105908, "dur": 41, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105952, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105974, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981105996, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106019, "dur": 26, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106047, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106049, "dur": 36, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106086, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106109, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106110, "dur": 30, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106141, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106166, "dur": 19, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106186, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106228, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106251, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106274, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106299, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106320, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106366, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106390, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106414, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106435, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106454, "dur": 18, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106474, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106507, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106531, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106554, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106576, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106598, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106621, "dur": 35, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106657, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106681, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106703, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106725, "dur": 21, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106748, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106768, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106796, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106822, "dur": 28, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106853, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106855, "dur": 28, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106885, "dur": 49, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106938, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106968, "dur": 26, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981106997, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107021, "dur": 35, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107057, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107084, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107106, "dur": 32, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107140, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107142, "dur": 28, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107172, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107196, "dur": 21, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107219, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107242, "dur": 18, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107262, "dur": 43, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107306, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107338, "dur": 31, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107371, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107391, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107413, "dur": 45, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107459, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107483, "dur": 24, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107509, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107528, "dur": 18, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107547, "dur": 53, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107603, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107628, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107650, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107675, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107696, "dur": 31, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107728, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107750, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107772, "dur": 19, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107793, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107817, "dur": 44, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107864, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107899, "dur": 29, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107930, "dur": 29, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981107961, "dur": 36, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108000, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108030, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108053, "dur": 31, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108086, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108107, "dur": 29, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108138, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108164, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108187, "dur": 20, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108209, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108260, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108295, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108326, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108327, "dur": 36, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108367, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108369, "dur": 34, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108404, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108430, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108454, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108481, "dur": 29, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108513, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108541, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108574, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108602, "dur": 29, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108632, "dur": 19, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108653, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108672, "dur": 32, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108706, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108729, "dur": 30, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108763, "dur": 30, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108794, "dur": 28, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108826, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108828, "dur": 39, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108869, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108871, "dur": 26, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108900, "dur": 21, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108923, "dur": 25, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108951, "dur": 25, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981108977, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109003, "dur": 25, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109029, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109050, "dur": 49, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109102, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109131, "dur": 28, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109162, "dur": 23, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109187, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109212, "dur": 27, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109242, "dur": 28, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109272, "dur": 23, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109297, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109319, "dur": 18, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109339, "dur": 44, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109387, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109422, "dur": 32, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109457, "dur": 27, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109485, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109486, "dur": 26, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109514, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109534, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109553, "dur": 21, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109576, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109596, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109621, "dur": 43, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109666, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109689, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109708, "dur": 31, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109740, "dur": 28, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109771, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109772, "dur": 55, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109831, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109868, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109869, "dur": 38, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109909, "dur": 26, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109939, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981109988, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110023, "dur": 27, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110053, "dur": 32, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110086, "dur": 52, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110139, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110170, "dur": 29, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110203, "dur": 30, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110235, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110256, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110307, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110335, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110357, "dur": 23, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110381, "dur": 24, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110406, "dur": 52, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110461, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110485, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110507, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110509, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110531, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110552, "dur": 41, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110594, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110617, "dur": 23, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110642, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110664, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110684, "dur": 42, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110727, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110748, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110774, "dur": 155, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110932, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110934, "dur": 40, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110977, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981110979, "dur": 33, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111015, "dur": 29, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111047, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111066, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111089, "dur": 29, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111120, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111122, "dur": 32, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111181, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111204, "dur": 30, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111238, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111239, "dur": 36, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111279, "dur": 30, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111311, "dur": 29, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111342, "dur": 25, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111368, "dur": 42, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111412, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111441, "dur": 22, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111464, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111487, "dur": 17, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111505, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111539, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111566, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111587, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111611, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111633, "dur": 42, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111676, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111699, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111721, "dur": 14, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111736, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111755, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111805, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111828, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111845, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111865, "dur": 21, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111887, "dur": 14, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111902, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111924, "dur": 21, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111946, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111968, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981111990, "dur": 18, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112010, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112034, "dur": 44, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112080, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112107, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112129, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112148, "dur": 17, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112166, "dur": 42, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112210, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112233, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112256, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112273, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112296, "dur": 17, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112314, "dur": 37, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112353, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112375, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112398, "dur": 44, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112444, "dur": 29, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112474, "dur": 25, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112502, "dur": 25, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112529, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112551, "dur": 23, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112576, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112597, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112621, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112680, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112713, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112736, "dur": 22, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112759, "dur": 37, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112798, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112821, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112843, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112865, "dur": 18, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112884, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112904, "dur": 48, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981112955, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113006, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113025, "dur": 14, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113041, "dur": 54, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113097, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113119, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113143, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113165, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113183, "dur": 30, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113215, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113239, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113262, "dur": 1, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113263, "dur": 20, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113284, "dur": 42, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113328, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113352, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113376, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113398, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113420, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113442, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113465, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113489, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113511, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113532, "dur": 21, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113554, "dur": 41, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113596, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113622, "dur": 111, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113735, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981113766, "dur": 368, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114138, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114196, "dur": 5, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114202, "dur": 32, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114236, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114262, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114285, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114318, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114339, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114363, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114387, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114415, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114456, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114459, "dur": 41, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114501, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114504, "dur": 43, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114550, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114553, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114595, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114597, "dur": 35, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114634, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114635, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114666, "dur": 35, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114703, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114708, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114745, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114747, "dur": 35, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114783, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114785, "dur": 26, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114812, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114814, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114844, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114846, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114877, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114879, "dur": 29, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981114910, "dur": 151, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115064, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115103, "dur": 2, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115106, "dur": 30, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115140, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115170, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115172, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115211, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115213, "dur": 30, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115247, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115248, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115280, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115310, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115312, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115344, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115374, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115376, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115408, "dur": 21, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115432, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115454, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981115487, "dur": 1962, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981117456, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981117491, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981117492, "dur": 265, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981117761, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981117790, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981117882, "dur": 152, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981118039, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981118114, "dur": 1312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119429, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119462, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119496, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119498, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119532, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119711, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119738, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119864, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119894, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119927, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119952, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119975, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981119997, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120180, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120202, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120224, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120248, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120277, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120306, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120328, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120349, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120370, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120391, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120416, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120441, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120465, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120486, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120516, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120540, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120571, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120592, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120621, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120645, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120666, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120686, "dur": 224, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120912, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120942, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120963, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981120966, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121012, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121045, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121046, "dur": 40, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121089, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121121, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121150, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121207, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121239, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121262, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121288, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121290, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121325, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121347, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121393, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121414, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121445, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121476, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121498, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121524, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121547, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121572, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121595, "dur": 75, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121674, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121696, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121718, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121739, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121767, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121798, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121845, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121877, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121915, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981121939, "dur": 108, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122052, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122082, "dur": 480, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122563, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122610, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122612, "dur": 199, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122816, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122841, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122861, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122891, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981122926, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123041, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123063, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123284, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123320, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123380, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123403, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123430, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123564, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123587, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123613, "dur": 372, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981123987, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981124018, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981124021, "dur": 231071, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981355103, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981355107, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981355145, "dur": 2010, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357161, "dur": 497, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357667, "dur": 163, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357834, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357836, "dur": 47, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357887, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981357889, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358029, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358056, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358058, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358295, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358321, "dur": 396, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358721, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358753, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358783, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358879, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358915, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358963, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981358994, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359044, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359083, "dur": 88, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359176, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359211, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359278, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359311, "dur": 409, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359722, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359749, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359751, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359789, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359820, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359849, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359879, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359916, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981359945, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360076, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360115, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360290, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360321, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360360, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360391, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360426, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360458, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360491, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360493, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360529, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360551, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360573, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360597, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360619, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360641, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360685, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360707, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360729, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360766, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360792, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360815, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360854, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360882, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360909, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360953, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981360983, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361012, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361036, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361072, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361109, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361138, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361165, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361188, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361213, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361239, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361262, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361287, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361325, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361351, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361380, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361405, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361431, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361494, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361523, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361545, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361569, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361634, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361659, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361682, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361698, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361724, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361748, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361773, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645981361797, "dur": 1096161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982457968, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982457973, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982458008, "dur": 3143, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982461155, "dur": 3577, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982464740, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982464743, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982464805, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982464808, "dur": 60913, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982525732, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982525737, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982525766, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982525769, "dur": 199912, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982725693, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982725698, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982725762, "dur": 4077, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982729842, "dur": 13331, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982743179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982743182, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982743209, "dur": 23, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982743232, "dur": 4247, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982747484, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982747533, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982747538, "dur": 1524, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982749066, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982749092, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982749107, "dur": 63489, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982812608, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982812613, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982812707, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982812713, "dur": 668, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982813386, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982813438, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982813467, "dur": 1648, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982815119, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982815147, "dur": 234, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645982815383, "dur": 5968, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754645982829337, "dur": 842, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18540, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18540, "tid": 8589934592, "ts": 1754645980979776, "dur": 43893, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18540, "tid": 8589934592, "ts": 1754645981023673, "dur": 67318, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18540, "tid": 8589934592, "ts": 1754645981090993, "dur": 1371, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754645982830180, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18540, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645980882700, "dur": 1939459, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645980884870, "dur": 91829, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645982822173, "dur": 2575, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645982823966, "dur": 67, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645982824799, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754645982830191, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754645981090510, "dur": 1402, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645981091924, "dur": 811, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645981092846, "dur": 56, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754645981092902, "dur": 340, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645981094087, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_B76D6751422DDE5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754645981096639, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754645981096952, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754645981099581, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754645981111579, "dur": 142, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2349487485701900628.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754645981093268, "dur": 21134, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645981114414, "dur": 1699796, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645982814212, "dur": 85, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645982814298, "dur": 205, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645982815900, "dur": 1232, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754645981093572, "dur": 20883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981114591, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114590, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_188EDD6374EAFAAD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114670, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114669, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114839, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_706C4E83917515AE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114895, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114893, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981114985, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981115059, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754645981115377, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754645981115545, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754645981115701, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754645981115859, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754645981116234, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981116781, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981116981, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981117180, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981117352, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981117525, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981117693, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981118268, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981118446, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981118618, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981118802, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981118984, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981119187, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981119395, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981119621, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981119815, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981119886, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981120087, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981120248, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981120553, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981120660, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981120744, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981121192, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981121310, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981121467, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981121947, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981122131, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981122278, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981122620, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981122709, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981122958, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981123260, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645981123337, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981123593, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981124113, "dur": 233202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981357317, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981358583, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981358475, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981359638, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/OpenFracture.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981360593, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645981360769, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981361087, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981361625, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645981360703, "dur": 1832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645981362567, "dur": 1451667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981093602, "dur": 20864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981114474, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645981114586, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981114650, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645981114931, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981114993, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754645981115369, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754645981115556, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754645981115882, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754645981116191, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981116738, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981116949, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981117110, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981117304, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981117549, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981117718, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981118234, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981118464, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981118700, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981118888, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981119075, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981119253, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981119481, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981119690, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981119892, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981120119, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981120243, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981120541, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645981120659, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645981121013, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645981120720, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981121156, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981121259, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981121717, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.31\\Rider\\Editor\\ProjectGeneration\\GUIDProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754645981121334, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981121811, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981121873, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981122266, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981122514, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981122639, "dur": 1464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981124103, "dur": 233195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645981357300, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981358425, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981358584, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645981358522, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981359547, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981360654, "dur": 1603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645981362293, "dur": 1365684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645982727978, "dur": 86236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981093703, "dur": 20843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981114574, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645981114560, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_D2B66E0C49661945.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981114883, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645981114882, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981114986, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3AB3AEE512D0C853.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981115140, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645981115368, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754645981115587, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645981115804, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645981115950, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754645981116205, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981116554, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981116746, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981116941, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981117102, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981117306, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981117622, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981118241, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981118440, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981118602, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981118789, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981118997, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981119229, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981119416, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981119619, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981119807, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981120068, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981120247, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981120550, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981120672, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981120997, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981121174, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981121636, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645981121323, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981121787, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981121996, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645981122322, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645981121916, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981122546, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645981122626, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645981122702, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981123047, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981123763, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981124167, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981124377, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645981124788, "dur": 1333929, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645982460673, "dur": 4358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645982460456, "dur": 4661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645982465475, "dur": 260989, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645982727971, "dur": 86273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981093611, "dur": 20867, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981114486, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_1DBF74AC9293BF07.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645981114592, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1754645981114567, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A3CA169B49A7C799.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645981114894, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981115084, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645981115164, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754645981115329, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645981115663, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754645981115857, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645981116179, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981116485, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981116641, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981116793, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981117136, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981117307, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981117485, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981117690, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981118250, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981118458, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981118702, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981118943, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981119120, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981119309, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981119511, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981119699, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981119887, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981120076, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981120249, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981120564, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645981120680, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645981121079, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981121183, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645981121288, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981121565, "dur": 298, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645981121417, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645981122381, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981122649, "dur": 1452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981124101, "dur": 233201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981357303, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645981358584, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645981358435, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645981359508, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645981361389, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645981360576, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645981362083, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981362195, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645981362452, "dur": 1451863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981093556, "dur": 20885, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981114577, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645981114576, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981114781, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_623E7535F4BD3DF5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981114887, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981115061, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_12BA7C2C14719C45.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981115374, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754645981115625, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754645981115865, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754645981116153, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/908057632503650206.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754645981116258, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981116883, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981117077, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981117271, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981117457, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981117634, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981117815, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981118375, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981118582, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981118766, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981118965, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981119160, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981119415, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981119600, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981119804, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981120040, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981120241, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981120543, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981120654, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981121019, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981121168, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981121372, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981121682, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981121920, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981122038, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981122100, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981122543, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981122642, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981124098, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645981124172, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981124354, "dur": 232950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981357307, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981358514, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645981358450, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981359693, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645981359830, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981362011, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645981361079, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645981362502, "dur": 1451777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981093523, "dur": 20903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981114434, "dur": 2796, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981117231, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981117419, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981117600, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981117816, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981118347, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981118537, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981118721, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981118950, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981119158, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981119348, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981119550, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981119760, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981120048, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981120238, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981120545, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645981120674, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645981121093, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645981121288, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645981121794, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Sequence\\Jog.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754645981121208, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645981122225, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981122371, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981122625, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981124111, "dur": 233182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981357295, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645981358584, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645981358901, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645981359789, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645981358452, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645981360005, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981361724, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645981360578, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645981361990, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981362198, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645981362505, "dur": 1451762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981093665, "dur": 20849, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981114540, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981114597, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1754645981114527, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_370C590F53957EFB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645981114691, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645981114912, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981114985, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754645981115132, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754645981115198, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754645981115546, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754645981115808, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754645981116112, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/61918516800488742.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754645981116187, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981116566, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981116799, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981116961, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981117144, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981117312, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981117583, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\ISnapping.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754645981117476, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981118286, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981118490, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981118647, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981118854, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981119043, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981119223, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981119447, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981119653, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981119897, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981120043, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981120239, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981120540, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645981120646, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645981121012, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981121149, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645981121258, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645981121717, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981121668, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645981122368, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981122640, "dur": 1469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981124109, "dur": 233203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981357313, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645981358392, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981358809, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645981359748, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981360770, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981359953, "dur": 1119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645981361073, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981361129, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\RayFireAssembly.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981361127, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RayFireAssembly.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981361377, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981361725, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\OpenFracture.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981361724, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/OpenFracture.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645981362157, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645981362336, "dur": 1451868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981093639, "dur": 20856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981114530, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645981114508, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645981114886, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981115060, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754645981115194, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754645981115617, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754645981116202, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981116725, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981116927, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981117102, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981117418, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981117673, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981118152, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981118391, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981118588, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981118806, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981119008, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981119219, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981119438, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981119651, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981119849, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981119913, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981120056, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981120248, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981120558, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645981120660, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645981120730, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981121013, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981121084, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645981121247, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645981121407, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981121883, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645981122158, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645981121791, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981122373, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981122631, "dur": 1471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981124102, "dur": 233198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981357302, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981358421, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981358584, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645981359535, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.16\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645981358524, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireEditorAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981360770, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645981359867, "dur": 1248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645981361221, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981361649, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981362153, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645981362307, "dur": 1385772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645982748081, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645982748081, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645982748261, "dur": 1598, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645982749862, "dur": 64344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981093686, "dur": 20843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981114584, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645981114582, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_BE454E773931EEAC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981114886, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645981114885, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981114994, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981115151, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981115973, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645981116243, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645981116538, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645981117821, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754645981118155, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754645981118253, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754645981115221, "dur": 3291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981118593, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981118663, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981119593, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineParser\\CommandLineOptionSet.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754645981118818, "dur": 1370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981120314, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981120551, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981120652, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981120772, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981121232, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645981121421, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981121710, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981121800, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1754645981122290, "dur": 148, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981122835, "dur": 233028, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1754645981357309, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981358585, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645981358427, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981359490, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981359563, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981360529, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981360598, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645981362201, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645981362537, "dur": 1451716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981093724, "dur": 20837, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981114581, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981114569, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_3335CD69E05AF253.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981114682, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981114681, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981114750, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_969BEA2E4A09BAD9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981114912, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981114911, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981115016, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981115286, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981115348, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981116241, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981116451, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981116549, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981116735, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981117331, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754645981117821, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Shadow.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754645981115592, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981118152, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981118258, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981118433, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981118600, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981118807, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981118988, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981119229, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981119472, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981119659, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981119870, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981119991, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981120044, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981120253, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981120545, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981120663, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981121022, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981120761, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981121205, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981121356, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981122065, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981122148, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981122254, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981122644, "dur": 1454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981124099, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645981124197, "dur": 233187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981357384, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981358584, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981358448, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981359543, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981360536, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981361026, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Html.Abstractions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981361937, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645981360626, "dur": 1509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645981362204, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645981362572, "dur": 1451653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981093755, "dur": 20818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981114587, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645981114580, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_ED51E50A679089CA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981114891, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981114974, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_539666F57CEC7FE9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981115218, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645981115446, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645981115844, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645981116217, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981116565, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981116769, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981116923, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981117195, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981117374, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981117588, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981117776, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981118320, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981118514, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981118930, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981119131, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981119336, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981119534, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981119703, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981120045, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981120240, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981120552, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981120698, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981121022, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Tutorials.Core.ref.dll_D3CDBE453963C686.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981121087, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981121211, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981121337, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981121742, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981122008, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645981122147, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981122267, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981122623, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981124111, "dur": 233198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981357310, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981358396, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981358585, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645981359396, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645981358460, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981359747, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981360700, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645981361531, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-stdio-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645981361830, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Memory.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645981361074, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645981362437, "dur": 1451763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981093782, "dur": 20809, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981114611, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645981114601, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_2C01C8125CB356D8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645981114824, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1703D820AF2EEFB1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645981114893, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645981114892, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645981115304, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754645981115366, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754645981115667, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754645981116044, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754645981116203, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981116637, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981116791, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981116980, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981117187, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981117366, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981117549, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981117729, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981118283, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981118466, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981118641, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981118850, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981119083, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981119293, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981119471, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981119752, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981120041, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981120237, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981120545, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645981120687, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981120990, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645981121782, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\UnityThreadWaiter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754645981121135, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981122239, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981122322, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645981122461, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981122704, "dur": 1404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981124108, "dur": 233206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981357315, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981358584, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645981359535, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645981358462, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981359763, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981359823, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981360754, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645981360852, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645981362329, "dur": 1451869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981093820, "dur": 20781, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981114613, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645981114608, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2933F28E75319B3A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645981114893, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981115085, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754645981115294, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645981115371, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754645981115531, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645981115861, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645981116045, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645981116148, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1177933628359149393.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645981116210, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981116720, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981116868, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981117105, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981117469, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981117767, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981118299, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981118513, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981118722, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981118934, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981119116, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981119296, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981119496, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981119710, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981119878, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981120050, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981120245, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981120539, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645981121289, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\Settings\\InputEditorUserSettings.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754645981120649, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645981121705, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645981121883, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645981121827, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645981122622, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645981122699, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645981122888, "dur": 1225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981124113, "dur": 233887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981358584, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645981358002, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645981359791, "dur": 1039, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645981359085, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645981361399, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981361762, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981362086, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645981362207, "dur": 1098256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645982460465, "dur": 64569, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645982460464, "dur": 65336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645982526372, "dur": 81, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645982526491, "dur": 217458, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645982748074, "dur": 65220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645982748073, "dur": 65223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645982813318, "dur": 833, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645981093847, "dur": 20768, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981114781, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3C3F89D87E20F844.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645981114900, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981114995, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754645981115205, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754645981115326, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754645981115554, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754645981115707, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754645981116196, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981116532, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981116847, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981117055, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981117249, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981117425, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981117636, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981117809, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981118430, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981118616, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981118801, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981119106, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981119316, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981119523, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981119719, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981120045, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981120248, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981120555, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645981120674, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645981120992, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981121125, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645981121269, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645981121442, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645981121819, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645981121993, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645981122583, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981122637, "dur": 1462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981124100, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645981124200, "dur": 234235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645981358584, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645981359540, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645981358436, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645981359853, "dur": 650, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645981360769, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645981361087, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645981361691, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.FileProviders.Composite.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645981360506, "dur": 1901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645981362462, "dur": 1451831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981093869, "dur": 20766, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981114672, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981114889, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981114888, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645981115135, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754645981115288, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754645981115450, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754645981115612, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1754645981115868, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1754645981116179, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981116520, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981116757, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981116943, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981117111, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981117570, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981117768, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981118327, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981118524, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981118717, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981118953, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981119174, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981119416, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981119618, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981119805, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981120052, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981120246, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981120547, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645981120657, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645981120709, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981121005, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981121105, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645981121226, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981121747, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981121643, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981122232, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981122379, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981122622, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981123269, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645981123345, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981123654, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981124107, "dur": 233200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981357308, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981358584, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981359291, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981359535, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981358440, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981359863, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981361026, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981361087, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645981360062, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645981361283, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981361645, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981361942, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981362162, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645981362458, "dur": 1451739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981093894, "dur": 20758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981114653, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645981114898, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981114986, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754645981115261, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754645981115559, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754645981115658, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754645981115856, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754645981116225, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981116600, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981116772, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981117138, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981117334, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981117587, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981118217, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981118435, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981118612, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981118836, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981119011, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981119240, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981119482, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981119684, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981119895, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981120078, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981120252, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981120557, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645981120686, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981121003, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981121222, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981121320, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645981121883, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.5\\Editor\\Filtering\\AssemblyFiltering.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754645981121473, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981122093, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645981122093, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645981122177, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645981122311, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981122631, "dur": 1473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981124105, "dur": 233191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981357297, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981358430, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981358584, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645981358503, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981359583, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981360558, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645981359767, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981360845, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645981361260, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645981361103, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645981362525, "dur": 1451733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645982820365, "dur": 1349, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18540, "tid": 1055, "ts": 1754645982830444, "dur": 1236, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18540, "tid": 1055, "ts": 1754645982831775, "dur": 1731, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754645982828307, "dur": 5689, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}