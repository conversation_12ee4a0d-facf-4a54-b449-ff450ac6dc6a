{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18540, "tid": 1055, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18540, "tid": 1055, "ts": 1754646425147618, "dur": 286, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754646425149791, "dur": 436, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18540, "tid": 1, "ts": 1754646424072436, "dur": 3191, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754646424075630, "dur": 18465, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754646424094103, "dur": 18825, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754646425150230, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 18540, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424071323, "dur": 114111, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424185435, "dur": 956949, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424186208, "dur": 845, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424187059, "dur": 595, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424187657, "dur": 118, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424187777, "dur": 218, "ph": "X", "name": "ProcessMessages 12967", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424187996, "dur": 54, "ph": "X", "name": "ReadAsync 12967", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188053, "dur": 2, "ph": "X", "name": "ProcessMessages 4606", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188056, "dur": 27, "ph": "X", "name": "ReadAsync 4606", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188085, "dur": 24, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188112, "dur": 23, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188138, "dur": 24, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188163, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188187, "dur": 23, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188211, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188234, "dur": 23, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188258, "dur": 21, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188281, "dur": 28, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188312, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188314, "dur": 41, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188357, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188359, "dur": 32, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188392, "dur": 60, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188456, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188494, "dur": 22, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188518, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188540, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188566, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188590, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188616, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188639, "dur": 21, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188662, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188685, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188708, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188731, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188754, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188779, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188802, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188825, "dur": 15, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188841, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188865, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188888, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188911, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188934, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188957, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424188980, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189003, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189025, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189047, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189069, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189091, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189123, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189146, "dur": 22, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189169, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189193, "dur": 21, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189215, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189239, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189262, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189284, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189307, "dur": 24, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189333, "dur": 21, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189355, "dur": 18, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189374, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189397, "dur": 24, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189422, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189442, "dur": 24, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189467, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189489, "dur": 23, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189517, "dur": 104, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189622, "dur": 46, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189670, "dur": 1, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189672, "dur": 33, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189709, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189738, "dur": 31, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189773, "dur": 33, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189808, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189832, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189856, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189875, "dur": 61, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189937, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189962, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424189984, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190009, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190032, "dur": 21, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190055, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190071, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190094, "dur": 22, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190117, "dur": 22, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190141, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190164, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190188, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190208, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190231, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190253, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190276, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190299, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190323, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190339, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190364, "dur": 21, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190387, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190411, "dur": 35, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190449, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190450, "dur": 30, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190482, "dur": 22, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190505, "dur": 19, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190526, "dur": 24, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190551, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190576, "dur": 24, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190602, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190624, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190649, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190671, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190693, "dur": 22, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190716, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190739, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190763, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190786, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190808, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190832, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190852, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190875, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190898, "dur": 27, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190926, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190949, "dur": 24, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190974, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424190992, "dur": 17, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191011, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191034, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191057, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191080, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191103, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191126, "dur": 14, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191141, "dur": 18, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191160, "dur": 24, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191185, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191207, "dur": 21, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191230, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191253, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191268, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191288, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191307, "dur": 21, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191330, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191352, "dur": 26, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191380, "dur": 18, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191399, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191418, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191440, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191461, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191484, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191506, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191529, "dur": 39, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191569, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191591, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191614, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191633, "dur": 22, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191656, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191679, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191702, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191725, "dur": 28, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191755, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191776, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191795, "dur": 20, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191816, "dur": 105, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191923, "dur": 30, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191954, "dur": 21, "ph": "X", "name": "ReadAsync 1634", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424191977, "dur": 24, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192002, "dur": 23, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192026, "dur": 24, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192051, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192075, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192093, "dur": 21, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192116, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192138, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192160, "dur": 14, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192176, "dur": 18, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192196, "dur": 15, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192212, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192234, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192254, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192278, "dur": 21, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192301, "dur": 51, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192353, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192376, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192398, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192420, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192438, "dur": 21, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192460, "dur": 23, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192485, "dur": 14, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192500, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192523, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192544, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192568, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192587, "dur": 17, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192605, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192628, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192653, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192675, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192698, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192724, "dur": 21, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192746, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192768, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192792, "dur": 20, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192814, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192836, "dur": 30, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192867, "dur": 22, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192891, "dur": 17, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192911, "dur": 21, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192933, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192955, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424192978, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193001, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193023, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193043, "dur": 19, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193064, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193086, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193105, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193128, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193151, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193174, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193196, "dur": 33, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193230, "dur": 27, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193258, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193278, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193298, "dur": 14, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193314, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193336, "dur": 21, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193358, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193377, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193401, "dur": 17, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193419, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193460, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193484, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193506, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193530, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193552, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193575, "dur": 17, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193594, "dur": 21, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193616, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193639, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193661, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193683, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193706, "dur": 14, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193721, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193744, "dur": 21, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193766, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193789, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193811, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193833, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193856, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193878, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193903, "dur": 18, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193922, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193946, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193969, "dur": 14, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424193985, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194007, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194030, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194051, "dur": 141, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194197, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194199, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194249, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194251, "dur": 29, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194283, "dur": 24, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194308, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194331, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194354, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194378, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194402, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194425, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194448, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194471, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194494, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194512, "dur": 21, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194535, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194557, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194579, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194602, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194624, "dur": 24, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194650, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194670, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194693, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194716, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194739, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194762, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194785, "dur": 18, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194805, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194823, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194846, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194868, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194890, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194914, "dur": 43, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194961, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424194964, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195008, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195010, "dur": 27, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195039, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195062, "dur": 37, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195104, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195107, "dur": 62, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195171, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195173, "dur": 29, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195204, "dur": 30, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195237, "dur": 24, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195262, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195285, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195310, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195333, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195360, "dur": 24, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195386, "dur": 21, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195409, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195431, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195455, "dur": 20, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195476, "dur": 18, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195496, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195515, "dur": 21, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195537, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195560, "dur": 26, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195587, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195615, "dur": 18, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195635, "dur": 18, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195655, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195677, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195707, "dur": 18, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195727, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195750, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195772, "dur": 26, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195799, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195822, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195841, "dur": 18, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195860, "dur": 21, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195883, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195905, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195929, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195953, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195975, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424195994, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196015, "dur": 22, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196039, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196062, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196085, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196108, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196132, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196153, "dur": 17, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196172, "dur": 27, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196199, "dur": 24, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196225, "dur": 34, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196261, "dur": 22, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196284, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196307, "dur": 27, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196335, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196354, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196377, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196399, "dur": 22, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196423, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196445, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196469, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196487, "dur": 19, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196508, "dur": 22, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196532, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196555, "dur": 21, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196578, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196601, "dur": 23, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196626, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196653, "dur": 30, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196685, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196708, "dur": 22, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196732, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196754, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196776, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196799, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196818, "dur": 18, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196837, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196860, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196882, "dur": 22, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196905, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196927, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196949, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196968, "dur": 22, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424196991, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197014, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197037, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197060, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197083, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197106, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197129, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197151, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197175, "dur": 23, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197199, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197222, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197245, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197268, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197290, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197312, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197335, "dur": 22, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197358, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197377, "dur": 21, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197399, "dur": 26, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197428, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197462, "dur": 27, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197491, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197512, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197592, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197615, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197638, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197661, "dur": 27, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197690, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197712, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197734, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197757, "dur": 17, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197776, "dur": 17, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197794, "dur": 22, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197818, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197883, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197920, "dur": 37, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424197959, "dur": 48, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198010, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198012, "dur": 49, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198066, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198069, "dur": 52, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198122, "dur": 29, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198155, "dur": 28, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198185, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198231, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198260, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198283, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198309, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198311, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198334, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198364, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198388, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198411, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198435, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198460, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198489, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198511, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198535, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198558, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198579, "dur": 50, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198631, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198654, "dur": 22, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198677, "dur": 21, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198700, "dur": 17, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198719, "dur": 37, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198757, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198780, "dur": 23, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198804, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198823, "dur": 21, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198845, "dur": 29, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198875, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198895, "dur": 21, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198918, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198940, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424198959, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199000, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199021, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199044, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199067, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199087, "dur": 50, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199138, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199161, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199184, "dur": 24, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199210, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199230, "dur": 30, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199261, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199282, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199304, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199323, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199342, "dur": 44, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199387, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199412, "dur": 24, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199439, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199466, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199469, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199495, "dur": 19, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199515, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199538, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199561, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199580, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199602, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199654, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199676, "dur": 23, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199700, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199724, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199745, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199768, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199790, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199813, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199835, "dur": 23, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199859, "dur": 20, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199881, "dur": 33, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199915, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199938, "dur": 30, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199969, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424199991, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200009, "dur": 35, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200046, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200070, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200090, "dur": 18, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200110, "dur": 25, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200136, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200173, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200198, "dur": 23, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200223, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200245, "dur": 20, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200267, "dur": 32, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200300, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200325, "dur": 22, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200349, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200372, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200390, "dur": 40, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200431, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200454, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200476, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200495, "dur": 20, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200516, "dur": 44, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200562, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200588, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200610, "dur": 14, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200625, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200647, "dur": 38, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200686, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200712, "dur": 22, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200736, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200757, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200774, "dur": 36, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200812, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200834, "dur": 23, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200858, "dur": 55, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200916, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200918, "dur": 39, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200960, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424200988, "dur": 28, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201018, "dur": 32, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201052, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201054, "dur": 32, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201087, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201114, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201138, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201161, "dur": 21, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201184, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201206, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201227, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201250, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201271, "dur": 14, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201286, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201334, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201362, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201385, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201407, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201430, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201449, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201471, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201494, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201516, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201535, "dur": 20, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201557, "dur": 29, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201587, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201606, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201628, "dur": 20, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201649, "dur": 21, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201671, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201694, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201713, "dur": 38, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201753, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201776, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201798, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201820, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201842, "dur": 21, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201864, "dur": 20, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201886, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201914, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201936, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201955, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424201976, "dur": 31, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202008, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202027, "dur": 21, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202049, "dur": 17, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202068, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202090, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202110, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202138, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202160, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202182, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202204, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202222, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202244, "dur": 21, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202266, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202288, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202311, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202331, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202350, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202400, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202416, "dur": 23, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202440, "dur": 23, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202465, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202487, "dur": 42, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202531, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202552, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202578, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202600, "dur": 19, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202621, "dur": 30, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202651, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202675, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202696, "dur": 21, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202718, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202737, "dur": 43, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202782, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202803, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202826, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202848, "dur": 17, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202866, "dur": 37, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202905, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202927, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202947, "dur": 23, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202971, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424202993, "dur": 36, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203031, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203050, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203072, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203094, "dur": 17, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203112, "dur": 41, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203155, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203176, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203195, "dur": 22, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203219, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203238, "dur": 43, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203282, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203309, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203331, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203353, "dur": 20, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203374, "dur": 32, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203407, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203433, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203456, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203479, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203496, "dur": 33, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203531, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203553, "dur": 34, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203589, "dur": 21, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203611, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203629, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203659, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203682, "dur": 23, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203706, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203728, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203750, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203786, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203805, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203828, "dur": 33, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203866, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203869, "dur": 35, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203907, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203943, "dur": 27, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203972, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424203998, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204021, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204060, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204091, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204114, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204137, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204159, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204190, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204214, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204236, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204257, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204279, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204301, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204331, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204353, "dur": 28, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204382, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204405, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204424, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204456, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204483, "dur": 17, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204501, "dur": 25, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204528, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204550, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204573, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204596, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204619, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204638, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204657, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204679, "dur": 41, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204721, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204743, "dur": 31, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204776, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204798, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204818, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204847, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204870, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204892, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204915, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204934, "dur": 37, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204972, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424204994, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205016, "dur": 25, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205043, "dur": 27, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205071, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205091, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205111, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205133, "dur": 18, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205152, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205175, "dur": 17, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205193, "dur": 35, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205230, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205257, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205279, "dur": 21, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205301, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205324, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205347, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205370, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205392, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205414, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205432, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205463, "dur": 28, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205493, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205517, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205544, "dur": 20, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205565, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205588, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205611, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205633, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205656, "dur": 26, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205684, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205706, "dur": 14, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205721, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205771, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205799, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205822, "dur": 25, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205848, "dur": 23, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205873, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205896, "dur": 21, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205918, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205937, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205960, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424205979, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206002, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206047, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206069, "dur": 105, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206179, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206244, "dur": 262, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206509, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206569, "dur": 4, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206574, "dur": 38, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206613, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206615, "dur": 24, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206641, "dur": 28, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206672, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206674, "dur": 34, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206710, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206711, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206739, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206741, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206765, "dur": 20, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206787, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206809, "dur": 31, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206843, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206845, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206876, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206877, "dur": 26, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206906, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206907, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206938, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206966, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424206994, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207020, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207052, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207082, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207108, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207134, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207136, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207163, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207164, "dur": 71, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207238, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207240, "dur": 31, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207272, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207275, "dur": 30, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207307, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207346, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207348, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207375, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207377, "dur": 114, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207494, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207528, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207530, "dur": 28, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207560, "dur": 27, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207589, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207591, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207619, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207651, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207653, "dur": 24, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207680, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207706, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207708, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207737, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424207763, "dur": 1964, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424209731, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424209759, "dur": 293, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424210055, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424210080, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424210107, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424210182, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424210204, "dur": 1280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211486, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211511, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211543, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211578, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211600, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211758, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211778, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211905, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211947, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211949, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211986, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424211988, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212024, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212056, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212114, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212140, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212316, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212351, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212352, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212382, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212405, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212438, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212461, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212496, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212522, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212547, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212600, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212625, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212626, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212653, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212696, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212725, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212753, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212775, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212808, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212833, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212947, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212972, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424212996, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213032, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213060, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213102, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213130, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213161, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213201, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213230, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213256, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213299, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213330, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213363, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213417, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213449, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213479, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213481, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213545, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213573, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213602, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213630, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213651, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213707, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213740, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213764, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213791, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213792, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213822, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213846, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213871, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213902, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213929, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213931, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213960, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213989, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424213991, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214102, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214151, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214153, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214204, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214248, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214295, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214330, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214359, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214392, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214394, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214475, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214514, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214545, "dur": 449, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424214995, "dur": 41, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215038, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215061, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215080, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215111, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215317, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215351, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215405, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215438, "dur": 372, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215813, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215849, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424215884, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216190, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216218, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216302, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216333, "dur": 143, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216481, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216503, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216524, "dur": 373, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216899, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216921, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424216924, "dur": 242437, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424459371, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424459376, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424459412, "dur": 1750, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424461168, "dur": 999, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462175, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462223, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462225, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462264, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462293, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462438, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462469, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462471, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462566, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424462592, "dur": 445, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463041, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463079, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463081, "dur": 269, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463352, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463392, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463432, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463461, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463517, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463547, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463607, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463633, "dur": 206, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463844, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424463876, "dur": 277, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464156, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464195, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464197, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464237, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464257, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464410, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464444, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464619, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464649, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464767, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464804, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464956, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424464997, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465000, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465077, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465111, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465178, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465196, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465307, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465342, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465344, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465375, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465377, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465413, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465437, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465464, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465506, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465542, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465568, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465594, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465617, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465671, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465697, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465726, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465727, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465755, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465778, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465801, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465822, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465851, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465885, "dur": 70, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465958, "dur": 35, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465995, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424465998, "dur": 28, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466028, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466077, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466106, "dur": 21, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466129, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466130, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466163, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466192, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466222, "dur": 26, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466249, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466289, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466322, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466345, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466368, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466392, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466420, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466445, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466470, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466510, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424466542, "dur": 77007, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424543560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424543564, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424543602, "dur": 2754, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424546360, "dur": 4359, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424550726, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424550730, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424550769, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424550773, "dur": 61688, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424612471, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424612475, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424612508, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424612511, "dur": 187422, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424799943, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424799947, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424799975, "dur": 38, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424800014, "dur": 26239, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424826263, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424826267, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424826298, "dur": 29, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424826327, "dur": 3906, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424830243, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424830246, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424830285, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424830289, "dur": 1526, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424831821, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424831845, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424831864, "dur": 30913, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424862787, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424862795, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424862826, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424862829, "dur": 33672, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424896511, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424896516, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424896568, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424896572, "dur": 675, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424897251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424897254, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424897296, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646424897319, "dur": 173984, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425071313, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425071317, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425071364, "dur": 23, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425071388, "dur": 5036, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425076432, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425076435, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425076465, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425076468, "dur": 653, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425077127, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425077129, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425077168, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425077194, "dur": 57613, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425134816, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425134820, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425134868, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425134871, "dur": 606, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425135483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425135485, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425135525, "dur": 30, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425135556, "dur": 442, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425136002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425136004, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425136051, "dur": 441, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754646425136495, "dur": 5830, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754646425150240, "dur": 826, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18540, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18540, "tid": 8589934592, "ts": 1754646424069615, "dur": 43411, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18540, "tid": 8589934592, "ts": 1754646424113028, "dur": 72375, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18540, "tid": 8589934592, "ts": 1754646424185406, "dur": 1380, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754646425151068, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18540, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18540, "tid": 4294967296, "ts": 1754646423969408, "dur": 1173693, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754646423971478, "dur": 95180, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754646425143111, "dur": 2470, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754646425144683, "dur": 51, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754646425145640, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754646425151078, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754646424183257, "dur": 1454, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646424184724, "dur": 805, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646424185643, "dur": 56, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754646424185699, "dur": 361, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646424186945, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_44552E2E3B45DEBD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754646424186076, "dur": 19255, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646424205344, "dur": 929462, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646425134807, "dur": 130, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646425134998, "dur": 54, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646425135259, "dur": 1096, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754646424186380, "dur": 18998, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424205621, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_DF83CFB61A6929CE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646424205764, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646424205763, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646424205864, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646424205991, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646424206996, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646424208453, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754646424206111, "dur": 2854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424209018, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424209193, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424209445, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424209626, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424209853, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424210045, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424210248, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424210435, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424210668, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424210792, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424211076, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646424211161, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424211224, "dur": 529, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646424211998, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646424211217, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424212179, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424212298, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424212702, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424212980, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754646424213084, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424213411, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754646424213176, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424213658, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424213724, "dur": 1779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424215504, "dur": 244685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424460190, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424461440, "dur": 1165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424462606, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424462705, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424464064, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754646424465236, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424465452, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754646424465572, "dur": 669231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424186371, "dur": 18995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424205780, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646424205779, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646424205927, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754646424206079, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754646424206211, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754646424206700, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754646424206972, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424207278, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424207470, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424207682, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424207873, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424208039, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424208258, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424208745, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424208938, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424209118, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424209301, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424209518, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424209782, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424209999, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424210237, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424210440, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424210627, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424210784, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424211067, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646424211159, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424211603, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424211718, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424211800, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646424212092, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646424211960, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424212616, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424212738, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646424212853, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646424212968, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424213391, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424213463, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424213669, "dur": 1821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424215491, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754646424215580, "dur": 244605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424460186, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424461435, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424461515, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424462733, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424463132, "dur": 1201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754646424464334, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424464807, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Burst.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646424464806, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754646424464866, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424464939, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424465221, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424465295, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Editor.Tests.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754646424465371, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424465451, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754646424465574, "dur": 669230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424186392, "dur": 18997, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424205397, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646424205671, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_33F6F62BD0C52DED.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646424205800, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424205856, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646424205959, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_778247FFEF05A9EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646424206158, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646424206376, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754646424206489, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646424206637, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646424206888, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3867973322336625480.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754646424206966, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424207312, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424207520, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424207749, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424207939, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424208200, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424208394, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424208911, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424209110, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424209306, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424209629, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424209839, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424210036, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424210247, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424210551, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424210616, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424210787, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424211066, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646424211133, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424211889, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\UITKAssetEditor\\InputActionsEditorSettingsProvider.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754646424211250, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424212386, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646424213300, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerFloatField.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754646424212852, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424213669, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754646424213741, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424213985, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424214344, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424214583, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424215497, "dur": 244672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424460171, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424461390, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424461542, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424463080, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424463425, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754646424464677, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424464772, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424464869, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424465165, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424465319, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754646424465470, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754646424465794, "dur": 669021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424186364, "dur": 18991, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424205362, "dur": 2431, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424207793, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424207977, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424208208, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424208409, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424208900, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424209072, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424209240, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424209427, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424209659, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424209879, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424210083, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424210249, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424210619, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424210786, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424211077, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646424211172, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424211505, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424211601, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646424211764, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424213008, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646424213187, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424213506, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424213569, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424213668, "dur": 1820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424215489, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754646424215567, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424215745, "dur": 244436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424460193, "dur": 1208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424462198, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Channels.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646424462253, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646424461450, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424462799, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754646424464087, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646424462889, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424465095, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754646424464417, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754646424465705, "dur": 669131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424186521, "dur": 18932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424205610, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D9FC4B6329D94867.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646424205759, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646424205757, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646424205844, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B4028357F84AC7DF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646424206098, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754646424206233, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754646424206395, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754646424206592, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754646424206695, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754646424206978, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424207327, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424207522, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424207714, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424207915, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424208105, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424208357, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424208846, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424209050, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424209233, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424209410, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424209605, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424209836, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424210016, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424210251, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424210440, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424210678, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424210779, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424211080, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646424211180, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646424211537, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424211806, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646424212019, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646424212705, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754646424212807, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754646424213243, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424213681, "dur": 1826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424215507, "dur": 244687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424460220, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646424461836, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646424463082, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424463137, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754646424464407, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424464768, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424464843, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646424464842, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646424465116, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754646424465316, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754646424465460, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754646424465656, "dur": 669171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424186445, "dur": 18973, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424205452, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754646424205436, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646424205520, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424205760, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424205834, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_20062661B6C0E97A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646424206010, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754646424206141, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754646424206377, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754646424207000, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424207338, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424207572, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424207693, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424207875, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424208054, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424208281, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424208753, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424208955, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424209132, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424209357, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424209544, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424209726, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424209951, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424210143, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424210382, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424210628, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424210789, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424211073, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646424211191, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424211555, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424211685, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646424211803, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646424211916, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424211995, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424212490, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424212986, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754646424213190, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424213498, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424213617, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424213674, "dur": 1826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424215500, "dur": 244671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424460181, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424461432, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424463100, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754646424462800, "dur": 1339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/OpenFracture.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424464140, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754646424464355, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754646424464206, "dur": 1411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754646424465648, "dur": 669153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424186428, "dur": 18974, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424205411, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_1DBF74AC9293BF07.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646424205550, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646424205774, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424206151, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754646424206546, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754646424206699, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754646424206961, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424207294, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424207480, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424207766, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424207962, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424208131, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424208349, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424208829, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424209030, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424209201, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424209448, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424209652, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424209837, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424210024, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424210236, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424210435, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424210654, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424210790, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424211084, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646424211169, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424211614, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646424211896, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424212372, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424212715, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424213267, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424213381, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424213664, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754646424213738, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424213934, "dur": 1564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424215498, "dur": 244984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424461498, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646424460483, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424462254, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.StaticFiles.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646424461707, "dur": 1635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424463343, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424464053, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646424464355, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754646424463504, "dur": 1638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424465162, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754646424465467, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754646424465711, "dur": 669127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424186473, "dur": 18963, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424205761, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424205971, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754646424206179, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754646424206629, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754646424206901, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754646424206993, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424207315, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424207480, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424207724, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424207902, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424208228, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424208750, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424208959, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424209142, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424209364, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424209545, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424209781, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424209972, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424210165, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424210352, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424210612, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424210778, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424211070, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646424211288, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424211661, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424211792, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646424212278, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.dgreenheck.openfracture@6d0ddff641\\Runtime\\Scripts\\Fragment\\EdgeConstraint.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754646424212596, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.dgreenheck.openfracture@6d0ddff641\\Runtime\\Scripts\\Fragment\\UnfreezeFragment.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754646424211896, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424212781, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424212930, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424213255, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646424213042, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424213525, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424213666, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646424213752, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424214216, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754646424214296, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424215134, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424215571, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424215783, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424216186, "dur": 326603, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424544732, "dur": 4865, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646424544523, "dur": 5135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424549945, "dur": 249246, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424800465, "dur": 60070, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754646424800463, "dur": 60840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646424861894, "dur": 88, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754646424861999, "dur": 208544, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754646425075517, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754646425075515, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754646425075672, "dur": 723, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754646425076399, "dur": 58431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424186498, "dur": 18946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424205545, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646424205543, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4B3448FA7561224A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424205761, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646424205760, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_35385077F0CEC424.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424205929, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754646424206170, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754646424206405, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754646424206598, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754646424206707, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754646424206972, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424207300, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424207473, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424207723, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424207925, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424208153, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424208336, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424208827, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424209036, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424209222, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424209405, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424209597, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424209857, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424210068, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424210276, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424210529, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424210629, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424210791, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424211083, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424211208, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424211662, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424211798, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424211920, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424212042, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424212247, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424212733, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754646424213013, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646424213236, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646424212900, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424213790, "dur": 1712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424215502, "dur": 244685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424460189, "dur": 1203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424461448, "dur": 1207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424462656, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754646424463642, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Numerics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646424462884, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424464729, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Memory.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754646424464218, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754646424465585, "dur": 669241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424186552, "dur": 18911, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424205762, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1754646424205749, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_180837F1985FCEBE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646424205967, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646424206202, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754646424206534, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754646424206673, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754646424206989, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424207338, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424207489, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424207759, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424208020, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424208227, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424208410, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424208909, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424209147, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424209393, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424209596, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424209804, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424209994, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424210172, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424210361, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424210559, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424210617, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424210791, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424211086, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646424211224, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646424211211, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424211810, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754646424212278, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646424211988, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424212553, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424212612, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424212958, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424213058, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424213166, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424213678, "dur": 1821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424215499, "dur": 244680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424460185, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424461417, "dur": 1154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424462813, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646424462621, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424463928, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754646424465096, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754646424464429, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754646424465667, "dur": 669151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424186568, "dur": 18909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424205744, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424205876, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754646424206097, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646424206235, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754646424206425, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754646424206502, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646424206789, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754646424206979, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424207336, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424207615, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424207811, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424207969, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424208130, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424208348, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424208817, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424209021, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424209242, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424209434, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424209638, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424209851, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424210040, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424210238, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424210441, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424210611, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424210777, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424211067, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646424211215, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424211179, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424211666, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424211908, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754646424212277, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424212077, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424212606, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424212948, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424213110, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424213180, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424213666, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424214363, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424214675, "dur": 819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424215494, "dur": 244705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424460201, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424461806, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424463444, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424462298, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireEditorAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424463573, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424464091, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Antiforgery.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424464842, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424463674, "dur": 1596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424465312, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754646424465475, "dur": 79056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424544533, "dur": 65632, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424544532, "dur": 66457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424611573, "dur": 92, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754646424611715, "dur": 213781, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754646424829316, "dur": 66375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424829315, "dur": 66379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424895716, "dur": 808, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754646424896528, "dur": 238280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424186605, "dur": 18894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424205533, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424205516, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_A8B81DEC69E8E1CF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646424205756, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424205755, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BA3DE9AF0772CF8F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646424205982, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754646424206084, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754646424206207, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646424206450, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754646424206697, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754646424206981, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424207310, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424207482, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424207747, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424207937, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424208117, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424208325, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424208812, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424209010, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424209182, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424209349, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424209521, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424209715, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424209964, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424210161, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424210349, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424210612, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424210782, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424211073, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646424211195, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424211603, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646424211743, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646424211905, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424212383, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424212465, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754646424212567, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424213183, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424213668, "dur": 1825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424215493, "dur": 244681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424460176, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424462253, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424462589, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424461422, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424463109, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424463443, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424464054, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424462873, "dur": 1617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424464491, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754646424464701, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754646424464556, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754646424465787, "dur": 669042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424186639, "dur": 18906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424205559, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_DD29252AECEC5C10.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646424205761, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424206016, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754646424206222, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754646424206456, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754646424206589, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424206641, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754646424206960, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424207270, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424207462, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424207750, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424208051, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424208293, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424208861, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424209029, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424209193, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424209378, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424209610, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424209873, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424210049, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424210239, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424210450, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424210626, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424210785, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424211068, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646424211182, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646424211795, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424212050, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646424212215, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754646424212987, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424213173, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424213671, "dur": 1821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424215493, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754646424215584, "dur": 244650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424460235, "dur": 1611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646424461856, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424462814, "dur": 377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646424462301, "dur": 1545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646424464803, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646424463880, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754646424465327, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.MemoryProfiler.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754646424465457, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754646424465677, "dur": 669134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424186694, "dur": 18863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424205645, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424205644, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AB4BD236749C82D4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646424205770, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424205950, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754646424206432, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754646424206612, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1754646424206983, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424207305, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424207487, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424207710, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424207934, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424208103, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424208317, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424208789, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424208975, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424209303, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424209501, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424209729, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424209948, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424210129, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424210324, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424210652, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424210795, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424211144, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646424211384, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646424211893, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646424212111, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424212244, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754646424212340, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424212580, "dur": 372, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424212398, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754646424213224, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424213480, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424213672, "dur": 1838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424215510, "dur": 244709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424460221, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646424461498, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424462254, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424462337, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424461435, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646424462898, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424463141, "dur": 335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424464355, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646424463105, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754646424464752, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424464825, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424465021, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424465353, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646424465489, "dur": 610035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754646425075526, "dur": 58474, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646425075525, "dur": 58478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754646425134024, "dur": 722, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646424186729, "dur": 18842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424205781, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424205853, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754646424205995, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646424206063, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646424206587, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646424207001, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646424208455, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754646424209163, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754646424206144, "dur": 3105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424209250, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424209335, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646424209451, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424210845, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424211077, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646424211169, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646424211236, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424211666, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424211767, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754646424211982, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424212499, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1754646424212951, "dur": 235, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424213787, "dur": 244819, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1754646424460182, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424461429, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424462566, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424463082, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Results.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754646424462649, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424463829, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754646424464329, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754646424465581, "dur": 669218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424186767, "dur": 18863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424205630, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_31158FA896333743.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646424205753, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424205921, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754646424206225, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754646424206564, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754646424206703, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1754646424207011, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424207342, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424207500, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424207737, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424207924, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424208095, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424208327, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424208799, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424209005, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424209174, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424209420, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424209589, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424209802, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424209997, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424210175, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424210361, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424210541, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424210613, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424210776, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424211071, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646424211134, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424211239, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646424211668, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424211795, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754646424211891, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\RayFire\\Plugins\\Windows\\x86_x64\\RFLib_DotNet_2018.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646424211891, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754646424212421, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424212922, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424213030, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424213142, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424213685, "dur": 1810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424215496, "dur": 244680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424460178, "dur": 1625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646424461843, "dur": 1223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646424463111, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646424464236, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754646424463110, "dur": 1527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754646424464689, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424464883, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424464993, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424465075, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424465486, "dur": 363834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754646424829322, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754646424829321, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754646424829479, "dur": 1610, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754646424831092, "dur": 303730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754646425139900, "dur": 1322, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18540, "tid": 1055, "ts": 1754646425151363, "dur": 1305, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18540, "tid": 1055, "ts": 1754646425152756, "dur": 1428, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18540, "tid": 1055, "ts": 1754646425149099, "dur": 5557, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}