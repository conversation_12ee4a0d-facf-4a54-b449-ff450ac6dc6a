{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18540, "tid": 1059, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18540, "tid": 1059, "ts": 1754645251892970, "dur": 291, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18540, "tid": 1059, "ts": 1754645251895148, "dur": 467, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18540, "tid": 1, "ts": 1754645251003141, "dur": 3174, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754645251006318, "dur": 17928, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18540, "tid": 1, "ts": 1754645251024255, "dur": 18181, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18540, "tid": 1059, "ts": 1754645251895618, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 18540, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251002161, "dur": 115100, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251117265, "dur": 770947, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251117954, "dur": 1000, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251118960, "dur": 621, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251119585, "dur": 99, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251119686, "dur": 222, "ph": "X", "name": "ProcessMessages 8911", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251119909, "dur": 59, "ph": "X", "name": "ReadAsync 8911", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251119970, "dur": 1, "ph": "X", "name": "ProcessMessages 3804", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251119972, "dur": 36, "ph": "X", "name": "ReadAsync 3804", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120010, "dur": 53, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120066, "dur": 24, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120092, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120117, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120134, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120156, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120179, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120205, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120227, "dur": 21, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120251, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120273, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120296, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120319, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120343, "dur": 21, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120366, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120389, "dur": 15, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120406, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120428, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120451, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120474, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120497, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120521, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120544, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120570, "dur": 22, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120593, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120617, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120640, "dur": 21, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120662, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120685, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120708, "dur": 23, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120732, "dur": 28, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120763, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120765, "dur": 38, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120806, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120807, "dur": 37, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120847, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120849, "dur": 34, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120885, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120887, "dur": 35, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120923, "dur": 22, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120947, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120973, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251120995, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121018, "dur": 21, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121041, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121064, "dur": 22, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121088, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121114, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121138, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121161, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121183, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121211, "dur": 26, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121239, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121263, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121287, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121308, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121333, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121356, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121381, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121404, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121425, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121447, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121469, "dur": 24, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121494, "dur": 21, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121517, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121540, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121565, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121586, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121608, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121634, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121657, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121679, "dur": 20, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121701, "dur": 27, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121729, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121750, "dur": 24, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121776, "dur": 23, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121800, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121823, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121846, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121870, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121892, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121913, "dur": 69, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251121984, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122013, "dur": 20, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122034, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122057, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122075, "dur": 21, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122097, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122120, "dur": 24, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122146, "dur": 22, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122169, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122191, "dur": 36, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122229, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122251, "dur": 28, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122280, "dur": 26, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122307, "dur": 21, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122331, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122332, "dur": 28, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122362, "dur": 25, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122389, "dur": 21, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122411, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122436, "dur": 29, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122467, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122490, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122511, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122534, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122556, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122579, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122601, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122622, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122644, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122664, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122686, "dur": 21, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122709, "dur": 35, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122746, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122747, "dur": 42, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122792, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122794, "dur": 28, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122823, "dur": 23, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122848, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122870, "dur": 24, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122896, "dur": 22, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122919, "dur": 20, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122941, "dur": 23, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122967, "dur": 25, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251122994, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123017, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123039, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123060, "dur": 21, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123082, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123105, "dur": 19, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123125, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123149, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123171, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123194, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123215, "dur": 38, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123254, "dur": 25, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123281, "dur": 23, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123305, "dur": 21, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123327, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123350, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123372, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123393, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123410, "dur": 20, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123431, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123453, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123475, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123497, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123519, "dur": 18, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123538, "dur": 22, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123562, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123584, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123607, "dur": 21, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123629, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123653, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123675, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123697, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123718, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123741, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123764, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123786, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123805, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123828, "dur": 22, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123851, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123872, "dur": 22, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123895, "dur": 30, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123927, "dur": 49, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123979, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251123980, "dur": 44, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124026, "dur": 38, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124067, "dur": 38, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124108, "dur": 23, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124132, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124155, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124178, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124203, "dur": 24, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124230, "dur": 26, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124257, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124281, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124304, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124323, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124345, "dur": 24, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124371, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124394, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124416, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124438, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124461, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124484, "dur": 21, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124506, "dur": 91, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124599, "dur": 28, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124629, "dur": 21, "ph": "X", "name": "ReadAsync 1367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124651, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124670, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124692, "dur": 31, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124725, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124749, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124771, "dur": 27, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124799, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124838, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124839, "dur": 69, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124909, "dur": 36, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124948, "dur": 1, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124949, "dur": 32, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251124984, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125008, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125030, "dur": 13, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125044, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125066, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125089, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125111, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125131, "dur": 51, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125183, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125206, "dur": 29, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125236, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125257, "dur": 24, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125282, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125304, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125328, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125349, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125370, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125391, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125412, "dur": 21, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125434, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125456, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125479, "dur": 20, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125500, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125521, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125539, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125559, "dur": 21, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125582, "dur": 20, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125604, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125625, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125646, "dur": 71, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125719, "dur": 23, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125743, "dur": 26, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125770, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125793, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125818, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125840, "dur": 21, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125862, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125884, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125907, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125929, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125949, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125971, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251125994, "dur": 25, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126020, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126043, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126066, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126087, "dur": 20, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126109, "dur": 18, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126127, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126148, "dur": 21, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126172, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126195, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126218, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126243, "dur": 17, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126261, "dur": 22, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126284, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126306, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126328, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126352, "dur": 19, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126373, "dur": 21, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126395, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126417, "dur": 181, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126599, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126622, "dur": 22, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126645, "dur": 20, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126666, "dur": 17, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126684, "dur": 20, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126707, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126708, "dur": 35, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126745, "dur": 22, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126768, "dur": 28, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126797, "dur": 28, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126827, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126829, "dur": 35, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126866, "dur": 42, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126909, "dur": 25, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126936, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126960, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251126982, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127007, "dur": 34, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127042, "dur": 26, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127070, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127095, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127118, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127140, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127164, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127187, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127211, "dur": 22, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127235, "dur": 28, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127264, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127286, "dur": 57, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127345, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127371, "dur": 20, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127393, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127416, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127438, "dur": 21, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127460, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127483, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127506, "dur": 21, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127528, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127550, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127574, "dur": 20, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127595, "dur": 21, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127617, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127639, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127662, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127684, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127708, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127731, "dur": 34, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127769, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127772, "dur": 58, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127833, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127835, "dur": 30, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127867, "dur": 26, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127895, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127914, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127937, "dur": 24, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127964, "dur": 23, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251127989, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128012, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128034, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128057, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128079, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128102, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128127, "dur": 21, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128149, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128173, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128194, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128216, "dur": 25, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128243, "dur": 23, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128268, "dur": 23, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128294, "dur": 46, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128346, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128348, "dur": 79, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128429, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128431, "dur": 48, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128482, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128484, "dur": 35, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128520, "dur": 21, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128543, "dur": 23, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128567, "dur": 24, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128593, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128617, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128640, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128662, "dur": 20, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128683, "dur": 31, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128717, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128718, "dur": 32, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128753, "dur": 29, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128784, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128806, "dur": 21, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128829, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128856, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128880, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128904, "dur": 20, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128925, "dur": 20, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128946, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128966, "dur": 17, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251128984, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129006, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129029, "dur": 20, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129050, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129072, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129098, "dur": 20, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129120, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129142, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129165, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129186, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129208, "dur": 29, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129239, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129262, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129284, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129306, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129328, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129350, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129371, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129392, "dur": 20, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129413, "dur": 23, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129438, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129460, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129482, "dur": 34, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129517, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129536, "dur": 20, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129557, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129580, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129602, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129623, "dur": 20, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129645, "dur": 18, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129664, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129685, "dur": 111, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129797, "dur": 35, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129834, "dur": 20, "ph": "X", "name": "ReadAsync 1282", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129856, "dur": 29, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129888, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129929, "dur": 30, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129961, "dur": 28, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251129990, "dur": 51, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130043, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130076, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130078, "dur": 32, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130112, "dur": 25, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130139, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130161, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130183, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130205, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130226, "dur": 21, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130248, "dur": 43, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130293, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130316, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130340, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130360, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130381, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130402, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130423, "dur": 22, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130447, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130468, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130487, "dur": 19, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130508, "dur": 21, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130530, "dur": 44, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130578, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130612, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130636, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130657, "dur": 40, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130698, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130721, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130741, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130763, "dur": 21, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130786, "dur": 27, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130814, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130837, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130859, "dur": 18, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130879, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130899, "dur": 43, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130943, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130966, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251130989, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131010, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131033, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131072, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131095, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131117, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131139, "dur": 26, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131167, "dur": 34, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131204, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131236, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131261, "dur": 29, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131292, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131293, "dur": 27, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131322, "dur": 20, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131344, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131367, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131393, "dur": 50, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131445, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131466, "dur": 22, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131490, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131512, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131535, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131578, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131601, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131623, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131641, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131662, "dur": 38, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131702, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131727, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131750, "dur": 22, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131774, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131792, "dur": 30, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131824, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131846, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131870, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131895, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131916, "dur": 36, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131953, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131976, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251131999, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132022, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132045, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132067, "dur": 14, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132083, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132105, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132127, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132150, "dur": 19, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132171, "dur": 28, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132201, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132223, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132245, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132268, "dur": 21, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132290, "dur": 41, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132333, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132356, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132379, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132401, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132423, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132452, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132477, "dur": 38, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132516, "dur": 34, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132553, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132554, "dur": 36, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132591, "dur": 24, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132617, "dur": 43, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132663, "dur": 26, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132691, "dur": 21, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132714, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132737, "dur": 30, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132769, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132771, "dur": 32, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132805, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132807, "dur": 40, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132850, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132884, "dur": 22, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132908, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132931, "dur": 62, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251132994, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133023, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133024, "dur": 41, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133066, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133088, "dur": 60, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133151, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133176, "dur": 19, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133197, "dur": 20, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133218, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133235, "dur": 50, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133287, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133309, "dur": 21, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133332, "dur": 21, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133354, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133375, "dur": 37, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133413, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133436, "dur": 30, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133469, "dur": 23, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133493, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133515, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133537, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133559, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133583, "dur": 20, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133604, "dur": 19, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133624, "dur": 25, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133651, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133674, "dur": 17, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133692, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133716, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133744, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133771, "dur": 32, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133806, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133808, "dur": 30, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133839, "dur": 17, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133858, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133905, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133929, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133955, "dur": 19, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133976, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251133997, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134016, "dur": 44, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134062, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134085, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134107, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134128, "dur": 18, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134147, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134170, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134191, "dur": 20, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134213, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134235, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134255, "dur": 19, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134275, "dur": 40, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134316, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134338, "dur": 21, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134361, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134383, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134402, "dur": 32, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134436, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134458, "dur": 21, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134481, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134504, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134527, "dur": 20, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134548, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134572, "dur": 27, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134600, "dur": 20, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134622, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134643, "dur": 19, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134664, "dur": 28, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134693, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134716, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134735, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134760, "dur": 30, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134794, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134796, "dur": 33, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134833, "dur": 32, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134867, "dur": 22, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134891, "dur": 48, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134940, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134963, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251134984, "dur": 20, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135006, "dur": 19, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135027, "dur": 34, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135062, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135082, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135103, "dur": 22, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135126, "dur": 20, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135147, "dur": 34, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135183, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135206, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135228, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135250, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135271, "dur": 33, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135305, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135328, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135350, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135372, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135393, "dur": 28, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135423, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135448, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135469, "dur": 21, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135492, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135510, "dur": 38, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135550, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135681, "dur": 41, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135724, "dur": 65, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135795, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135798, "dur": 48, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135848, "dur": 39, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135889, "dur": 23, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135915, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135917, "dur": 38, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135957, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251135981, "dur": 54, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136038, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136072, "dur": 17, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136091, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136115, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136140, "dur": 54, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136196, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136224, "dur": 35, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136260, "dur": 14, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136275, "dur": 43, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136320, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136348, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136370, "dur": 22, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136393, "dur": 20, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136415, "dur": 50, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136469, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136513, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136514, "dur": 36, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136553, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136575, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136597, "dur": 19, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136618, "dur": 17, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136636, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136660, "dur": 40, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136703, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136737, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136739, "dur": 40, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136781, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136783, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136809, "dur": 36, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136846, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136868, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136893, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136917, "dur": 21, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136940, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136963, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251136988, "dur": 25, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137015, "dur": 65, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137082, "dur": 26, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137109, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137131, "dur": 20, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137153, "dur": 22, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137177, "dur": 20, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137198, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137236, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137258, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137285, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137311, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137330, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137357, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137379, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137398, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137419, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137441, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137464, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137488, "dur": 20, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137509, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137532, "dur": 19, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137552, "dur": 20, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137574, "dur": 37, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137612, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137634, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137656, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137674, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137696, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137718, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137740, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137762, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137783, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137804, "dur": 20, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137825, "dur": 36, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137863, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137886, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137907, "dur": 20, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137928, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137950, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137972, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251137996, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138018, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138040, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138061, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138080, "dur": 19, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138101, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138139, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138172, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138193, "dur": 20, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138215, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138237, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138259, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138280, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138302, "dur": 20, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138324, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138344, "dur": 18, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138363, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138412, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138434, "dur": 123, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138560, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251138627, "dur": 381, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139011, "dur": 77, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139091, "dur": 4, "ph": "X", "name": "ProcessMessages 1456", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139096, "dur": 53, "ph": "X", "name": "ReadAsync 1456", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139154, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139156, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139198, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139200, "dur": 30, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139232, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139233, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139269, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139270, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139305, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139307, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139336, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139338, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139394, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139399, "dur": 40, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139440, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139442, "dur": 57, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139500, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139502, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139536, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139538, "dur": 30, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139570, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139572, "dur": 34, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139607, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139609, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139645, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139647, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139672, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139674, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139701, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139725, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139727, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139755, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139784, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139813, "dur": 23, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139838, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139869, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139870, "dur": 25, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139896, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139897, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139928, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139929, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139960, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139962, "dur": 27, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139991, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251139993, "dur": 27, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140022, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140024, "dur": 27, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140053, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140082, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140084, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140111, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140142, "dur": 25, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140170, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140196, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140197, "dur": 23, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140223, "dur": 23, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251140249, "dur": 2457, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251142711, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251142750, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251142751, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251142817, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251142929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251142931, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251143010, "dur": 183, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251143196, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251143221, "dur": 1406, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144632, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144671, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144674, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144704, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144729, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144895, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251144929, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145015, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145049, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145051, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145083, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145114, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145235, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145258, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145381, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145402, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145425, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145447, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145470, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145492, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145515, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145544, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145568, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145588, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145617, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145640, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145660, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145681, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145706, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145745, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145776, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145805, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145882, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145903, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145932, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145958, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145960, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251145990, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146025, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146051, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146124, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146150, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146172, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146200, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146229, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146285, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146307, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146328, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146349, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146369, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146390, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146419, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146442, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146467, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146494, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146520, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146542, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146566, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146590, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146624, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146647, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146669, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146690, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146736, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146759, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146789, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146810, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146838, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146860, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146863, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146889, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146910, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146933, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146960, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251146986, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147008, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147030, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147103, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147131, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147158, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147190, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147216, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147248, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147249, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147277, "dur": 552, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147830, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147882, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147883, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147943, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251147986, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148021, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148043, "dur": 215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148263, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148288, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148315, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148341, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148566, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148604, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148606, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148630, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148959, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251148960, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149003, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149005, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149063, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149108, "dur": 133, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149245, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149269, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149271, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149292, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149682, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149709, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251149712, "dur": 240187, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251389910, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251389913, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251389955, "dur": 1819, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251391780, "dur": 567, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392353, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392394, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392396, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392428, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392430, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392619, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251392654, "dur": 707, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393366, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393399, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393428, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393454, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393482, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393576, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393606, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393641, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393666, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393709, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393748, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393749, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393779, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393813, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393838, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393864, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251393885, "dur": 780, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394669, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394725, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394819, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394863, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394865, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394938, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394966, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251394993, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395021, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395150, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395182, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395184, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395219, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395241, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395333, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395360, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395381, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395425, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395455, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395490, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395519, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395542, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395576, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395598, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395629, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395651, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395674, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395700, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395721, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395745, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395770, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395792, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395821, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395823, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395854, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395875, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395902, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395935, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395966, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251395993, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396015, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396043, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396073, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396075, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396109, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396131, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396153, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396177, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396207, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396209, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396246, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396248, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396287, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396318, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396320, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396368, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396370, "dur": 36, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396408, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396436, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396458, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251396489, "dur": 81985, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251478484, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251478487, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251478545, "dur": 2638, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251481185, "dur": 3765, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251484955, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251485005, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251485009, "dur": 58281, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251543300, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251543303, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251543340, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251543344, "dur": 262293, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251805649, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251805653, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251805692, "dur": 25, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251805718, "dur": 4129, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251809855, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251809858, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251809907, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251809910, "dur": 1437, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251811361, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251811368, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251811411, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251811438, "dur": 680, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251812123, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251812162, "dur": 13, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251812176, "dur": 3429, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251815610, "dur": 231, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251815843, "dur": 60493, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251876346, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251876350, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251876421, "dur": 3345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251879776, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251879779, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251879836, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251879841, "dur": 745, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251880592, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251880596, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251880664, "dur": 28, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251880693, "dur": 490, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251881187, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251881243, "dur": 426, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18540, "tid": 12884901888, "ts": 1754645251881671, "dur": 6490, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18540, "tid": 1059, "ts": 1754645251895626, "dur": 779, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18540, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18540, "tid": 8589934592, "ts": 1754645251000683, "dur": 41862, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18540, "tid": 8589934592, "ts": 1754645251042548, "dur": 74714, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18540, "tid": 8589934592, "ts": 1754645251117265, "dur": 1426, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18540, "tid": 1059, "ts": 1754645251896406, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18540, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645250903197, "dur": 985671, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645250905185, "dur": 92692, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645251888878, "dur": 2231, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645251890443, "dur": 49, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18540, "tid": 4294967296, "ts": 1754645251891155, "dur": 7, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18540, "tid": 1059, "ts": 1754645251896410, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754645251116519, "dur": 1434, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251117964, "dur": 820, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251118904, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754645251118962, "dur": 494, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251119628, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_A3CA169B49A7C799.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754645251120155, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D8B70DCE17B2FCB8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754645251126191, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754645251128815, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754645251128892, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754645251119480, "dur": 19495, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251138987, "dur": 742210, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251881199, "dur": 217, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251881417, "dur": 71, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251881635, "dur": 65, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251881726, "dur": 1073, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754645251119753, "dur": 19242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251139003, "dur": 2238, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251141242, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251141542, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251141704, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251141894, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251142483, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251143024, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251143224, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251143421, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251143577, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251143752, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251143971, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251144172, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251144343, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251144546, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251144759, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251145029, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251145201, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251145492, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645251145617, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251145951, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251146110, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645251146217, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754645251146310, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251146734, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645251146733, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251147396, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251147465, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251147556, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251147702, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251148565, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251148860, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251149541, "dur": 242160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251391711, "dur": 1130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251394026, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645251392891, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251394202, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645251395153, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754645251394201, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754645251395666, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251395930, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251396130, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251396395, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251396518, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251396754, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251396811, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754645251396897, "dur": 484339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251119797, "dur": 19220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251139028, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251139095, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_370C590F53957EFB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645251139387, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251139531, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754645251139903, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754645251139967, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754645251140035, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754645251140291, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754645251140415, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754645251140709, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754645251140762, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251141141, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251141342, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251141523, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251141705, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251141932, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251142401, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251143016, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251143204, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251143356, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251143632, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251143831, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251144022, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251144174, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251144323, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251144483, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251144680, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251144922, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251145190, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251145477, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645251145561, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251145948, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645251146513, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Tool\\AuthToken.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754645251146036, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251147012, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251147178, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754645251147296, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251147708, "dur": 1835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251149543, "dur": 242141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251391687, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Base.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251392857, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251394030, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645251393160, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireEditorAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251394415, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251396520, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754645251395488, "dur": 1221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754645251396709, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754645251396888, "dur": 484324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251119874, "dur": 19183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251139071, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_38C1DD3B59418845.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645251139167, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_613AE6E2D149BAF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645251139296, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251139414, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251139587, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754645251139797, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754645251140016, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754645251140469, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251140736, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251141102, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251141289, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251141533, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251141698, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251141901, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251142519, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251143082, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251143321, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251143578, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251143758, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251143992, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251144576, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251144757, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251145008, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251145204, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251145585, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251146006, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645251146116, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645251146242, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645251146334, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251146864, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754645251147061, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251147725, "dur": 1834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251149559, "dur": 242153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251391751, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645251391714, "dur": 1147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251394030, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645251392903, "dur": 1306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251395153, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645251394261, "dur": 1327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251395589, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754645251396474, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754645251395769, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754645251397012, "dur": 484223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251119868, "dur": 19172, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251139266, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_09D119D0E0AA1ABF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645251139355, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251139539, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645251139602, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754645251139835, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645251140468, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645251140769, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645251141988, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754645251142512, "dur": 377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754645251139942, "dur": 3269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251143251, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251143355, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251143604, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251143757, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251143968, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251144166, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251144609, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251144782, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251144999, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251145200, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251145470, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645251145583, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251146519, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645251146762, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645251146694, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251147702, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754645251147766, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251147946, "dur": 1600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251149546, "dur": 242141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251391689, "dur": 1147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251392877, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251394030, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645251392952, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251394228, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251394309, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251396431, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754645251395364, "dur": 1185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754645251396804, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754645251396872, "dur": 484314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251119785, "dur": 19221, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251139444, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251139719, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_8573C5AEBE7FAC0E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645251139982, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754645251140256, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1754645251140494, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251140766, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251141150, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251141321, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251141571, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251141722, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251141932, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251142879, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251143071, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251143255, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251143749, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251143965, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251144511, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251144674, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251144922, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251145192, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251145490, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645251145580, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251146009, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645251146100, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645251146191, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251146478, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251147066, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645251147065, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645251147132, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754645251147249, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251147428, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251147748, "dur": 1807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251149555, "dur": 242161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251391719, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251392906, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251393886, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251394031, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645251393957, "dur": 1139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251395096, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251395207, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251396234, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754645251395262, "dur": 1351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754645251396614, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754645251396862, "dur": 484325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251119834, "dur": 19193, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251139034, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_7FCD2258FCB2D3F4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645251139403, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251139590, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_12BA7C2C14719C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645251139742, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754645251140417, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754645251140686, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3867973322336625480.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754645251140741, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251141109, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251141302, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251141535, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251141754, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251141963, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251142934, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251143105, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251143274, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251143468, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251143686, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251143955, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251144157, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251144320, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251145030, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251145205, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251145505, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645251145645, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251146066, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251146143, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251146500, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251147020, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645251146586, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251147145, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251147233, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645251147416, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645251147339, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251147737, "dur": 1797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251149543, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754645251149628, "dur": 242078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251391707, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251392912, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251394022, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754645251395227, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645251395386, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645251394194, "dur": 1461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/OpenFracture.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251396289, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754645251395691, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754645251397016, "dur": 484185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251119920, "dur": 19175, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251139116, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_B6DB781081D3DD3C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251139416, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251139618, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754645251139864, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754645251139986, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754645251140264, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754645251140425, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1754645251140498, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1754645251140753, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251141164, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251141400, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251141600, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251141783, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251141957, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251142876, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251143080, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251143490, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251143674, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251143839, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251144081, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251144223, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251144362, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251144566, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251144780, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251145054, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251145195, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251145485, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251145608, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251146058, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251146160, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251146306, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251146788, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251146951, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251147136, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251147565, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251147701, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251147778, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251148163, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251148226, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251149109, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251149172, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251149543, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754645251149604, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251149817, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251150242, "dur": 328747, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251480792, "dur": 4587, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645251480580, "dur": 4861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251485480, "dur": 56649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754645251485477, "dur": 57480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251543577, "dur": 82, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754645251543855, "dur": 268812, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754645251816039, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754645251816038, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754645251816144, "dur": 65059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251119898, "dur": 19171, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251139231, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_022E81689B98A39E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645251139414, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251139609, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754645251140200, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754645251140480, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251140731, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251141203, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251141426, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251141621, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251141789, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251142336, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251142909, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251143097, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251143313, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251143584, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251143753, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251143937, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251144224, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251144595, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251144785, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251144923, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251145189, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251145490, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645251145577, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645251145975, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645251146078, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754645251146147, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251146556, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645251146249, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645251146667, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251147186, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645251146835, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754645251147549, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251147702, "dur": 1848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251149550, "dur": 242154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251391715, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645251394042, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645251394198, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@1.2.5\\lib\\ReportGenerator\\ReportGeneratorMerged.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645251392898, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645251394361, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754645251395385, "dur": 834, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251396226, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251396476, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645251396475, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754645251396630, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645251396859, "dur": 413366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754645251810228, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645251810227, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645251810384, "dur": 1505, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754645251811892, "dur": 69323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251119938, "dur": 19177, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251139259, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4190EB41F081C965.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645251139414, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251139601, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754645251139789, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754645251140074, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754645251140191, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754645251140427, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1754645251140601, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754645251140767, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251141180, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251141331, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251141574, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251141775, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251142309, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251142482, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251143091, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251143268, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251143475, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251143645, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251143813, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251144133, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251144292, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251144474, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251144657, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251144923, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251145216, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251145488, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645251145568, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645251145722, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251146234, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754645251146555, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TextMeshProUGUI.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754645251145795, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645251146806, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251147088, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754645251147220, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251147285, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754645251147743, "dur": 1805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251149548, "dur": 242151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251391701, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645251392925, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645251394031, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645251394199, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645251395153, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645251394006, "dur": 1500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645251395506, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251396431, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754645251395563, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754645251396828, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754645251397007, "dur": 484187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251119977, "dur": 19150, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251139406, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645251139405, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_34C604C212D62B6E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645251139609, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754645251139893, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1754645251140044, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754645251140133, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.007.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754645251140329, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754645251140652, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2115181816857846961.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754645251140780, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251141153, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251141328, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251141595, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251141775, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251141968, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251142486, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251143059, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251143213, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251143401, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251143620, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251143798, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251144017, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251144243, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251144550, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251144778, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251145048, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251145210, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251145494, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645251145591, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251145972, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251146094, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RayFireAssembly.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645251146204, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645251146317, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251146834, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251146926, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754645251147046, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/OpenFracture.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251147531, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251147654, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251147707, "dur": 1831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251149538, "dur": 242159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251391699, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RuntimeAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251393115, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251394031, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645251393903, "dur": 1239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251395143, "dur": 1679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251396846, "dur": 83738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251480587, "dur": 61654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645251480585, "dur": 62429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251543767, "dur": 85, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754645251543860, "dur": 262300, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754645251810221, "dur": 70042, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645251810220, "dur": 70045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754645251880286, "dur": 846, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645251120003, "dur": 19136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251139228, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBD7613341ED5E70.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645251139424, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251139519, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645251139667, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754645251139839, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645251139961, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645251140077, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754645251140467, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RayFireEditorAssembly.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754645251140750, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251141169, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251141340, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251141544, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251141707, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251141997, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251142105, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251142212, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251142972, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251143130, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251143296, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251143610, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251143764, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251144007, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251144243, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251144391, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251144616, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251144812, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251144923, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251145191, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251145475, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645251145565, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251145935, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251146163, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251146512, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251146944, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645251147152, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.learn.iet-framework@3.1.3\\Editor\\Core\\IModel.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754645251146570, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251147513, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251147703, "dur": 1830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251149533, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754645251149608, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251149790, "dur": 241933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251391724, "dur": 1120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251392892, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251394010, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251394395, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645251394142, "dur": 1370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754645251395513, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251395913, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251396064, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251396466, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251396685, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.007.dll"}}, {"pid": 12345, "tid": 11, "ts": 1754645251396748, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251396819, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754645251396952, "dur": 484270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251120041, "dur": 19109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251139409, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251139531, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_EEF40264B201505D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645251139777, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1754645251140216, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1754645251140745, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251141121, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251141335, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251141574, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251141752, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251141959, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251142080, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251142180, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251142283, "dur": 124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251142407, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251143211, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251143414, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251143899, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251144074, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251144227, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251144372, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251144575, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251144724, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251144929, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251145199, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251145489, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645251145594, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251146073, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251146445, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645251146551, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645251146671, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\RayFire\\Plugins\\Windows\\x86_x64\\RFLib_DotNet_2018.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645251146670, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251147271, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251147497, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251147700, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645251147771, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251148016, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754645251148082, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251148541, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251148799, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251149560, "dur": 242818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251392380, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251393621, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251394395, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645251394929, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645251394263, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754645251396220, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251396652, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251396802, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.MemoryProfiler.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1754645251396859, "dur": 419187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754645251816048, "dur": 60729, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645251816047, "dur": 60733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754645251876829, "dur": 4376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251120093, "dur": 19085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251139358, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251139592, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645251139743, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754645251139917, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1754645251140074, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Tutorials.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754645251140315, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RuntimeAssembly.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645251140547, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645251140640, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16770287611300916226.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754645251140734, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251141133, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251141291, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251141529, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251141705, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251141866, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251142448, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251143004, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251143163, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251143426, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251143578, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251143732, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251143916, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251144249, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251144540, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251144718, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251144930, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251145206, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251145482, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645251145595, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251146003, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645251146096, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251146867, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251146965, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645251147106, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251147713, "dur": 1823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251149537, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754645251149622, "dur": 242088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251391711, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251393113, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251394031, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645251394169, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645251393189, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251394425, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Tutorials.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251395484, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754645251396431, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754645251395567, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754645251396893, "dur": 484314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251120136, "dur": 19071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251139357, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251139541, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754645251139897, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754645251139985, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754645251140642, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16259340576238914666.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754645251140756, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251141168, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251141371, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251141524, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251141703, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251141921, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251142436, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251143015, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251143182, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251143491, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251143846, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251144543, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251144707, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251144923, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251145202, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251145475, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645251145572, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645251146079, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251146208, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754645251146668, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Animation\\ClipCurveEditor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754645251146308, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754645251147284, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251147494, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251147704, "dur": 1835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251149539, "dur": 242155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251391695, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RayFireAssembly.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645251392919, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645251394012, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251395386, "dur": 710, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 14, "ts": 1754645251394119, "dur": 2011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754645251396131, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251396364, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251396514, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251396821, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754645251397019, "dur": 484224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645251120268, "dur": 18954, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645251139357, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645251139356, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_FEEE89EA26456A87.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645251139531, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_FEEE89EA26456A87.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645251139835, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645251140467, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645251140765, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645251142045, "dur": 373, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754645251142524, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\WorkItemFactory.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754645251143251, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754645251139903, "dur": 3620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251143562, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645251143925, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645251143659, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251145258, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251145472, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645251145529, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645251145642, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251146065, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645251146171, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754645251146313, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251147065, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645251146758, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1754645251147337, "dur": 108, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645251147833, "dur": 242600, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1754645251391702, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251394030, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 15, "ts": 1754645251392930, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Recorder.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251394318, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251395375, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754645251395522, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.007.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754645251396886, "dur": 484297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251120307, "dur": 18928, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251139433, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645251139432, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645251139601, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754645251139963, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754645251140110, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.007.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754645251140289, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754645251140491, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754645251140745, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251141193, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251141360, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251141572, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251141762, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251141925, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251142883, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251143195, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251143355, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251143570, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251143738, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251143940, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251144269, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251144561, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251144751, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251144956, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251145198, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251145472, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645251145553, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251146093, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754645251146239, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251146919, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251147415, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VSCode.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645251147487, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251147706, "dur": 1841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251149547, "dur": 242145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251391699, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251392846, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251394031, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.16\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645251392927, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251394118, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754645251395160, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\GameSoft\\Unity\\Editor\\2022.3.38f1c1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.AspNetCore.NamedPipeSupport.dll"}}, {"pid": 12345, "tid": 16, "ts": 1754645251394286, "dur": 1387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251395696, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754645251396940, "dur": 484259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754645251886680, "dur": 1665, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18540, "tid": 1059, "ts": 1754645251896660, "dur": 1264, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18540, "tid": 1059, "ts": 1754645251898003, "dur": 1383, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18540, "tid": 1059, "ts": 1754645251894476, "dur": 5359, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}