{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to activate Inactive objects \par
with Activator\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1\b Create \b0 Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [15,1,10]\line\par
{\pntext\f0 3.\tab}Create another \b Cube\b0 . \line\par
{\pntext\f0 4.\tab}Set its \b name \b0 to "\i Floor\i0 ", \b position \b0 to [0,5,0] and \b scale \b0 to [8,0.2,4]\line\par
{\pntext\f0 5.\tab}Add \b RayFire Shatter \b0 to the Floor object, in \b Voronoi \b0 properties set \b Amount \b0 to \b 300 \b0 and click the \b Fragment \b0 button. New object Floor_root will be created.\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 or \b Deactivate \b0 Floor object, we won\rquote t need it anymore.\line\par
{\pntext\f0 7.\tab}Add \b RayFire Rigid \b0 to the Floor_root object and set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 8.\tab}Set \b Simulation type \b0 to \b Inactive\b0 . \line\par
{\pntext\f0 9.\tab}Set \b Object type \b0 to \b Mesh Root\b0 .\line\par
{\pntext\f0 10.\tab}\b Create \b0 a new empty Gameobject. This will be the object which will be used to activate Inactive fragments.\line\par
{\pntext\f0 11.\tab}Set its \b name \b0 to \ldblquote\i Activator\i0\rdblquote  and \b position \b0 to [6,5,0]\line\par
{\pntext\f0 12.\tab}Add \b RayFire Activator \b0 component to Activator object.\line\par
{\pntext\f0 13.\tab}Set \b Gizmo Type \b0 to \b Sphere \b0 and \b Radius \b0 to \b 1\b0 . All fragments which will be overlapped by this gizmo will be activated.\line\line\tab In order to activate Floor fragments we need to enable Activator activation in the Rigid component.\line\par
{\pntext\f0 14.\tab}\b Select \b0 Floor_root object and in Rigid component \b Activation \b0 properties enable \b Activator\b0  property.\line\par
{\pntext\f0 15.\tab}\b Start \b0 Play Mode. \line\par
{\pntext\f0 16.\tab}\b Select \b0 the Activator object and \b start moving \b0 it over it\rquote s \b X axis \b0 towards Floor_root and pass it through.\line\line\tab While you will move the Activator you will see that it activates all fragments inside it\rquote s gizmo and they start to fall down. \line\tab In some cases you can add the RayFire Activator component to already animated or moving objects. But in some cases you may need just the simplest Activator move to trigger activation of a group of fragments.\line\par
{\pntext\f0 17.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 18.\tab}\b Select \b0 Activator, in Animation properties \f1\lang1049 e\f0\lang1033 nable Show Animation checkbox, \lang9 set \b Position Animation to By Global Position List\b0  and \b Duration \b0 to \b 5\b0  seconds.\line\par
{\pntext\f0 19.\tab}\b Click \b0 on the \b Add Position \b0 button on top of the Activator component UI. \line\line\tab Under the Add Position button you will see string \ldblquote\i Positions: 1\i0\rdblquote , here you can see how many saved positions the Activator has. All saved positions are stored in the public Position List at the bottom of the Activator component UI.\line\par
{\pntext\f0 20.\tab}\b Move \b0 Activator to \b position \b0 [-6, 7, 0]\line\par
{\pntext\f0 21.\tab}\b Click \b0 on the \b Add Position \b0 button again to save second position.\line\par
{\pntext\f0 22.\tab}\b Move \b0 Activator object away to some \b random \b0 position.\line\par
{\pntext\f0 23.\tab}\b Start \b0 Play Mode. \line\par
{\pntext\f0 24.\tab}\b Click \b0 on the \b Start \b0 button on top of the Activator component. \par

\pard\nowidctlpar\sl276\slmult1\par
\tab Activator will start moving from first position to second position. Whole animation will take 5 seconds. The same animation can be initiated by Activator public method \b TriggerAnimation(). \b0\par
\tab Notice that even though Activator was at some other position it started it\rquote s animation from first position. If you want to initiate animation from Activator\rquote s current position you need to set \b Position Animation \b0 to \b By Local Position List \ul\b0 before \ulnone you will start save positions.\par
}
 