{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish Mesh object in runtime by collision.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create Cube, this will be the cube which will be demolished in runtime, you don't need to prefragment anything, all fragments will be created in runtime. \line\par
{\pntext\f0 2.\tab}Set its name to "\i Brick\i0 ", position to [0,5,0], rotation to [30, 0, 50] and scale to [2,1,1]\line\par
{\pntext\f0 3.\tab}Remove it's Box Collider because Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "\i Ground\i0 ", position to [0,0,0] and scale to [5,1,5]\line\par
{\pntext\f0 6.\tab}Add \b RayFire Rigid \b0 component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid \b Initialization to At Start\b0 , it means that object will get all necessary components at Start. Default \b By Method \b0 type means that you will need to Initialize it first using button on top of component or public method \b Initialize()\b0 .\line\par
{\pntext\f0 8.\tab}Set \b Simulation \b0 type to \b Dynamic \b0 so the object will start to fall down immediately after Initialization.\line\par
{\pntext\f0 9.\tab}Set \b Object \b0 type to \b Mesh\b0 , it means that you are going to simulate and demolish to pieces meshfilter\rquote s mesh.\line\par
{\pntext\f0 10.\tab}Set \b Demolition \b0 type to \b Runtime\b0 . For Mesh objects it means that all slicing operations will be performed in runtime right after collision or when Demolition will be initiated in some other ways.\line\par
{\pntext\f0 11.\tab}In \b Physics \b0 properties select demolishable (non metal) material \b Brick\b0 . Keep in mind that all material properties, including demolishable state, can be edited in the \b RayFire Manager \b0 component. Different materials have different density, friction and solidity which affects simulation and demolition.\line\par
{\pntext\f0 12.\tab}In \b Limitations \b0 properties set \b Solidity \b0 to 0.1, This is Solidity multiplier which makes object less or more fragile. The less this value, the higher chance that the object will be demolished by collision.\line\par
{\pntext\f0 13.\tab}In \b Mesh Demolition \b0 properties set \b Amount \b0 to 10, this is where you can define the approximate amount of fragments you will get after the object will be demolished.\line\par
{\pntext\f0 14.\tab}Set \b Contact Bias \b0 to 0.8, this property allows to create more tiny fragments at collision point. With value 0 you will get even fragments distribution.\line\par
{\pntext\f0 15.\tab}Start Play Mode. Brick will fall down and will be demolished by collision.\line\par
{\pntext\f0 16.\tab}Turn off Play Mode. Now you can try different properties to see how they affect simulation and demolition. \line\par
{\pntext\f0 17.\tab}Set \b Solidity \b0 to 1.0 and start Play mode. This time Brick won\rquote t be demolished. \line\par
{\pntext\f0 18.\tab}Turn off Play Mode and set Bricks \b position \b0 to [0,10,0] and start Play Mode again. Brick was demolished again because this time collision strength was higher.\line\par

\pard\nowidctlpar\sl276\slmult1 In other tutorials you will learn how other properties affect objects simulation and demolition and how to demolish objects in other ways.\par
\fs24\lang1033\par
}
 