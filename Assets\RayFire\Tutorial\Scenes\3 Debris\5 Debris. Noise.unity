%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 1504295874}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &221844243
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221844244}
  - component: {fileID: 221844247}
  - component: {fileID: 221844246}
  - component: {fileID: 221844245}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221844244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221844243}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 910653997}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &221844245
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221844243}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &221844246
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221844243}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &221844247
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221844243}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &249418061
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 249418062}
  - component: {fileID: 249418066}
  - component: {fileID: 249418065}
  - component: {fileID: 249418064}
  - component: {fileID: 249418063}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &249418062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249418061}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18.78, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 910653997}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &249418063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249418061}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 15
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 1
    sizeMax: 3
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0
    velocityMax: 0
    gravityMin: 0.1
    gravityMax: 0.2
    rotationSpeed: 0
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.4
    strengthMax: 0.7
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &249418064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249418061}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 249418062}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 249418066}
  meshRenderer: {fileID: 249418065}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &249418065
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249418061}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &249418066
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 249418061}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &303668957
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 303668958}
  - component: {fileID: 303668961}
  - component: {fileID: 303668960}
  - component: {fileID: 303668959}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &303668958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303668957}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1723015002}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &303668959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303668957}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &303668960
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303668957}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &303668961
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303668957}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &330654811
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 330654812}
  - component: {fileID: 330654815}
  - component: {fileID: 330654814}
  - component: {fileID: 330654813}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &330654812
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330654811}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1289028724}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &330654813
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330654811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &330654814
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330654811}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &330654815
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330654811}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &495159927
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 495159928}
  - component: {fileID: 495159932}
  - component: {fileID: 495159931}
  - component: {fileID: 495159930}
  - component: {fileID: 495159929}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &495159928
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 495159927}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18.78, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1723015002}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &495159929
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 495159927}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 15
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 1
    sizeMax: 3
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0
    velocityMax: 0
    gravityMin: 0.1
    gravityMax: 0.2
    rotationSpeed: 0
  noise:
    enabled: 1
    quality: 2
    strengthMin: 1.1
    strengthMax: 1.2
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &495159930
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 495159927}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 495159928}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 495159932}
  meshRenderer: {fileID: 495159931}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &495159931
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 495159927}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &495159932
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 495159927}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &504282429
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 504282430}
  - component: {fileID: 504282433}
  - component: {fileID: 504282432}
  - component: {fileID: 504282431}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &504282430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504282429}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1060764843}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &504282431
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504282429}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &504282432
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504282429}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &504282433
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504282429}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &518704684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 518704685}
  - component: {fileID: 518704689}
  - component: {fileID: 518704688}
  - component: {fileID: 518704687}
  - component: {fileID: 518704686}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &518704685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518704684}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1060764843}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &518704686
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518704684}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 1
    sizeMax: 3
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0
    velocityMax: 0
    gravityMin: 0.1
    gravityMax: 0.2
    rotationSpeed: 0
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.5
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &518704687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518704684}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 518704685}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 518704689}
  meshRenderer: {fileID: 518704688}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &518704688
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518704684}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &518704689
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518704684}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &649428062
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 649428063}
  m_Layer: 0
  m_Name: State
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &649428063
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 649428062}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1520001136}
  - {fileID: 910653997}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &758222088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 758222089}
  m_Layer: 0
  m_Name: Frequency
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &758222089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 758222088}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1060764843}
  - {fileID: 1874830506}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &835069012
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 835069013}
  - component: {fileID: 835069016}
  - component: {fileID: 835069015}
  - component: {fileID: 835069014}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &835069013
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835069012}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1874830506}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &835069014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835069012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &835069015
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835069012}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &835069016
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835069012}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &853705575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 853705576}
  m_Layer: 0
  m_Name: Strength
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &853705576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 853705575}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1289028724}
  - {fileID: 1723015002}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &910653996
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 910653997}
  m_Layer: 0
  m_Name: Enabled
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &910653997
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910653996}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 249418062}
  - {fileID: 221844244}
  m_Father: {fileID: 649428063}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1060764842
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1060764843}
  m_Layer: 0
  m_Name: 0.3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1060764843
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060764842}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 518704685}
  - {fileID: 504282430}
  m_Father: {fileID: 758222089}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1131675111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1131675112}
  - component: {fileID: 1131675115}
  - component: {fileID: 1131675114}
  - component: {fileID: 1131675113}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1131675112
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131675111}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1520001136}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1131675113
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131675111}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1131675114
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131675111}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1131675115
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131675111}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &1289028723
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1289028724}
  m_Layer: 0
  m_Name: 0.2-0.3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1289028724
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1289028723}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1300557703}
  - {fileID: 330654812}
  m_Father: {fileID: 853705576}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1300557702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1300557703}
  - component: {fileID: 1300557707}
  - component: {fileID: 1300557706}
  - component: {fileID: 1300557705}
  - component: {fileID: 1300557704}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1300557703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1300557702}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1289028724}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1300557704
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1300557702}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 100
    burstVar: 5
    distanceRate: 5
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 1
    sizeMax: 3
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0
    velocityMax: 0
    gravityMin: 0.1
    gravityMax: 0.2
    rotationSpeed: 0
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.2
    strengthMax: 0.3
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1300557705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1300557702}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1300557703}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1300557707}
  meshRenderer: {fileID: 1300557706}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1300557706
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1300557702}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1300557707
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1300557702}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1001 &1445489124
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1014529264720340, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1026965256934996, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1260722613888276, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1359285263087640, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1736276488727964, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalScale.x
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 16.36
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 35.18
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -43.55
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.995008
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.099795856
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 11.455001
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -40.18
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!850595691 &1504295874
LightingSettings:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Settings.lighting
  serializedVersion: 3
  m_GIWorkflowMode: 0
  m_EnableBakedLightmaps: 0
  m_EnableRealtimeLightmaps: 1
  m_RealtimeEnvironmentLighting: 1
  m_BounceScale: 1
  m_AlbedoBoost: 1
  m_IndirectOutputScale: 1
  m_UsingShadowmask: 1
  m_BakeBackend: 1
  m_LightmapMaxSize: 512
  m_BakeResolution: 10
  m_Padding: 2
  m_TextureCompression: 1
  m_AO: 0
  m_AOMaxDistance: 1
  m_CompAOExponent: 1
  m_CompAOExponentDirect: 0
  m_ExtractAO: 0
  m_MixedBakeMode: 2
  m_LightmapsBakeMode: 1
  m_FilterMode: 1
  m_LightmapParameters: {fileID: 15204, guid: 0000000000000000f000000000000000, type: 0}
  m_ExportTrainingData: 0
  m_TrainingDataDestination: TrainingData
  m_RealtimeResolution: 2
  m_ForceWhiteAlbedo: 0
  m_ForceUpdates: 0
  m_FinalGather: 0
  m_FinalGatherRayCount: 256
  m_FinalGatherFiltering: 1
  m_PVRCulling: 1
  m_PVRSampling: 1
  m_PVRDirectSampleCount: 32
  m_PVRSampleCount: 256
  m_PVREnvironmentSampleCount: 256
  m_PVREnvironmentReferencePointCount: 2048
  m_LightProbeSampleCountMultiplier: 4
  m_PVRBounces: 2
  m_PVRMinBounces: 2
  m_PVREnvironmentMIS: 0
  m_PVRFilteringMode: 2
  m_PVRDenoiserTypeDirect: 0
  m_PVRDenoiserTypeIndirect: 0
  m_PVRDenoiserTypeAO: 0
  m_PVRFilterTypeDirect: 0
  m_PVRFilterTypeIndirect: 0
  m_PVRFilterTypeAO: 0
  m_PVRFilteringGaussRadiusDirect: 1
  m_PVRFilteringGaussRadiusIndirect: 5
  m_PVRFilteringGaussRadiusAO: 2
  m_PVRFilteringAtrousPositionSigmaDirect: 0.5
  m_PVRFilteringAtrousPositionSigmaIndirect: 2
  m_PVRFilteringAtrousPositionSigmaAO: 1
--- !u!1 &1520001135
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1520001136}
  m_Layer: 0
  m_Name: Disabled
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1520001136
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1520001135}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2072862523}
  - {fileID: 1131675112}
  m_Father: {fileID: 649428063}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1723015001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1723015002}
  m_Layer: 0
  m_Name: 1.1-1.2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1723015002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1723015001}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 495159928}
  - {fileID: 303668958}
  m_Father: {fileID: 853705576}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1874830505
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1874830506}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1874830506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1874830505}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1911165705}
  - {fileID: 835069013}
  m_Father: {fileID: 758222089}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1911165704
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1911165705}
  - component: {fileID: 1911165709}
  - component: {fileID: 1911165708}
  - component: {fileID: 1911165707}
  - component: {fileID: 1911165706}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1911165705
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1911165704}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18.78, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1874830506}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1911165706
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1911165704}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 1
    sizeMax: 3
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0
    velocityMax: 0
    gravityMin: 0.1
    gravityMax: 0.2
    rotationSpeed: 0
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.5
    strengthMax: 0.6
    frequency: 1
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1911165707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1911165704}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1911165705}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1911165709}
  meshRenderer: {fileID: 1911165708}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1911165708
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1911165704}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1911165709
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1911165704}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &2072862522
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2072862523}
  - component: {fileID: 2072862527}
  - component: {fileID: 2072862526}
  - component: {fileID: 2072862525}
  - component: {fileID: 2072862524}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2072862523
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2072862522}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1520001136}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2072862524
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2072862522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 15
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 1
    sizeMax: 3
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0
    velocityMax: 0
    gravityMin: 0.1
    gravityMax: 0.2
    rotationSpeed: 0
  noise:
    enabled: 0
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &2072862525
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2072862522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 2072862523}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 2072862527}
  meshRenderer: {fileID: 2072862526}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2072862526
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2072862522}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2072862527
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2072862522}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Noise
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 649428063}
  - {fileID: 853705576}
  - {fileID: 758222089}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
