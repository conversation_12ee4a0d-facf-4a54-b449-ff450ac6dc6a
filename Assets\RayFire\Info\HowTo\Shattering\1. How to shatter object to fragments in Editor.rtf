{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to shatter object to \par
fragments in Editor\ulnone\b0\fs22\par

\pard\nowidctlpar\sl276\slmult1\par
To shatter objects to fragments in the Editor you should use RayFire Shatter component.\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be shattered to multiple fragments. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Wall\i0 ", \b position \b0 to [0,0,0], and \b scale \b0 to [10,10,1]\line\par
{\pntext\f0 3.\tab}Add \b RayFire Shatter \b0 component to Wall and set \b Type \b0 to \b Voronoi\b0 . Voronoi fragmentation type allows to create convex, physics friendly fragments. It is based on automatically generated point cloud, every point represents one fragment. \line\par
{\pntext\f0 4.\tab}In \b Scene Window\b0  Set \b Shading Mode \b0 to \b Shaded Wireframe \b0 to see fragment's edges when they will be created.\line\par
{\pntext\f0 5.\tab}Click on the \b Fragment \b0 button. A new gameobject \ldblquote\i Wall_root\i0\rdblquote  with fragments as it\rquote s children will be created. Right now it is hard to see the actual fragment's shapes because of a lot of edges. Also, the original object overlaps all fragment's by it's mesh.\line\par
{\pntext\f0 6.\tab}In the Shatter \b Preview \b0 section toggle \b ON Color \b0 button. Now you can see every fragment separately. \line\par
{\pntext\f0 7.\tab}\b Toggle Color \b0 button \b OFF.\b0\line\line Lets say we want to get more fragments.\line\par
{\pntext\f0 8.\tab}\b Click \b0 on \b Delete Last \b0 button under Fragment button. Wall_root gameobject with all fragments will be destroyed.\line\par
{\pntext\f0 9.\tab}In \b Voronoi \b0 properties set \b Amount \b0 to \b 100\b0 . This property defines the amount of points in a point cloud. Since some points could be outside of the mesh final amount of fragments probably will be lower than Amount value.\line\par
{\pntext\f0 10.\tab}Click on the \b Fragment \b0 button.\line\par
{\pntext\f0 11.\tab}In the Shatter \b Preview \b0 section \b toggle ON Scale \b0 button (it is ON by default). \line\par
{\pntext\f0 12.\tab}Start to drag \b Scale Preview \b0 slider from the \b left \b0 to the \b right\b0 . You will see that the original object's MeshRender disabled so it won't cover fragments and fagment's scale is getting lower so it is possible to see every fragment's shape and inner surface.\line\par
{\pntext\f0 13.\tab}\b Toggle OFF Scale \b0 button to reset fragment's scale and enable original object's MeshRender component.\line\par
}
 