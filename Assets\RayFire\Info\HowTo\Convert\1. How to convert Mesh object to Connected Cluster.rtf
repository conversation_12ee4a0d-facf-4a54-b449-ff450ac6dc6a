{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to convert Mesh object to Connected Cluster.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par
\lang1033 I\lang9 f you want to use Connected Cluster setup you should prefragment object, export fragment meshes into asset file, then setup Rigid component, use Editor Setup to cache all connections and finally create prefab. Depends on amount of fragments, Prefab and Mesh assets size can be too big for your game. \par
\par
In order to avoid creating prefab and mesh assets but still use Connected Cluster setup you can use Convert feature. It allows to demolish mesh object to fragments, but then automatically convert these fragments to Connected Cluster setup. In this case you will not need to store heavy asset files, but in other hand, you will need to fragment and cache all connections every time.\par
\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , \lang1033 s\lang9 et its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [15,1,10]\line\par
{\pntext\f0 2.\tab}Create another \b Cube\b0 , \lang1033 s\lang9 et its \b name \b0 to "\i Pillar\i0 ", \b position \b0 to [0,5.5,0] and \b scale \b0 to [1,10,1]\line\par
{\pntext\f0 3.\tab}Add \b RayFire Rigid \b0 component to Pillar.\line\par
{\pntext\f0 4.\tab}Set Rigid \b Initialization \b0 to \b At Start.\b0\line\par
{\pntext\f0 5.\tab}Set \b Demolition \b0 type to \b Runtime\b0 .\line\par
{\pntext\f0 6.\tab}In \b Mesh Demolition \b0 properties set \b Amount \b0 to 100.\line\par
{\pntext\f0 7.\tab}In \b Damage \b0 properties, toggle On \b Enable \b0 property and set \b Max Damage \b0 to 10\lang1033 .\lang9  Damage feature should be enabled so it will be possible to demolish Pillar object using Gun component.\line\par
{\pntext\f0 8.\tab}Create \b Cylinder\b0 , \lang1033 s\lang9 et its \b name \b0 to "\i Gun\i0 ", \b position \b0 to [0,5,-10] and \b rotation \b0 to [90,0,0]\line\par
{\pntext\f0 9.\tab}Add \b RayFire Gun \b0 component to Gun object and set \b Axis \b0 property to \b Y Green, \b0 Impact \b Strength \b0 to 10 and \b Radius \b0 to 2. Disable \b Show Impact \b0 property.\line\par
{\pntext\f0 10.\tab}\b Start \b0 Play Mode. \line\par
{\pntext\f0 11.\tab}\b Select \b0 Gun object and click on \b Single Shot \b0 button on top of \b Rayfire Gun \b0 component.\line\line Pillar will be demolished to fragments, and they will crumble to the ground.\line\par
{\pntext\f0 12.\tab}\b Turn off \b0 Play Mode. \line\par
{\pntext\f0 13.\tab}\b Select \b0 Pillar object and in \b Mesh Demolition\b0  properties set \b Convert \b0 property to \b Connected Cluster.\b0  \line\line You may notice that Cluster Demolition properties was revealed under Mesh Demolition properties. In Cluster Demolition properties you can set properties for converted Connected Cluster, but for now let's use default properties.\line\par
{\pntext\f0 14.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 15.\tab}\b Select \b0 Gun object and click on \b Single Shot \b0 button.\line  \line This time Mesh object will be demolished to fragments, but instead of separately simulated fragments you will get one Connected Cluster which will be instantly demolished at the point where original mesh object was shot. You can pause play mode right after shot to select bottom and upper Connected Clusters, which were created when converted Connected Cluster was demolished.\line\line Depends on platform, you may get FPS drop at demolition, because there are too many heavy calculations\lang1033 : original mesh fragmentation and connection establishment among fragments. In order to avoid such FPS drop, you can use Awake Prefragment demolition type instead of Runtime demolition type.\lang9\line\par
{\pntext\f0 16.\tab}\b Turn off \b0 Play Mode. \line\par
{\pntext\f0 17.\tab}\b Select \b0 Pillar object and set \b Demolition Type \b0 property to \b Awake Prefragment\b0 .\line\par
{\pntext\f0 18.\tab}\b Start \b0 Play Mode.\fs24\lang1033\line\fs22\lang9  \fs24\lang1033\par
{\pntext\f0 19.\tab}\fs22\lang9 In \b Hierarchy \b0 window, reveal children of \b RayFireMan \b0 object, then reveal children of \b Storage_Fragments \b0 object. \fs24\lang1033\line\line You can see that deactivated Connected Cluster Pillar_root object with all fragments and cached connections is already there, waiting to be replaced with original object.\line\par
{\pntext\f0 20.\tab}\b\fs22\lang9 Select \b0 Gun object and click on \b Single Shot \b0 button.\fs24\lang1033\line\line This time, when you make a shot, original object swaps with deactivated Connected Cluster and there is no FPS drop because all calculation were performed at Start.\line\par
{\pntext\f0 21.\tab}\b\fs22\lang9 Turn off \b0 Play Mode. \fs24\lang1033\line\line\f1\lang1049 L\f0\lang1033 et's say you want to attach pillar to ceiling, so some fragments will stay attached to ceiling after demolition.\line\par
{\pntext\f0 22.\tab}\b\fs22\lang9 Select \b0 Pillar and in \b Rayfire\b0  \b Rigid \b0 component set \b Simulation Type \b0 to Kinematik\b .\b0\fs24\lang1033\line\line\fs22 Now Pillar object will start as \b Kinematik\b0 , then it will be replaced by Connected Cluster, but after cluster demolition all fragments and lesser clusters will be Dynamic because default \b Cluster Sim Type \b0 property value in Cluster Demolition is \b Dynamic\b0 . \line\par
{\pntext\f0 23.\tab}\b\lang9 Add Rayfire Unyielding \b0 component, \b Size \b0 to [1,0.1,1] and \b Center \b0 to [0,0.45,0] so it will overlap upper fragments by its gizmo. Set Simulation Type to \b Kinematik\b0 .\lang1033\line\par
{\pntext\f0 24.\tab}\b\lang9 Start \b0 Play Mode.\fs24\lang1033\line\par
{\pntext\f0 25.\tab}\b\fs22\lang9 Select \b0 Gun object and click on \b Single Shot \b0 button.\fs24\lang1033\line\line\fs22\lang9 This time Gun will shoot out fragments at the middle of Pillar object but fragments on top will stay. You can move Gun object upper and make more shots. Notice that fragments with red connections, defined by Unyielding gizmo, can not be demolished.\fs24\lang1033  \fs22\lang9 This behaviour can be changed in \b Rayfire Unyielding \b0 component by enabling \b Activatable \b0 property. In this case connections will be purple and it will be possible to shoot even unyielding fragments.\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
}
 