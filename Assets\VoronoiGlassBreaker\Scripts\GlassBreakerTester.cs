using UnityEditor;
using UnityEngine;

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Test script for the Voronoi Glass Breaker system
    /// Provides various ways to test the breaking functionality
    /// </summary>
    public class GlassBreakerTester : MonoBehaviour
    {
        [Header("Test Settings")]
        public VoronoiGlassBreaker glassBreaker;
        public bool enableMouseBreaking = true;
        public bool enableKeyboardBreaking = true;
        public float testImpactForce = 15f;
        
        [Header("Projectile Testing")]
        public GameObject projectilePrefab;
        public Transform projectileSpawnPoint;
        public float projectileForce = 500f;
        public float projectileLifetime = 5f;
        
        [Header("UI")]
        public KeyCode breakKey = KeyCode.Space;
        public KeyCode repairKey = KeyCode.R;
        public KeyCode shootKey = KeyCode.F;
        
        private Camera playerCamera;
        
        private void Start()
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                playerCamera = FindObjectOfType<Camera>();
            }
            
            if (glassBreaker == null)
            {
                glassBreaker = FindObjectOfType<VoronoiGlassBreaker>();
            }
            
            if (projectileSpawnPoint == null && playerCamera != null)
            {
                projectileSpawnPoint = playerCamera.transform;
            }
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        private void HandleInput()
        {
            if (glassBreaker == null) return;
            
            // Mouse breaking
            if (enableMouseBreaking && Input.GetMouseButtonDown(0))
            {
                BreakAtMousePosition();
            }
            
            // Keyboard breaking
            if (enableKeyboardBreaking && Input.GetKeyDown(breakKey))
            {
                BreakAtRandomPosition();
            }
            
            // Repair glass
            if (Input.GetKeyDown(repairKey))
            {
                RepairGlass();
            }
            
            // Shoot projectile
            if (Input.GetKeyDown(shootKey))
            {
                ShootProjectile();
            }
        }
        
        private void BreakAtMousePosition()
        {
            if (playerCamera == null) return;
            
            Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit))
            {
                if (hit.collider.gameObject == glassBreaker.gameObject)
                {
                    glassBreaker.BreakGlass(hit.point, testImpactForce);
                    Debug.Log($"Breaking glass at mouse position: {hit.point}");
                }
            }
        }
        
        private void BreakAtRandomPosition()
        {
            if (glassBreaker == null) return;
            
            // Get a random point on the glass surface
            Bounds bounds = glassBreaker.GetComponent<MeshRenderer>().bounds;
            Vector3 randomPoint = new Vector3(
                Random.Range(bounds.min.x, bounds.max.x),
                bounds.center.y,
                Random.Range(bounds.min.z, bounds.max.z)
            );
            
            glassBreaker.BreakGlass(randomPoint, testImpactForce);
            Debug.Log($"Breaking glass at random position: {randomPoint}");
        }
        
        private void RepairGlass()
        {
            if (glassBreaker == null) return;
            
            glassBreaker.RepairGlass();
            Debug.Log("Repairing glass");
        }
        
        private void ShootProjectile()
        {
            if (projectilePrefab == null || projectileSpawnPoint == null) return;
            
            GameObject projectile = Instantiate(projectilePrefab, projectileSpawnPoint.position, projectileSpawnPoint.rotation);
            
            // Add rigidbody if not present
            Rigidbody rb = projectile.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = projectile.AddComponent<Rigidbody>();
            }
            
            // Add collider if not present
            Collider col = projectile.GetComponent<Collider>();
            if (col == null)
            {
                col = projectile.AddComponent<SphereCollider>();
            }
            
            // Set up projectile
            rb.mass = 0.5f;
            projectile.tag = "Breakable"; // Make sure it can break glass
            
            // Launch projectile
            Vector3 direction = projectileSpawnPoint.forward;
            rb.AddForce(direction * projectileForce);
            
            // Destroy projectile after lifetime
            Destroy(projectile, projectileLifetime);
            
            Debug.Log("Projectile fired!");
        }
        
        private void OnGUI()
        {
            if (glassBreaker == null) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Voronoi Glass Breaker Test", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
            GUILayout.Space(10);
            
            GUILayout.Label($"Glass Status: {(glassBreaker.IsFullyBroken ? "Fully Broken" : glassBreaker.IsBreaking ? "Breaking..." : "Intact")}");
            GUILayout.Label($"Breakage: {(glassBreaker.GetBreakagePercentage() * 100f):F1}%");
            GUILayout.Space(10);
            
            GUILayout.Label("Controls:");
            GUILayout.Label($"• Left Click: Break at mouse position");
            GUILayout.Label($"• {breakKey}: Break at random position");
            GUILayout.Label($"• {repairKey}: Repair glass");
            GUILayout.Label($"• {shootKey}: Shoot projectile");
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Break at Random Position"))
            {
                BreakAtRandomPosition();
            }
            
            if (GUILayout.Button("Repair Glass"))
            {
                RepairGlass();
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
