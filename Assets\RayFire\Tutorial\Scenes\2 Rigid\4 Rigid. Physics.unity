%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!4 &45393424 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 79267235}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &49037700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 49037701}
  m_Layer: 0
  m_Name: DensityMass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &49037701
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49037700}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -26.71, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 239092677}
  - {fileID: 951333208}
  - {fileID: 516802410}
  - {fileID: 695158703}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &79267235
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1258667093}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Box
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 6.01
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.6
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.38268322
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9238797
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -45.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.colliderType
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!4 &111551738 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 180833055}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &171664270
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 228813817}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Ice
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.45
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 10.85
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.14
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.38268322
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9238797
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -45.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: limitations.solidity
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: initialization
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.materialType
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: demolitionType
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1001 &180833055
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 301319673}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Ice
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.14999962
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 6.44
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.48
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.49999917
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8660259
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 60.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.materialType
      value: 8
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1 &228813816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 228813817}
  m_Layer: 0
  m_Name: Destructible
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &228813817
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 228813816}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8.8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1549092080}
  - {fileID: 1809800648}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &233932361
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1258667093}
    m_Modifications:
    - target: {fileID: 1194623286426632, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_Name
      value: Mesh
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.1700003
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.y
      value: 5.96
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.46
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.9659252
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.25882143
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -150
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.x
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.y
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.z
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 114137721341641136, guid: a8fcd1926b0a4d442a1169ceff273537,
        type: 3}
      propertyPath: demolitionType
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114137721341641136, guid: a8fcd1926b0a4d442a1169ceff273537,
        type: 3}
      propertyPath: initialization
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
--- !u!1 &239092676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 239092677}
  - component: {fileID: 239092680}
  - component: {fileID: 239092679}
  - component: {fileID: 239092678}
  m_Layer: 0
  m_Name: HeavyMetal
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &239092677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 239092676}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.04, y: 9.25, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 49037701}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &239092678
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 239092676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 1
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 0
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &239092679
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 239092676}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &239092680
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 239092676}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &301319672
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 301319673}
  m_Layer: 0
  m_Name: Friction
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &301319673
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 301319672}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -12.37, y: 0, z: -2.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 641973286}
  - {fileID: 1489040520}
  - {fileID: 111551738}
  - {fileID: 1214262150}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &516802409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 516802410}
  - component: {fileID: 516802413}
  - component: {fileID: 516802412}
  - component: {fileID: 516802411}
  m_Layer: 0
  m_Name: WoodBig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &516802410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516802409}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.1000004, y: 9.25, z: 0}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_Children: []
  m_Father: {fileID: 49037701}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &516802411
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516802409}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 1
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 9
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &516802412
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516802409}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &516802413
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 516802409}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &633027374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 633027375}
  m_Layer: 0
  m_Name: Solidity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &633027375
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 633027374}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.4999995, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2036068916}
  - {fileID: 1633766440}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &641973285 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 914955908}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &641973286 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 914955908}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &641973287
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 641973285}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 4
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 6
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &695158702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 695158703}
  - component: {fileID: 695158706}
  - component: {fileID: 695158705}
  - component: {fileID: 695158704}
  m_Layer: 0
  m_Name: Custom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &695158703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 695158702}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 5.1, y: 9.25, z: 0}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_Children: []
  m_Father: {fileID: 49037701}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &695158704
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 695158702}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 1
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 9
    material: {fileID: 0}
    massBy: 1
    mass: 5
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &695158705
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 695158702}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &695158706
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 695158702}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &745413196
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 633027375}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Ice
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.4500003
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 9.68
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.1400001
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.3318772
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9433226
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -38.766003
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: initialization
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: demolitionType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.materialType
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: limitations.solidity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: limitations.time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: meshDemolition.amount
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1001 &914955908
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 301319673}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Plate
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.44
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.57
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.49999917
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8660259
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 60.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 23093967442453408, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1 &951333207
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 951333208}
  - component: {fileID: 951333211}
  - component: {fileID: 951333210}
  - component: {fileID: 951333209}
  m_Layer: 0
  m_Name: WoodSmall
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &951333208
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 951333207}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.30000114, y: 9.25, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 49037701}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &951333209
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 951333207}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 1
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 9
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &951333210
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 951333207}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &951333211
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 951333207}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &1194339413
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 633027375}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: DenseRock
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.6500001
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 9.68
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.13999987
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.3318772
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9433226
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -38.766003
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: initialization
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: demolitionType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.materialType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: limitations.solidity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: limitations.time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: meshDemolition.amount
      value: 30
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1001 &1194808239
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 301319673}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Custom
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.3499997
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 6.44
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.48
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.49999917
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8660259
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 60.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.0000002
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.0000002
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.material
      value: 
      objectReference: {fileID: 13400000, guid: 8e417de65e38c4644ad6f6dd202f04fd,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!4 &1214262150 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1194808239}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1256760940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1256760941}
  - component: {fileID: 1256760944}
  - component: {fileID: 1256760943}
  - component: {fileID: 1256760942}
  m_Layer: 0
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1256760941
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1256760940}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.0299997, y: 5.96, z: 1.46}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1258667093}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1256760942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1256760940}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 70
    variation: 0
    depthFade: 0.5
    contactBias: 0.848
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1256760943
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1256760940}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1256760944
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1256760940}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1258667092
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1258667093}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1258667093
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1258667092}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 20.3, y: 2.3, z: 3.6}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1436928573}
  - {fileID: 45393424}
  - {fileID: 1256760941}
  - {fileID: 1269478047}
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1269478047 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537,
    type: 3}
  m_PrefabInstance: {fileID: 233932361}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1358712781
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 228813817}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: LightMetal
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.65
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 10.85
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.13999987
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.38268322
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9238797
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -45.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: physics.materialType
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: initialization
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: limitations.solidity
      value: 0.09
      objectReference: {fileID: 0}
    - target: {fileID: 114211121995268436, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: demolitionType
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1 &1436928572 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1439711967}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1436928573 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1439711967}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1436928574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1436928572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 4
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 6
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1001 &1439711967
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1258667093}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Plate
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.44
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.49999917
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8660259
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 60.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 23093967442453408, guid: 757eb8714409c024aabfc2c2827116c3,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1001 &1445489124
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4150519914291712, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.16363072
      objectReference: {fileID: 0}
    - target: {fileID: 4150519914291712, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 113.67999
      objectReference: {fileID: 0}
    - target: {fileID: 4150519914291712, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 6.915641
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalScale.x
      value: 60
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalScale.z
      value: 40
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -22.3
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 9.783629
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.055917617
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99843544
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 6.4110003
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.2
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4701658560748982, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.16363072
      objectReference: {fileID: 0}
    - target: {fileID: 4701658560748982, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -79.320015
      objectReference: {fileID: 0}
    - target: {fileID: 4701658560748982, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 6.9336443
      objectReference: {fileID: 0}
    - target: {fileID: 4860475029700398, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.11362958
      objectReference: {fileID: 0}
    - target: {fileID: 4860475029700398, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 18.779999
      objectReference: {fileID: 0}
    - target: {fileID: 4860475029700398, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: -208.16635
      objectReference: {fileID: 0}
    - target: {fileID: 4987217415896802, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.11362958
      objectReference: {fileID: 0}
    - target: {fileID: 4987217415896802, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 18.779991
      objectReference: {fileID: 0}
    - target: {fileID: 4987217415896802, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 223.23364
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!4 &1489040520 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1780210528}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1549092080 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1358712781}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1577974089
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1577974090}
  m_Layer: 0
  m_Name: Bounciness
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1577974090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577974089}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -5, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1970492362}
  - {fileID: 2117988175}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1633766440 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 745413196}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1780210528
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 301319673}
    m_Modifications:
    - target: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_Name
      value: Concrete
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.61
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 6.44
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.48
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.49999917
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8660259
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 60.000004
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!4 &1809800648 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 171664270}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1970492361
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1970492362}
  - component: {fileID: 1970492365}
  - component: {fileID: 1970492364}
  - component: {fileID: 1970492363}
  m_Layer: 0
  m_Name: HeavyMetal
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1970492362
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1970492361}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 8.85, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1577974090}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1970492363
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1970492361}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 0
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1970492364
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1970492361}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1970492365
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1970492361}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &2036068916 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1194339413}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: PhysicMaterial
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 5.5, y: 1.6999989, z: 3.6000004}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 49037701}
  - {fileID: 301319673}
  - {fileID: 1577974090}
  - {fileID: 633027375}
  - {fileID: 228813817}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2117988174
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2117988175}
  - component: {fileID: 2117988178}
  - component: {fileID: 2117988177}
  - component: {fileID: 2117988176}
  m_Layer: 0
  m_Name: Rubber
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2117988175
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117988174}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.5, y: 8.85, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1577974090}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2117988176
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117988174}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 7
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 2
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2117988177
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117988174}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2117988178
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117988174}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
