{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to collapse.\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0 ,0, 0] and \b scale \b0 to [15, 1, 10]\line\par
{\pntext\f0 3.\tab}Create \b Cylinder\b0 . \line\par
{\pntext\f0 4.\tab}Set its \b position \b0 to [0, 8.5, 0] and \b scale \b0 to [2, 8, 2]\line\par
{\pntext\f0 5.\tab}Add \b RayFire Shatter \b0 to the Cylinder object, in \b Voronoi \b0 properties set \b Amount \b0 to \b 500\b0  and click the \b Fragment \b0 button. New object Cylinder_root will be created.\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 or \b Deactivate \b0 Cylinder object, we won\rquote t need it anymore.\line\par
{\pntext\f0 7.\tab}\b Select \b0 Cylinder_root  object and set its \b name \b0 to "Pillar".\line\par
{\pntext\f0 8.\tab}Add \b RayFire Rigid \b0 to the Pillar object and set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 9.\tab}Set \b Simulation type \b0 to \b Inactive\b0 . \line\par
{\pntext\f0 10.\tab}Set \b Object type \b0 to \b Mesh Root\b0 .\line\par
{\pntext\f0 11.\tab}In \b Activation \b0 properties set \b Offset \b0 property to \b 0.2 \b0 and enable \b Connectivity \b0 property.\line\par
{\pntext\f0 12.\tab}Add \b RayFire Connectivity \b0 component to Pillar object.\line\par
{\pntext\f0 13.\tab}In the Connectivity component set \b Connectivity Type \b0 to \b By \f1\lang1049 T\f0\lang1033 riangles\b0\lang9 . Such connectivty type should be set only for fragments which share the same faces and stay close to each other.\line\par
{\pntext\f0 14.\tab}Add \b RayFire Unyielding \b0 component to Pillar object. It is \b very important \b0 to define Unyielding fragments.\line\par
{\pntext\f0 15.\tab}In the RayFire Unyielding component set \b Center \b0 to [0,-8,0] and Size to [3,1,3] to overlap fragments at the bottom of Pillar.\line\par
{\pntext\f0 16.\tab}In Scene view set Shading mode to Shaded.\line\par
{\pntext\f0 17.\tab}In Scene view set Shading mode to Shaded and disable Selection Wire in Gizmos. These two steps are not critical to do but it is just easier to see all details during collapse with such setup.\line\par
{\pntext\f0 18.\tab}\b Start \b0 Play Mode. \line\par
{\pntext\f0 19.\tab}In the \b RayFire Connectivity \b0 component start \b very slowly \b0 drag \b By Area \b0 slider in Collapse section \b on top \b0 of component UI.\line\line\tab You should notice that while you move the slider Connectivity structure starts to lose green connections. Every time it loses connection it makes a connectivity check and activates all fragments which have no any connection anymore or not connected to at least one Unyielding fragment at the bottom of the Pillar.\line\tab Keep in mind that after the fragment activates By Collapse it starts to fall down and push other Inactive fragments which at some point also activate because of By Offset activation and start to push other Inactive objects. So, in some cases it is enough just to initiate a starting collapse which will continue on it\rquote s own after some time.\line\tab In order to initiate collapse in your code you can use public static method in RFCollapse class:\line\line RFCollapse.AreaCollapse (\b RayfireConnectivity \b0 connectivity, \b float \b0 areaValue)\line RFCollapse.AreaCollapse (\b RayfireConnectivity \b0 connectivity, \b int \b0 areaPercentage)\line RFCollapse.SizeCollapse (\b RayfireConnectivity \b0 connectivity, \b float \b0 sizeValue)\line RFCollapse.SizeCollapse (\b RayfireConnectivity \b0 connectivity, \b int \b0 sizePercentage)\line RFCollapse.RandomCollapse (\b RayfireConnectivity \b0 connectivity, \b int \b0 randomPercentage, \b int \b0 seedValue)\line\line For instance, \line\tab RFCollapse.SizeCollapse (\b RayfireConnectivity \b0 connectivity, \b float \b0 sizeValue) method will activate all shards with bounding box size less than sizeValue. \line\tab RFCollapse.SizeCollapse (\b RayfireConnectivity \b0 connectivity, \b int \b0 sizePercentage) method instead of float value takes integer value as input, in this case input value 20 means than 20 percent of the smallest fragment will be activated. If nex time you will input value 30 then another 10 percents of te smalles fragments will be activated.\line\tab AreaCollapse methods works in the same way, except that this method destroys connections, so fragment will be activated only if it has no any connections with other fragments.\line\tab RandomCollapse takes only integer value as input and activates random percentage of fragments depends on input seed value, so you can get the same random activations every time which may be useful for coop games.\line\par
{\pntext\f0 20.\tab}\b Turn Off \b0 play Mode.\line\par
{\pntext\f0 21.\tab}In the \b Connectivity \b0 component set \b Collapse Initiate \b0 property to \b By Method\b0 .\line\par
{\pntext\f0 22.\tab}Open \b Collapse \b0 properties and set \b Type \b0 to \b By Area\b0 . \line\par
{\pntext\f0 23.\tab}Set \b Start \b0 property to 0 and \b End \b0 property to 15.\line\par
{\pntext\f0 24.\tab}Set \b Steps \b0 property to 25.\line\par
{\pntext\f0 25.\tab}Set \b Duration \b0 property to 25.\line\line\tab When you initialize Connectivity and establish all connections among fragments and cache shared area among every fragment. When you initiate Area Collapse you need to input \b float \b0 or \b integer \b0 value. In case you input float value then Connectivity removes all connections among fragments with shared area less than input value.\line\tab In case you input integer value then it is used as a percentage relative to the whole shared area range starting from the smallest area to the biggest area. \line\tab Right now the current setup means that you are going to remove 15% of all connections starting from the smallest and AreaCollapse will be initiated 25 times during 25 seconds\line\par
{\pntext\f0 26.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 27.\tab}In the \b Connectivity \b0 component click on \b Start Collapse \b0 button.\line\line\tab Notice that every second structure loses green connections and some fragments activate. Also it is possible to Start Collapse by Collapse properties using public static method:\line RFCollapse.StartCollapse(RayfireConnectivity connectivity)\line\line\tab Another way to start collapse is to set Start Collapse property to At Start. In this case Collapse will start immediately when the object will be initialized. \line\line\tab And finally you can Start Collapse By Integrity. When the Connectivity structure is initialized it starts with 100% Integrity readonly property. You can see current Integrity on top of the Connectivity component. Every time a connected structure loses fragments Integrity gets lower. Setting \b Start Collapse \b0 property to \b By Integrity \b0 you can initiate collapse when your structure \b Integrity \b0 will become lower than defined \b Collapse By Integrity \b0 property value.\par

\pard\nowidctlpar\sl276\slmult1\par
\par
\par
\par
\par
\par
}
 