%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8902001241107755306
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: AsteroidEatingWorm
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -6122639103416589286}
  - {fileID: 5895893242889647310}
  - {fileID: 4170148555585029806}
  - {fileID: 4904232081652405907}
  - {fileID: 8460845461883924138}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-8824600200340487766
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2214751534217887962}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 38.13333333333333
    m_ClipIn: 0
    m_Asset: {fileID: -6305977965615942206}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -8824600200340487766}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Wingman02_Explosion
  m_Markers:
    m_Objects: []
--- !u!114 &-8782196810902158277
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: Recorded (3)
  m_EditorClassIdentifier: 
  m_Clip: {fileID: -5200547574691348459}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 0
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!74 &-8367341334270310517
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (10)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 17.533333
        value: {x: -250, y: 0, z: 0}
        inSlope: {x: 4.1028447, y: 0, z: 0}
        outSlope: {x: 4.1028447, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 78.46667
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 4.1028447, y: 0, z: 0}
        outSlope: {x: -70.959755, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 2
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.20555261, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 78.833336
        value: {x: 10111.687, y: 0, z: 0}
        inSlope: {x: 51950.562, y: 0, z: 0}
        outSlope: {x: 51950.562, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.06949646, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 78.833336
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 17.533333
        value: -250
        inSlope: 4.1028447
        outSlope: 4.1028447
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 78.46667
        value: 0
        inSlope: 4.1028447
        outSlope: -70.959755
        tangentMode: 5
        weightedMode: 2
        inWeight: 0.33333334
        outWeight: 0.20555261
      - serializedVersion: 3
        time: 78.833336
        value: 10111.687
        inSlope: 51950.562
        outSlope: 51950.562
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.06949646
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-8185007113256543977
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 3444815892371042183}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -61, y: -662, z: 184}
  m_InfiniteClipOffsetEulerAngles: {x: 13.977908, y: 168.7365, z: 357.47647}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -8367341334270310517}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-7912713892685483856
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-7888839462541856601
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2214751534217887962}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 38.13333333333333
    m_ClipIn: 0
    m_Asset: {fileID: -6835567984012370922}
    m_Duration: 4.671709581588708
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7888839462541856601}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-7653531414369999113
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-7560441604123907686
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Worm Eating Capital Ship
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -3773366150126281475}
  - {fileID: 4389696916304106758}
  - {fileID: 8058170054742336848}
  - {fileID: 6986213765454315549}
  - {fileID: 7195074680933099319}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-7297021428673538757
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 5346621538431577854}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 79.25
    m_ClipIn: 0
    m_Asset: {fileID: -4933597836281323324}
    m_Duration: 5.774999999999999
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7297021428673538757}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-7288339202148678568
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-7209322685537724536
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6173495173678939289}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -1806354448937595817}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-7131920408928779857
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShips
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 4843711660086921686}
  - {fileID: 6037492252473884487}
  - {fileID: 1481434885471483769}
  - {fileID: 3444815892371042183}
  - {fileID: 1421294178623053767}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!74 &-7067957874963347948
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (2)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 3000
        inSlope: -33.157894
        outSlope: -150
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 20
        value: 0
        inSlope: -150
        outSlope: -66.80448
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: Model_Invader_Worm_Parts
    classID: 114
    script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 840632637
      attribute: 3856988375
      script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 20
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 3000
        inSlope: -33.157894
        outSlope: -150
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 20
        value: 0
        inSlope: -150
        outSlope: -66.80448
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: Model_Invader_Worm_Parts
    classID: 114
    script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!74 &-7053326945203709320
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (16)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 55.116665
        value: {x: 0, y: -0, z: 200}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 55.866665
        value: {x: 0, y: -0, z: 152.52}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 57.216667
        value: {x: 0, y: 0, z: 418.4}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 58.233334
        value: {x: 0, y: 0, z: 305.3}
        inSlope: {x: 0, y: 0, z: -3.5294092}
        outSlope: {x: 0, y: 0, z: -3.5294092}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 58.916668
        value: {x: 0, y: 0, z: 303.8}
        inSlope: {x: 0, y: 0, z: -3.4951444}
        outSlope: {x: 0, y: 0, z: -3.4951444}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 59.95
        value: {x: 0, y: 0, z: 236.3}
        inSlope: {x: 0, y: 0, z: -43.75345}
        outSlope: {x: 0, y: 0, z: -43.75345}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 62.566666
        value: {x: 0, y: 0, z: 144.1}
        inSlope: {x: 0, y: 0, z: -35.22225}
        outSlope: {x: 0, y: 0, z: -35.22225}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 64.45
        value: {x: 0, y: 0, z: 77.8}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Model_Invader_Worm_Parts/Invader_Worm_Head/LazerBeam/BeamHitPoint
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: -1329.5702
        inSlope: 34.067238
        outSlope: 34.067238
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.966667
        value: 645.194
        inSlope: 35.086594
        outSlope: 35.086594
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.15958601
        outWeight: 0.11507659
      - serializedVersion: 3
        time: 163.01666
        value: 1509.4755
        inSlope: 4.3391438
        outSlope: 4.3391438
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.16662082
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: Model_Invader_Worm_Parts
    classID: 114
    script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 55.983334
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 56.133335
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 58.716667
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 63.366665
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: hitting
    path: Model_Invader_Worm_Parts/Invader_Worm_Head/LazerBeam
    classID: 114
    script: {fileID: 11500000, guid: 9be2dec593c37470590fcba37a895a52, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 3147749473
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 840632637
      attribute: 3856988375
      script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 821935871
      attribute: 2372907223
      script: {fileID: 11500000, guid: 9be2dec593c37470590fcba37a895a52, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 163.01666
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: -1329.5702
        inSlope: 34.067238
        outSlope: 34.067238
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.966667
        value: 645.194
        inSlope: 35.086594
        outSlope: 35.086594
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.15958601
        outWeight: 0.11507659
      - serializedVersion: 3
        time: 163.01666
        value: 1509.4755
        inSlope: 4.3391438
        outSlope: 4.3391438
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.16662082
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: Model_Invader_Worm_Parts
    classID: 114
    script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 55.116665
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 55.866665
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.216667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 58.233334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 58.916668
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 59.95
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 62.566666
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 64.45
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: Model_Invader_Worm_Parts/Invader_Worm_Head/LazerBeam/BeamHitPoint
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 55.116665
        value: -0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 55.866665
        value: -0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.216667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 58.233334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 58.916668
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 59.95
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 62.566666
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 64.45
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: Model_Invader_Worm_Parts/Invader_Worm_Head/LazerBeam/BeamHitPoint
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 55.116665
        value: 200
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 55.866665
        value: 152.52
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.216667
        value: 418.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 58.233334
        value: 305.3
        inSlope: -3.5294092
        outSlope: -3.5294092
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 58.916668
        value: 303.8
        inSlope: -3.4951444
        outSlope: -3.4951444
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 59.95
        value: 236.3
        inSlope: -43.75345
        outSlope: -43.75345
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 62.566666
        value: 144.1
        inSlope: -35.22225
        outSlope: -35.22225
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 64.45
        value: 77.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: Model_Invader_Worm_Parts/Invader_Worm_Head/LazerBeam/BeamHitPoint
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 55.983334
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 56.133335
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 58.716667
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 63.366665
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: hitting
    path: Model_Invader_Worm_Parts/Invader_Worm_Head/LazerBeam
    classID: 114
    script: {fileID: 11500000, guid: 9be2dec593c37470590fcba37a895a52, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-6835567984012370922
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-6831507358245968961
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: ca5ccea80c045438c81e887b0f64ffcc, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &-6775853519657525662
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: Recorded (7)
  m_EditorClassIdentifier: 
  m_Clip: {fileID: -1526962205883544782}
  m_Position: {x: 301.6, y: -134.7, z: 56.5}
  m_EulerAngles: {x: -0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 0
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &-6694125939310699765
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -2672104690582071002}
    m_Duration: 83.04999999999997
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6694125939310699765}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-6382784016015682627
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-6305977965615942206
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 247760a0e616fda4cb962ebe947fd6e6
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 5568
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-6122639103416589286
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShip
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -8902001241107755306}
  m_Children:
  - {fileID: 8874520153100909236}
  - {fileID: 41190679625678502}
  - {fileID: -5359012214547937026}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-6112749937190210482
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6173495173678939289}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 33.8
    m_ClipIn: 0
    m_Asset: {fileID: 7768846571940883780}
    m_Duration: 18.249999999999996
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6112749937190210482}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-6062353956093434925
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 9046224206614352875}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 787.6, y: -354, z: 18.6}
  m_InfiniteClipOffsetEulerAngles: {x: 26.62781, y: 346.46518, z: 358.93494}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -1351658160234835311}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-5578650343536621654
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-5359012214547937026
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -6122639103416589286}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -6382784016015682627}
    m_Duration: 52.1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -5359012214547937026}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-5341602934083288157
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: Recorded (2)
  m_EditorClassIdentifier: 
  m_Clip: {fileID: -7067957874963347948}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 0
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!74 &-5200547574691348459
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (3)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 40
        outSlope: 40
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 50
        value: 2000
        inSlope: 40
        outSlope: 40
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: Model_Invader_Worm_Parts
    classID: 114
    script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 840632637
      attribute: 3856988375
      script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 50
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 40
        outSlope: 40
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 50
        value: 2000
        inSlope: 40
        outSlope: 40
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: Model_Invader_Worm_Parts
    classID: 114
    script: {fileID: 11500000, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-5085512086139851957
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: HyperSpaceEffects
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -1288878322955502448}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-4933597836281323324
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-4812338521988738114
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 5346621538431577854}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -2932685664834885569}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-4593545392363439824
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 9046224206614352875}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 22.266666666666666
    m_ClipIn: 0
    m_Asset: {fileID: -836479945450497801}
    m_Duration: 18.56666666666667
    m_TimeScale: 1
    m_ParentTrack: {fileID: -4593545392363439824}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-3931872584402023124
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-3861423199337726470
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Worm
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -1595460474559033013}
  m_Children:
  - {fileID: -3841275542634193721}
  - {fileID: 4597303333394403745}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-3841275542634193721
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (3)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -3861423199337726470}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 12.383333333333333
    m_ClipIn: 0
    m_Asset: {fileID: -8782196810902158277}
    m_Duration: 50.483333333333334
    m_TimeScale: 1
    m_ParentTrack: {fileID: -3841275542634193721}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 1
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 12.383333333333333
    m_DisplayName: Recorded (3)
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-3773366150126281475
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7560441604123907686}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 58.166666666666664
    m_ClipIn: 0
    m_Asset: {fileID: -1468976498205184144}
    m_Duration: 26.833333333333336
    m_TimeScale: 1
    m_ParentTrack: {fileID: -3773366150126281475}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-3484052350009896521
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-3313597300948773984
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: EnemyFighters
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 6173495173678939289}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!74 &-2932685664834885569
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (6)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 8.233334
        value: 0
        inSlope: 32.59109
        outSlope: 32.59109
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 32.933334
        value: 805
        inSlope: 29.699352
        outSlope: 29.699352
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 46.083332
        value: 1157.52
        inSlope: 18.627863
        outSlope: 18.627863
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 52.733334
        value: 1227
        inSlope: 22.270046
        outSlope: 22.270046
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.483334
        value: 1388.9369
        inSlope: 27.548664
        outSlope: 27.548664
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 64.38333
        value: 1533.8738
        inSlope: 32.578114
        outSlope: 32.578114
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 83.01667
        value: 2356.552
        inSlope: 44.150875
        outSlope: 44.150875
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2952802523
      script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 83.01667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 8.233334
        value: 0
        inSlope: 32.59109
        outSlope: 32.59109
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 32.933334
        value: 805
        inSlope: 29.699352
        outSlope: 29.699352
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 46.083332
        value: 1157.52
        inSlope: 18.627863
        outSlope: 18.627863
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 52.733334
        value: 1227
        inSlope: 22.270046
        outSlope: 22.270046
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 57.483334
        value: 1388.9369
        inSlope: 27.548664
        outSlope: 27.548664
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 64.38333
        value: 1533.8738
        inSlope: 32.578114
        outSlope: 32.578114
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 83.01667
        value: 2356.552
        inSlope: 44.150875
        outSlope: 44.150875
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-2916079507558419431
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: 6b84e831612c7476998111bb149c6434, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &-2672104690582071002
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-2112454054342727140
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: b896be9d227bc4624a4f1200425b92bd, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!74 &-1806354448937595817
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (13)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 38
        value: -36.65573
        inSlope: 39.126194
        outSlope: 39.126194
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.10401927
      - serializedVersion: 3
        time: 40.933334
        value: 128.00525
        inSlope: 63.069096
        outSlope: 63.069096
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.14728129
      - serializedVersion: 3
        time: 42.733334
        value: 255.61203
        inSlope: 71.048256
        outSlope: 71.048256
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 44.566666
        value: 386.15228
        inSlope: 55.82633
        outSlope: 55.82633
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.21802507
      - serializedVersion: 3
        time: 47.1
        value: 489.3203
        inSlope: 32.468437
        outSlope: 32.468437
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.14642963
        outWeight: 0.060059045
      - serializedVersion: 3
        time: 52.033333
        value: 629.04095
        inSlope: 19.636679
        outSlope: 19.636679
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.058817208
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2952802523
      script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 52.033333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 38
        value: -36.65573
        inSlope: 39.126194
        outSlope: 39.126194
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.10401927
      - serializedVersion: 3
        time: 40.933334
        value: 128.00525
        inSlope: 63.069096
        outSlope: 63.069096
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.14728129
      - serializedVersion: 3
        time: 42.733334
        value: 255.61203
        inSlope: 71.048256
        outSlope: 71.048256
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 44.566666
        value: 386.15228
        inSlope: 55.82633
        outSlope: 55.82633
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.21802507
      - serializedVersion: 3
        time: 47.1
        value: 489.3203
        inSlope: 32.468437
        outSlope: 32.468437
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.14642963
        outWeight: 0.060059045
      - serializedVersion: 3
        time: 52.033333
        value: 629.04095
        inSlope: 19.636679
        outSlope: 19.636679
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.058817208
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-1595460474559033013
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CQB
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -3861423199337726470}
  - {fileID: 9046224206614352875}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!74 &-1526962205883544782
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (7)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 52.083332
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: -9.951402, y: -10.528404, z: 14.398977}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 53.666668
        value: {x: -15.756411, y: -16.67, z: 22.798418}
        inSlope: {x: -9.951402, y: -10.528404, z: 14.398977}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.866667
        value: {x: -4420.37, y: 0, z: 0}
        inSlope: {x: 8017.106, y: 0, z: 0}
        outSlope: {x: 8017.106, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 7.4
        value: {x: -144.58, y: 0, z: 0}
        inSlope: {x: 8017.107, y: 0, z: 0}
        outSlope: {x: 15.112891, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 16.966667
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 13.876039, y: 0, z: 0}
        outSlope: {x: 13.876039, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 52.083332
        value: {x: 443.84607, y: 0, z: 0}
        inSlope: {x: -22.351488, y: 0, z: 0}
        outSlope: {x: -224.74297, y: -264.63373, z: -126.86965}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 53.666668
        value: {x: 88.002464, y: -419.0041, z: -200.87727}
        inSlope: {x: -224.74297, y: -264.63373, z: -126.86965}
        outSlope: {x: -57.342163, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 53.666668
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.866667
        value: -4420.37
        inSlope: 8017.106
        outSlope: 8017.106
        tangentMode: 1
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.4
        value: -144.58
        inSlope: 8017.107
        outSlope: 15.112891
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 16.966667
        value: 0
        inSlope: 13.876039
        outSlope: 13.876039
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 52.083332
        value: 443.84607
        inSlope: -22.351488
        outSlope: -224.74297
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 53.666668
        value: 88.002464
        inSlope: -224.74297
        outSlope: -57.342163
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 52.083332
        value: 0
        inSlope: 0
        outSlope: -264.63373
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 53.666668
        value: -419.0041
        inSlope: -264.63373
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 8
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 52.083332
        value: 0
        inSlope: 0
        outSlope: -126.86965
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 53.666668
        value: -200.87727
        inSlope: -126.86965
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 8
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 52.083332
        value: 0
        inSlope: 0
        outSlope: -9.951402
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 53.666668
        value: -15.756411
        inSlope: -9.951402
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 52.083332
        value: 0
        inSlope: 0
        outSlope: -10.528404
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 53.666668
        value: -16.67
        inSlope: -10.528404
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 52.083332
        value: 0
        inSlope: 0
        outSlope: 14.398977
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 53.666668
        value: 22.798418
        inSlope: 14.398977
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-1468976498205184144
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!74 &-1351658160234835311
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (8)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0.55
        value: {x: 9.887096, y: 0, z: 0}
        inSlope: {x: -2.0873625, y: 0, z: 0}
        outSlope: {x: -2.0873625, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 23.25
        value: {x: -37.496037, y: 0, z: 0}
        inSlope: {x: -2.0873625, y: 0, z: 0}
        outSlope: {x: 3.5439415, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 23.266666
        value: {x: -37.43697, y: 0, z: 0}
        inSlope: {x: 3.5439415, y: 0, z: 0}
        outSlope: {x: 3.5439415, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 23.366667
        value: {x: -37.082577, y: 0, z: -0.004201602}
        inSlope: {x: 3.5439417, y: 0, z: -0.08376195}
        outSlope: {x: 3.5439417, y: 0, z: -0.08376195}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 33.716667
        value: {x: -0.40278244, y: -15.052161, z: -15.392263}
        inSlope: {x: 3.5439415, y: -1.0503395, z: 0}
        outSlope: {x: 3.5439415, y: -1.0503395, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 35.666668
        value: {x: 6.507906, y: -16.140558, z: -15.392263}
        inSlope: {x: 3.5439415, y: 0, z: 0}
        outSlope: {x: 3.5439415, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 50.45
        value: {x: 58.899174, y: -16.140558, z: -15.392263}
        inSlope: {x: 3.5439415, y: 0, z: 0}
        outSlope: {x: 3.5439415, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 22.3
        value: {x: -4524.2905, y: 0, z: 0}
        inSlope: {x: 5604.9634, y: 0, z: 0}
        outSlope: {x: 5604.9634, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 23.1
        value: {x: -40.313538, y: 0, z: 0}
        inSlope: {x: 159.84004, y: 0, z: 0}
        outSlope: {x: 22.587465, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 1
        inWeight: {x: 0.12323362, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 23.316668
        value: {x: -35.419575, y: 0, z: 0}
        inSlope: {x: 22.587463, y: 0, z: 0}
        outSlope: {x: 22.587463, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 33.2
        value: {x: 187.81985, y: -83.90182, z: 69.1}
        inSlope: {x: 22.587467, y: 0, z: 0}
        outSlope: {x: 22.587467, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 55.033333
        value: {x: 680.9795, y: -83.90182, z: 69.1}
        inSlope: {x: 22.587465, y: 0, z: 0}
        outSlope: {x: 22.587465, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 55.033333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 22.3
        value: -4524.2905
        inSlope: 5604.9634
        outSlope: 5604.9634
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 23.1
        value: -40.313538
        inSlope: 159.84004
        outSlope: 22.587465
        tangentMode: 65
        weightedMode: 1
        inWeight: 0.12323362
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 55.033333
        value: 680.9795
        inSlope: 22.587465
        outSlope: 22.587465
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0.55
        value: 9.887096
        inSlope: -2.0873625
        outSlope: -2.0873625
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 23.25
        value: -37.496037
        inSlope: -2.0873625
        outSlope: 3.5439415
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 50.45
        value: 58.899174
        inSlope: 3.5439415
        outSlope: 3.5439415
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 23.266666
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 33.716667
        value: -15.392263
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 24
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 23.316668
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 33.2
        value: -83.90182
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 8
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 23.366667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 35.666668
        value: -16.140558
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 24
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 23.316668
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 33.2
        value: 69.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 8
  m_EulerEditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-1288878322955502448
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5085512086139851957}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 6642850519223993732}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!74 &-1151679610606172143
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (15)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.4166665
        value: {x: -2752.1802, y: 0, z: 0}
        inSlope: {x: 9437.131, y: 0, z: 0}
        outSlope: {x: 9437.131, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.07878376, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 6.85
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 6.85
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.4166665
        value: -2752.1802
        inSlope: 9437.131
        outSlope: 9437.131
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.07878376
      - serializedVersion: 3
        time: 6.85
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.85
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.85
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-926856046510426116
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1481434885471483769}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 1092, y: -647, z: -233}
  m_InfiniteClipOffsetEulerAngles: {x: 1.4007298, y: 1.3061411, z: 5.731891}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -1151679610606172143}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-836479945450497801
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!74 &-542543124592217201
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (11)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.4
        value: 0
        inSlope: 51.69887
        outSlope: 51.69887
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 12
        value: 289.51367
        inSlope: 41.038868
        outSlope: 41.038868
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.783333
        value: 495.58362
        inSlope: 35.199535
        outSlope: 35.199535
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 39.383335
        value: 1320
        inSlope: 40.020206
        outSlope: 40.020206
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2952802523
      script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 39.383335
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.4
        value: 0
        inSlope: 51.69887
        outSlope: 51.69887
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 12
        value: 289.51367
        inSlope: 41.038868
        outSlope: 41.038868
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.783333
        value: 495.58362
        inSlope: 35.199535
        outSlope: 35.199535
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 39.383335
        value: 1320
        inSlope: 40.020206
        outSlope: 40.020206
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-30525688668304150
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-14097949395348739
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: -6126408084118648288, guid: 3a7d2448f740f4f409e94f29b554d124, type: 3}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: CockpitFlythrough
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: 5346621538431577854}
  - {fileID: -5085512086139851957}
  - {fileID: -7560441604123907686}
  - {fileID: -8902001241107755306}
  - {fileID: -1595460474559033013}
  - {fileID: -7131920408928779857}
  - {fileID: -6694125939310699765}
  - {fileID: 2887210442281281629}
  - {fileID: -3313597300948773984}
  m_FixedDuration: 85
  m_EditorSettings:
    m_Framerate: 60
    m_ScenePreview: 1
  m_DurationMode: 1
  m_MarkerTrack: {fileID: 3643452466011184978}
--- !u!114 &41190679625678502
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -6122639103416589286}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -6775853519657525662}
    m_Duration: 53.666666666666664
    m_TimeScale: 1
    m_ParentTrack: {fileID: 41190679625678502}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 1
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: Recorded (7)
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &358383381871221953
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15c38f6fa1940124db1ab7f6fe7268d1, type: 3}
  m_Name: Signal Emitter
  m_EditorClassIdentifier: 
  m_Time: 57.733333333333334
  m_Retroactive: 0
  m_EmitOnce: 0
  m_Asset: {fileID: 11400000, guid: bcb370b04940f4527b6f6d2021027a6a, type: 2}
--- !u!114 &626662381910616675
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2214751534217887962}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 1826643726470873765}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &912635030183713846
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6173495173678939289}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 33.8
    m_ClipIn: 0
    m_Asset: {fileID: -7288339202148678568}
    m_Duration: 22.25
    m_TimeScale: 1
    m_ParentTrack: {fileID: 912635030183713846}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &1219146486064128845
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &1421294178623053767
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShipDestroyed1
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7131920408928779857}
  m_Children:
  - {fileID: 4676816444094801180}
  - {fileID: 5228012723108605691}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &1481434885471483769
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShip04
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7131920408928779857}
  m_Children:
  - {fileID: -926856046510426116}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!74 &1826643726470873765
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (12)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.4
        value: 0
        inSlope: 51.69887
        outSlope: 51.69887
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 12
        value: 289.51367
        inSlope: 41.038868
        outSlope: 41.038868
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.783333
        value: 495.58362
        inSlope: 34.501816
        outSlope: 34.501816
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 28.3
        value: 863.1626
        inSlope: 46.961426
        outSlope: 46.961426
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 31.75
        value: 1053.941
        inSlope: 60.73661
        outSlope: 60.73661
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.09250274
      - serializedVersion: 3
        time: 38.516666
        value: 1348.8324
        inSlope: 29.868513
        outSlope: 29.868513
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2952802523
      script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 38.516666
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 6.4
        value: 0
        inSlope: 51.69887
        outSlope: 51.69887
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 12
        value: 289.51367
        inSlope: 41.038868
        outSlope: 41.038868
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.783333
        value: 495.58362
        inSlope: 34.501816
        outSlope: 34.501816
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 28.3
        value: 863.1626
        inSlope: 46.961426
        outSlope: 46.961426
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 31.75
        value: 1053.941
        inSlope: 60.73661
        outSlope: 60.73661
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.09250274
      - serializedVersion: 3
        time: 38.516666
        value: 1348.8324
        inSlope: 29.868513
        outSlope: 29.868513
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &1878572427620169931
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 5346621538431577854}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -2.56, y: 9.1, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: -0, y: 90, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 7378981716129239615}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &2214751534217887962
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Wingman2
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2887210442281281629}
  m_Children:
  - {fileID: 626662381910616675}
  - {fileID: 5385148372996554636}
  - {fileID: -7888839462541856601}
  - {fileID: 5085201987124589703}
  - {fileID: -8824600200340487766}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &2271518108290482999
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Control Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6173495173678939289}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 51.666666666666664
    m_ClipIn: 0
    m_Asset: {fileID: 8340387510386334479}
    m_Duration: 2
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2271518108290482999}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Enemy01_Explosion
  m_Markers:
    m_Objects: []
--- !u!114 &2353005132238740958
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &2527789998945461427
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: Recorded (16)
  m_EditorClassIdentifier: 
  m_Clip: {fileID: -7053326945203709320}
  m_Position: {x: 695.3, y: -421.6, z: -603.8}
  m_EulerAngles: {x: -0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 0
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &2564549211628107529
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: ca5ccea80c045438c81e887b0f64ffcc, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &2651363257200378005
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6173495173678939289}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 51.666666666666664
    m_ClipIn: 0
    m_Asset: {fileID: -7912713892685483856}
    m_Duration: 4.383333333333333
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2651363257200378005}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &2887210442281281629
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Wingmen
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 8267334817970497538}
  - {fileID: 2214751534217887962}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &3241720075537349060
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &3444815892371042183
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShip4
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7131920408928779857}
  m_Children:
  - {fileID: -8185007113256543977}
  - {fileID: 8978906843114319546}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &3643452466011184978
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a16748d9461eae46a725db9776d5390, type: 3}
  m_Name: Markers
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects:
    - {fileID: 6647723330020771072}
--- !u!114 &3992915483964775881
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: 6b84e831612c7476998111bb149c6434, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &4170148555585029806
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -8902001241107755306}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 38.28333333333333
    m_ClipIn: 0
    m_Asset: {fileID: 5156819553431950382}
    m_Duration: 46.50000076293946
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4170148555585029806}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &4247040043894525179
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6037492252473884487}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 642.4, y: -100.9, z: -183}
  m_InfiniteClipOffsetEulerAngles: {x: 9.15801, y: 359.69684, z: 359.75122}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 8431942820850095243}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &4389696916304106758
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7560441604123907686}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 68.68333333333332
    m_ClipIn: 0
    m_Asset: {fileID: -6831507358245968961}
    m_Duration: 19.916666666666657
    m_TimeScale: 0.5
    m_ParentTrack: {fileID: 4389696916304106758}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: CapitalShipExpode
  - m_Version: 1
    m_Start: 58.166666666666664
    m_ClipIn: 0
    m_Asset: {fileID: 3992915483964775881}
    m_Duration: 10.516666666666666
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4389696916304106758}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 58.166666666666664
    m_DisplayName: Idle
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &4597303333394403745
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -3861423199337726470}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 12.399999999999999
    m_ClipIn: 0
    m_Asset: {fileID: -5578650343536621654}
    m_Duration: 28.433333333333337
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4597303333394403745}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &4676816444094801180
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1421294178623053767}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 64.86666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 2353005132238740958}
    m_Duration: 18.183333333333337
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4676816444094801180}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &4765362504578366258
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: Recorded
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 6232571123561132912}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 0
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &4843711660086921686
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: StartingCapitalShip
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7131920408928779857}
  m_Children:
  - {fileID: 8408581416296155633}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &4904232081652405907
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -8902001241107755306}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 55.416666666666664
    m_ClipIn: 0
    m_Asset: {fileID: -14097949395348739}
    m_Duration: 19.999999999999993
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4904232081652405907}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 55.416666666666664
    m_DisplayName: Explosion
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &4968040485007139225
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15c38f6fa1940124db1ab7f6fe7268d1, type: 3}
  m_Name: Signal Emitter
  m_EditorClassIdentifier: 
  m_Time: 68.25
  m_Retroactive: 0
  m_EmitOnce: 0
  m_Asset: {fileID: 11400000, guid: e5fba2671cf3d4704bbb2de09e353c1d, type: 2}
--- !u!114 &5085201987124589703
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2214751534217887962}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 9.35
    m_ClipIn: 0
    m_Asset: {fileID: -7653531414369999113}
    m_Duration: 33.45504291492204
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5085201987124589703}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!74 &5085793584255174137
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (5)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 5
        value: {x: -6400, y: 0, z: 0}
        inSlope: {x: 5439.159, y: 0, z: 0}
        outSlope: {x: 5439.159, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.040505335, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 6.2
        value: {x: -731.012, y: 0, z: 0}
        inSlope: {x: 2081.2979, y: 0, z: 0}
        outSlope: {x: 2081.2979, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.07404445, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 7
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 7
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 5
        value: -6400
        inSlope: 5439.159
        outSlope: 5439.159
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.040505335
      - serializedVersion: 3
        time: 6.2
        value: -731.012
        inSlope: 2081.2979
        outSlope: 2081.2979
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.07404445
      - serializedVersion: 3
        time: 7
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &5156819553431950382
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &5228012723108605691
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1421294178623053767}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -2112454054342727140}
    m_Duration: 83.05
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5228012723108605691}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: CapitalShipExploded
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &5346621538431577854
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: PlayerShip
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 1878572427620169931}
  - {fileID: 6647066434588751551}
  - {fileID: -4812338521988738114}
  - {fileID: -7297021428673538757}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &5358328320019647979
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15c38f6fa1940124db1ab7f6fe7268d1, type: 3}
  m_Name: Signal Emitter
  m_EditorClassIdentifier: 
  m_Time: 57.75
  m_Retroactive: 0
  m_EmitOnce: 0
  m_Asset: {fileID: 11400000, guid: bcb370b04940f4527b6f6d2021027a6a, type: 2}
--- !u!114 &5385148372996554636
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2214751534217887962}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 9.35
    m_ClipIn: 0
    m_Asset: {fileID: 1219146486064128845}
    m_Duration: 29.15
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5385148372996554636}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &5895893242889647310
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -8902001241107755306}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 2527789998945461427}
    m_Duration: 85
    m_TimeScale: 1
    m_ParentTrack: {fileID: 5895893242889647310}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 1
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: Recorded (16)
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &6023563724839486487
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 8267334817970497538}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 9.35
    m_ClipIn: 0
    m_Asset: {fileID: -3484052350009896521}
    m_Duration: 29.866666666666667
    m_TimeScale: 1
    m_ParentTrack: {fileID: 6023563724839486487}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &6037492252473884487
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShip02
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7131920408928779857}
  m_Children:
  - {fileID: 4247040043894525179}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &6173495173678939289
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Enemy01
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -3313597300948773984}
  m_Children:
  - {fileID: -7209322685537724536}
  - {fileID: 912635030183713846}
  - {fileID: -6112749937190210482}
  - {fileID: 2651363257200378005}
  - {fileID: 2271518108290482999}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!74 &6232571123561132912
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.983333
        value: 0
        inSlope: -0
        outSlope: 34.63707
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 29.733334
        value: 753.35626
        inSlope: 45.518673
        outSlope: 45.518673
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.1926861
      - serializedVersion: 3
        time: 36.15
        value: 1068.7428
        inSlope: 25.33674
        outSlope: 25.33674
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.034660403
      - serializedVersion: 3
        time: 68.01667
        value: 1900
        inSlope: 41.522938
        outSlope: 41.522938
        tangentMode: 0
        weightedMode: 1
        inWeight: 0.09081294
        outWeight: 0.11375885
      - serializedVersion: 3
        time: 79.98333
        value: 2499.0466
        inSlope: 61.602356
        outSlope: 61.602356
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 84.76667
        value: 5317.606
        inSlope: 833.0504
        outSlope: 833.0504
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.07793774
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2952802523
      script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 84.76667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.983333
        value: 0
        inSlope: -0
        outSlope: 34.63707
        tangentMode: 69
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 29.733334
        value: 753.35626
        inSlope: 45.518673
        outSlope: 45.518673
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.1926861
      - serializedVersion: 3
        time: 36.15
        value: 1068.7428
        inSlope: 25.33674
        outSlope: 25.33674
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.034660403
      - serializedVersion: 3
        time: 68.01667
        value: 1900
        inSlope: 41.522938
        outSlope: 41.522938
        tangentMode: 0
        weightedMode: 1
        inWeight: 0.09081294
        outWeight: 0.11375885
      - serializedVersion: 3
        time: 79.98333
        value: 2499.0466
        inSlope: 61.602356
        outSlope: 61.602356
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 84.76667
        value: 5317.606
        inSlope: 833.0504
        outSlope: 833.0504
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.07793774
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Position
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!74 &6642850519223993732
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (4)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 5
        value: 1
        inSlope: -1.0601516
        outSlope: -1.0601516
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.018470526
      - serializedVersion: 3
        time: 7
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Float
    path: 
    classID: 23
    script: {fileID: 0}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2187107597
      script: {fileID: 0}
      typeID: 23
      customType: 22
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 7
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 5
        value: 1
        inSlope: -1.0601516
        outSlope: -1.0601516
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.018470526
      - serializedVersion: 3
        time: 7
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Float
    path: 
    classID: 23
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &6647066434588751551
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 5346621538431577854}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 4765362504578366258}
    m_Duration: 83.05
    m_TimeScale: 1
    m_ParentTrack: {fileID: 6647066434588751551}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 1
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: Recorded
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &6647723330020771072
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15c38f6fa1940124db1ab7f6fe7268d1, type: 3}
  m_Name: Signal Emitter
  m_EditorClassIdentifier: 
  m_Time: 85
  m_Retroactive: 0
  m_EmitOnce: 0
  m_Asset: {fileID: 11400000, guid: bdc9c64d83e904853a246b56552071fe, type: 2}
--- !u!114 &6986213765454315549
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7560441604123907686}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 68.61666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 7566785530541050524}
    m_Duration: 10.91666529642361
    m_TimeScale: 1
    m_ParentTrack: {fileID: 6986213765454315549}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &7195074680933099319
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7560441604123907686}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 40.03333333333329
    m_ClipIn: 0
    m_Asset: {fileID: -3931872584402023124}
    m_Duration: 39.49999862975698
    m_TimeScale: 1
    m_ParentTrack: {fileID: 7195074680933099319}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &7217564361312357726
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 15c38f6fa1940124db1ab7f6fe7268d1, type: 3}
  m_Name: Signal Emitter
  m_EditorClassIdentifier: 
  m_Time: 68.28333333333333
  m_Retroactive: 0
  m_EmitOnce: 0
  m_Asset: {fileID: 11400000, guid: e5fba2671cf3d4704bbb2de09e353c1d, type: 2}
--- !u!74 &7378981716129239615
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (1)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: {x: 1.41, y: 1.68, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 0.76666665
        value: {x: 1.98, y: 2.13, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 4.35
        value: {x: -1.58, y: -0.33, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 6.116667
        value: {x: 2.03, y: 0.76, z: 10}
        inSlope: {x: 0, y: 1.212766, z: 0}
        outSlope: {x: 0, y: 1.212766, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 7.483333
        value: {x: 0.56, y: 3.47, z: 10}
        inSlope: {x: -0.80297124, y: 0, z: 0}
        outSlope: {x: -0.80297124, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 7.95
        value: {x: 0.19222085, y: 3.4258423, z: 10}
        inSlope: {x: -0.3888199, y: 0, z: 0}
        outSlope: {x: -0.3888199, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 8.05
        value: {x: 0.17189723, y: 3.7093086, z: 10}
        inSlope: {x: 0, y: 4.251966, z: 0}
        outSlope: {x: 0, y: 4.251966, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 8.15
        value: {x: 0.19674125, y: 3.9927707, z: 10}
        inSlope: {x: 0.45367634, y: 0, z: 0}
        outSlope: {x: 0.45367634, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 8.3
        value: {x: 0.286667, y: 3.8761268, z: 10}
        inSlope: {x: 0.6481112, y: -1.4515629, z: 0}
        outSlope: {x: 0.6481112, y: -1.4515629, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 8.5
        value: {x: 0.368761, y: 3.4706118, z: 10}
        inSlope: {x: 0, y: -2.4192655, z: 0}
        outSlope: {x: 0, y: -2.4192655, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 8.65
        value: {x: 0.36567873, y: 3.0999467, z: 10}
        inSlope: {x: -0.03881401, y: -2.4192653, z: 0}
        outSlope: {x: -0.03881401, y: -2.4192653, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 9
        value: {x: 0.34339234, y: 2.577788, z: 10}
        inSlope: {x: -0.07610595, y: 0, z: 0}
        outSlope: {x: -0.07610595, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 9.5
        value: {x: 0.31802368, y: 2.796739, z: 10}
        inSlope: {x: 0, y: 0.8009493, z: 0}
        outSlope: {x: 0, y: 0.8009493, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 11.283334
        value: {x: 8.06, y: 4.36, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 13.2
        value: {x: -0.01, y: 2.63, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 16.05
        value: {x: 0.79, y: 3.86, z: 10}
        inSlope: {x: 0.4192308, y: 0.4615385, z: 0}
        outSlope: {x: 0.4192308, y: 0.4615385, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 18.4
        value: {x: 2.17, y: 5.03, z: 10}
        inSlope: {x: 0, y: 0.29908252, z: 0}
        outSlope: {x: 0, y: 0.29908252, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 21.5
        value: {x: -0.46, y: 5.49, z: 10}
        inSlope: {x: -0.9156796, y: -0.4766901, z: 0}
        outSlope: {x: -0.9156796, y: -0.4766901, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.92789197, z: 0.33333334}
      - serializedVersion: 3
        time: 23.183332
        value: {x: -2.21, y: 3.86, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 25.583334
        value: {x: -0.69, y: 4.86, z: 10}
        inSlope: {x: 0.9834434, y: 0, z: 0}
        outSlope: {x: 0.9834434, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 28.216667
        value: {x: 2.74, y: 3.06, z: 10}
        inSlope: {x: 1.2045922, y: -0.106109485, z: 0}
        outSlope: {x: 1.2045922, y: -0.106109485, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.42964423, z: 0.33333334}
      - serializedVersion: 3
        time: 32.116665
        value: {x: 7.18, y: 4.34, z: 10}
        inSlope: {x: 1.4359754, y: 1.2879953, z: 0}
        outSlope: {x: 1.4359754, y: 1.2879953, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.8881318, z: 0.33333334}
      - serializedVersion: 3
        time: 33.683334
        value: {x: 10.59, y: 6.6111417, z: 10}
        inSlope: {x: 0, y: 0.1219009, z: 0}
        outSlope: {x: 0, y: 0.1219009, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 33.716667
        value: {x: 10.587201, y: 6.613184, z: 10}
        inSlope: {x: -0.16727504, y: 0, z: 0}
        outSlope: {x: -0.16727504, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 35.683334
        value: {x: 5.55, y: 1.86, z: 10}
        inSlope: {x: -2.4776132, y: -0.36671644, z: 0}
        outSlope: {x: -2.4776132, y: -0.36671644, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 1, z: 0.33333334}
      - serializedVersion: 3
        time: 37.033333
        value: {x: 2.29, y: 2.63, z: 10}
        inSlope: {x: -1.9709315, y: 1.5385914, z: 0}
        outSlope: {x: -1.9709315, y: 1.5385914, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.88305396, z: 0.33333334}
      - serializedVersion: 3
        time: 38.55
        value: {x: -0.1, y: 3.89, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 39.533333
        value: {x: 3.09, y: 1.99, z: 10}
        inSlope: {x: 0, y: -1.2230766, z: 0}
        outSlope: {x: 0, y: -1.2230766, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 40.283333
        value: {x: -0.63, y: 1.46, z: 10}
        inSlope: {x: -5.147483, y: 0, z: 0}
        outSlope: {x: -5.147483, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 41.45
        value: {x: -7.872364, y: 3.07, z: 10}
        inSlope: {x: -4.248932, y: 1.5822994, z: 0}
        outSlope: {x: -4.248932, y: 1.5822994, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 41.833332
        value: {x: -8.7492075, y: 3.8802266, z: 10}
        inSlope: {x: 0, y: 2.2365227, z: 0}
        outSlope: {x: 0, y: 2.2365227, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 42.166668
        value: {x: -7.7788296, y: 4.44, z: 10}
        inSlope: {x: 5.2787986, y: 0.813223, z: 0}
        outSlope: {x: 5.2787986, y: 0.813223, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 43.116665
        value: {x: -2.0180042, y: 4.8328447, z: 10}
        inSlope: {x: 2.43544, y: 0.11125461, z: 0}
        outSlope: {x: 2.43544, y: 0.11125461, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.48445022, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 43.466667
        value: {x: -1.4910034, y: 4.85, z: 10}
        inSlope: {x: 0.68583053, y: 0, z: 0}
        outSlope: {x: 0.68583053, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 44.633335
        value: {x: -2.027983, y: 4.85, z: 10}
        inSlope: {x: -0.38574657, y: 0, z: 0}
        outSlope: {x: -0.38574657, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 1, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.71412665, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 45.283333
        value: {x: -2.2391908, y: 4.85, z: 10}
        inSlope: {x: -0.26745, y: 0, z: 0}
        outSlope: {x: -0.26745, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 47.75
        value: {x: -2.51, y: 5.02, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 50.283333
        value: {x: 1.47, y: 3.83, z: 10}
        inSlope: {x: 0.5879517, y: -0.5469878, z: 0}
        outSlope: {x: 0.5879517, y: -0.5469878, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 51.9
        value: {x: 2.08, y: 2.75, z: 10}
        inSlope: {x: 0.36373925, y: 0, z: 0}
        outSlope: {x: 0.36373925, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 56.166668
        value: {x: 3.61, y: 3.52, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 59.6
        value: {x: -1.79, y: 2.88, z: 10}
        inSlope: {x: -0.8023972, y: 0, z: 0}
        outSlope: {x: -0.8023972, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 65.9
        value: {x: -4.2, y: 6.74, z: 10}
        inSlope: {x: 0, y: 1.5144982, z: 0}
        outSlope: {x: 0, y: 1.5144982, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.9051325, z: 0.33333334}
      - serializedVersion: 3
        time: 67
        value: {x: -3.3904433, y: 9.564777, z: 10}
        inSlope: {x: 1.4265949, y: 0, z: 0}
        outSlope: {x: 1.4265949, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 67.28333
        value: {x: -2.94, y: 9.178127, z: 10}
        inSlope: {x: 1.7500027, y: -2.5621536, z: 0}
        outSlope: {x: 1.7500027, y: -2.5621536, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 68.7
        value: {x: 0.7, y: 4.17, z: 10}
        inSlope: {x: 1.7480767, y: -0.32939923, z: 0}
        outSlope: {x: 1.7480767, y: -0.32939923, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 70.75
        value: {x: 3.12, y: 3.8611884, z: 10}
        inSlope: {x: 2.2272377, y: 0, z: 0}
        outSlope: {x: 2.2272377, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 72.98333
        value: {x: 10.24, y: 4.25, z: 10}
        inSlope: {x: 2.8824677, y: 0.321542, z: 0}
        outSlope: {x: 2.8824677, y: 0.321542, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 1, z: 0.33333334}
      - serializedVersion: 3
        time: 74.35
        value: {x: 13.49688, y: 5.6647825, z: 10}
        inSlope: {x: 0, y: 0.75912046, z: 0}
        outSlope: {x: 0, y: 0.75912046, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 74.4
        value: {x: 13.483368, y: 5.700359, z: 10}
        inSlope: {x: -0.5374712, y: 0.662518, z: 0}
        outSlope: {x: -0.5374712, y: 0.662518, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 74.833336
        value: {x: 12.354021, y: 5.7554812, z: 10}
        inSlope: {x: -4.4530873, y: -0.50761086, z: 0}
        outSlope: {x: -4.4530873, y: -0.50761086, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.7918172, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 77.083336
        value: {x: -3.1966267, y: 0.1, z: 10}
        inSlope: {x: -3.38998, y: 0, z: 0}
        outSlope: {x: -3.38998, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 77.433334
        value: {x: -3.8151903, y: 0.14593244, z: 10}
        inSlope: {x: 0, y: 0.25443834, z: 0}
        outSlope: {x: 0, y: 0.25443834, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 80.433334
        value: {x: -0.32, y: 2.1, z: 10}
        inSlope: {x: 0.24431144, y: 0.45802838, z: 0}
        outSlope: {x: 0.24431144, y: 0.45802838, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 83
        value: {x: 0.02, y: 2.81, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Pivot/Logic/ActionCameraAim
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 2
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendTotal
    path: 
    classID: 114
    script: {fileID: 11500000, guid: d5f46d4e505b74bb29521cf5ce090873, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 81.166664
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendContraints
    path: 
    classID: 114
    script: {fileID: 11500000, guid: d5f46d4e505b74bb29521cf5ce090873, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 79.6
        value: 0
        inSlope: 1.5392714
        outSlope: 1.5392714
        tangentMode: 0
        weightedMode: 2
        inWeight: 0.33333334
        outWeight: 0.10146332
      - serializedVersion: 3
        time: 81.6
        value: 0.4105917
        inSlope: 0.121686816
        outSlope: 0.121686816
        tangentMode: 0
        weightedMode: 3
        inWeight: 0.47523728
        outWeight: 0.4031369
      - serializedVersion: 3
        time: 82.23333
        value: 1
        inSlope: 1.7184546
        outSlope: 1.7184546
        tangentMode: 0
        weightedMode: 1
        inWeight: 0.18232802
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Float
    path: Pivot/FX/HyperSpaceTunnel
    classID: 23
    script: {fileID: 0}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 2785087389
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 3865075447
      script: {fileID: 11500000, guid: d5f46d4e505b74bb29521cf5ce090873, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 3080938016
      attribute: 2187107597
      script: {fileID: 0}
      typeID: 23
      customType: 22
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 2581865447
      script: {fileID: 11500000, guid: d5f46d4e505b74bb29521cf5ce090873, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 83
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 2
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendTotal
    path: 
    classID: 114
    script: {fileID: 11500000, guid: d5f46d4e505b74bb29521cf5ce090873, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 81.166664
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendContraints
    path: 
    classID: 114
    script: {fileID: 11500000, guid: d5f46d4e505b74bb29521cf5ce090873, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 79.6
        value: 0
        inSlope: 1.5392714
        outSlope: 1.5392714
        tangentMode: 0
        weightedMode: 2
        inWeight: 0.33333334
        outWeight: 0.10146332
      - serializedVersion: 3
        time: 81.6
        value: 0.4105917
        inSlope: 0.121686816
        outSlope: 0.121686816
        tangentMode: 0
        weightedMode: 3
        inWeight: 0.47523728
        outWeight: 0.4031369
      - serializedVersion: 3
        time: 82.23333
        value: 1
        inSlope: 1.7184546
        outSlope: 1.7184546
        tangentMode: 0
        weightedMode: 1
        inWeight: 0.18232802
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: material._Float
    path: Pivot/FX/HyperSpaceTunnel
    classID: 23
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1.41
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 0.76666665
        value: 1.98
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4.35
        value: -1.58
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 6.116667
        value: 2.03
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.483333
        value: 0.56
        inSlope: -0.80297124
        outSlope: -0.80297124
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 8.05
        value: 0.17189723
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 8.5
        value: 0.368761
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 9.5
        value: 0.31802368
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 11.283334
        value: 8.06
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 13.2
        value: -0.01
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 16.05
        value: 0.79
        inSlope: 0.4192308
        outSlope: 0.4192308
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.4
        value: 2.17
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 21.5
        value: -0.46
        inSlope: -0.9156796
        outSlope: -0.9156796
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 23.183332
        value: -2.21
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 25.583334
        value: -0.69
        inSlope: 0.9834434
        outSlope: 0.9834434
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 28.216667
        value: 2.74
        inSlope: 1.2045922
        outSlope: 1.2045922
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 32.116665
        value: 7.18
        inSlope: 1.4359754
        outSlope: 1.4359754
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 33.683334
        value: 10.59
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 35.683334
        value: 5.55
        inSlope: -2.4776132
        outSlope: -2.4776132
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 37.033333
        value: 2.29
        inSlope: -1.9709315
        outSlope: -1.9709315
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 38.55
        value: -0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 39.533333
        value: 3.09
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 40.283333
        value: -0.63
        inSlope: -5.147483
        outSlope: -5.147483
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 41.833332
        value: -8.7492075
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 43.116665
        value: -2.0180042
        inSlope: 2.43544
        outSlope: 2.43544
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.48445022
      - serializedVersion: 3
        time: 44.633335
        value: -2.027983
        inSlope: -0.38574657
        outSlope: -0.38574657
        tangentMode: 0
        weightedMode: 0
        inWeight: 1
        outWeight: 0.71412665
      - serializedVersion: 3
        time: 47.75
        value: -2.51
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 50.283333
        value: 1.47
        inSlope: 0.5879517
        outSlope: 0.5879517
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 51.9
        value: 2.08
        inSlope: 0.36373925
        outSlope: 0.36373925
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 56.166668
        value: 3.61
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 59.6
        value: -1.79
        inSlope: -0.8023972
        outSlope: -0.8023972
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 65.9
        value: -4.2
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 67.28333
        value: -2.94
        inSlope: 1.7500027
        outSlope: 1.7500027
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 68.7
        value: 0.7
        inSlope: 1.7480767
        outSlope: 1.7480767
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 70.75
        value: 3.12
        inSlope: 2.2272377
        outSlope: 2.2272377
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 72.98333
        value: 10.24
        inSlope: 2.8824677
        outSlope: 2.8824677
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 74.35
        value: 13.49688
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 77.433334
        value: -3.8151903
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 80.433334
        value: -0.32
        inSlope: 0.24431144
        outSlope: 0.24431144
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 83
        value: 0.02
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: Pivot/Logic/ActionCameraAim
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1.68
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 0.76666665
        value: 2.13
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4.35
        value: -0.33
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 6.116667
        value: 0.76
        inSlope: 1.212766
        outSlope: 1.212766
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.483333
        value: 3.47
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.95
        value: 3.4258423
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 8.15
        value: 3.9927707
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 9
        value: 2.577788
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 11.283334
        value: 4.36
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 13.2
        value: 2.63
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 16.05
        value: 3.86
        inSlope: 0.4615385
        outSlope: 0.4615385
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.4
        value: 5.03
        inSlope: 0.29908252
        outSlope: 0.29908252
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 21.5
        value: 5.49
        inSlope: -0.4766901
        outSlope: -0.4766901
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.92789197
      - serializedVersion: 3
        time: 23.183332
        value: 3.86
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 25.583334
        value: 4.86
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 28.216667
        value: 3.06
        inSlope: -0.106109485
        outSlope: -0.106109485
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.42964423
      - serializedVersion: 3
        time: 32.116665
        value: 4.34
        inSlope: 1.2879953
        outSlope: 1.2879953
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.8881318
      - serializedVersion: 3
        time: 33.716667
        value: 6.613184
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 35.683334
        value: 1.86
        inSlope: -0.36671644
        outSlope: -0.36671644
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 1
      - serializedVersion: 3
        time: 37.033333
        value: 2.63
        inSlope: 1.5385914
        outSlope: 1.5385914
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.88305396
      - serializedVersion: 3
        time: 38.55
        value: 3.89
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 39.533333
        value: 1.99
        inSlope: -1.2230766
        outSlope: -1.2230766
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 40.283333
        value: 1.46
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 41.45
        value: 3.07
        inSlope: 1.5822994
        outSlope: 1.5822994
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 42.166668
        value: 4.44
        inSlope: 0.813223
        outSlope: 0.813223
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 43.466667
        value: 4.85
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 45.283333
        value: 4.85
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 47.75
        value: 5.02
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 50.283333
        value: 3.83
        inSlope: -0.5469878
        outSlope: -0.5469878
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 51.9
        value: 2.75
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 56.166668
        value: 3.52
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 59.6
        value: 2.88
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 65.9
        value: 6.74
        inSlope: 1.5144982
        outSlope: 1.5144982
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.9051325
      - serializedVersion: 3
        time: 67
        value: 9.564777
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 68.7
        value: 4.17
        inSlope: -0.32939923
        outSlope: -0.32939923
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 70.75
        value: 3.8611884
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 72.98333
        value: 4.25
        inSlope: 0.321542
        outSlope: 0.321542
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 1
      - serializedVersion: 3
        time: 74.833336
        value: 5.7554812
        inSlope: -0.50761086
        outSlope: -0.50761086
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.7918172
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 77.083336
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 80.433334
        value: 2.1
        inSlope: 0.45802838
        outSlope: 0.45802838
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 83
        value: 2.81
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: Pivot/Logic/ActionCameraAim
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 0.76666665
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 4.35
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 6.116667
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 7.483333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 8.05
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 8.3
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 8.65
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 11.283334
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 13.2
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 16.05
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.4
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 21.5
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 23.183332
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 25.583334
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 28.216667
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 32.116665
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 33.683334
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 35.683334
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 37.033333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 38.55
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 39.533333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 40.283333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 41.45
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 42.166668
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 43.466667
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 45.283333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 47.75
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 50.283333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 51.9
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 56.166668
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 59.6
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 65.9
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 67.28333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 68.7
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 70.75
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 72.98333
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 74.4
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 77.083336
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 80.433334
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 83
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: Pivot/Logic/ActionCameraAim
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &7566785530541050524
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &7768846571940883780
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &8058170054742336848
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (3)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7560441604123907686}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 30.45
    m_ClipIn: 0
    m_Asset: {fileID: -5341602934083288157}
    m_Duration: 49.083331963090316
    m_TimeScale: 0.4074703
    m_ParentTrack: {fileID: 8058170054742336848}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 1
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 30.45
    m_DisplayName: Recorded (2)
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &8070366278982587123
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 8267334817970497538}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -542543124592217201}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &8267334817970497538
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Wingman1
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 2887210442281281629}
  m_Children:
  - {fileID: 8070366278982587123}
  - {fileID: 6023563724839486487}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &8340387510386334479
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 393ae220070b77e4e91989c7b740b783
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 721
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &8408581416296155633
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 4843711660086921686}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: -0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 5085793584255174137}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!74 &8431942820850095243
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (9)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 10.55
        value: {x: -3230.4473, y: 0, z: 0}
        inSlope: {x: 33361.27, y: 0, z: 0}
        outSlope: {x: 33361.27, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 3
        inWeight: {x: 0.26615024, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.50573957, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 10.666667
        value: {x: 1.6003418, y: 0, z: 0}
        inSlope: {x: 455.18103, y: 0, z: 0}
        outSlope: {x: 16.95113, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 1
        inWeight: {x: 0.1261809, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      - serializedVersion: 3
        time: 34.683334
        value: {x: 408.71, y: 0, z: 0}
        inSlope: {x: 16.95113, y: 0, z: 0}
        outSlope: {x: 16.95113, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 34.683334
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 10.55
        value: -3230.4473
        inSlope: 33361.27
        outSlope: 33361.27
        tangentMode: 1
        weightedMode: 3
        inWeight: 0.26615024
        outWeight: 0.50573957
      - serializedVersion: 3
        time: 10.666667
        value: 1.6003418
        inSlope: 455.18103
        outSlope: 16.95113
        tangentMode: 65
        weightedMode: 1
        inWeight: 0.1261809
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 34.683334
        value: 408.71
        inSlope: 16.95113
        outSlope: 16.95113
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &8460845461883924138
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -8902001241107755306}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 54.95000076293945
    m_ClipIn: 0
    m_Asset: {fileID: -30525688668304150}
    m_Duration: 9.549999237060547
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8460845461883924138}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &8874520153100909236
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -6122639103416589286}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 62.016666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 2564549211628107529}
    m_Duration: 19.91666666666667
    m_TimeScale: 0.4999999999999999
    m_ParentTrack: {fileID: 8874520153100909236}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: CapitalShipExpode
  - m_Version: 1
    m_Start: 52.63333333333333
    m_ClipIn: 0
    m_Asset: {fileID: -2916079507558419431}
    m_Duration: 9.383333333333333
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8874520153100909236}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 52.63333333333333
    m_DisplayName: Idle
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &8978906843114319546
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 3444815892371042183}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 12.233333333333333
    m_ClipIn: 0
    m_Asset: {fileID: 3241720075537349060}
    m_Duration: 70.71666666666671
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8978906843114319546}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &9046224206614352875
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CapitalShip
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -1595460474559033013}
  m_Children:
  - {fileID: -6062353956093434925}
  - {fileID: -4593545392363439824}
  m_Clips: []
  m_Markers:
    m_Objects: []
