{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish Nested Cluster by collision.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par
Nested cluster allows to simulate a group of objects under one root as a single object and demolish it to single objects and smaller Nested Clusters. While Connected Cluster demolition depends on collision or impact point and can be different every time, Nested Cluster allows to get the same demolition pattern every time.\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be shattered to multiple fragments. These fragments will be used as Shards for Nested Cluster. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Pillar\i0 ", \b position \b0 to [0,10,0], \b rotation \b0 to [0,0,90] and \b scale \b0 to [10,0.5,1]\line\par
{\pntext\f0 3.\tab}Create another \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 4.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [15,1,10]\line\par
{\pntext\f0 5.\tab}Add \b RayFire Shatter \b0 component to Pillar and set \b Type \b0 to \b Voronoi\b0 . In Voronoi properties set \b Amount \b0 to \b 100\b0 .\line\par
{\pntext\f0 6.\tab}Click on the \b Fragment \b0 button, a new gameobject \ldblquote\i Pillar_root\i0\rdblquote  with fragments as it\rquote s children will be created. Destroy original Pillar because we don\rquote t need it anymore.\line\par
{\pntext\f0 7.\tab}Add \b RayFire Rigid \b0 component to Pillar_root.\line\par
{\pntext\f0 8.\tab}Set Rigid \b Initialization \b0 to \b At Start.\b0\line\par
{\pntext\f0 9.\tab}Set \b Simulation \b0 type to \b Dynamic \b0 so the object will start to fall down immediately after Initialization.\line\par
{\pntext\f0 10.\tab}Set \b Object \b0 type to \b Nested Cluster\b0 .\line\par
{\pntext\f0 11.\tab}Set \b Demolition \b0 type to \b Runtime\b0 .\line\line Now let's setup Nested cluster hierarchy and predefine how it will be demolished.\line\par
{\pntext\f0 12.\tab}Right click on Pillar_root gameobject in the Hierarchy window and \b Create Empty \b0 gameobject as it's child. Name it "\i Nest_1\i0 ".\line\par
{\pntext\f0 13.\tab}In the same way create a \b second \b0 empty gameobject and name it "\i Nest_2\i0 ".\line\par
{\pntext\f0 14.\tab}Select approximately 40% of pillar fragments in the viewport starting from the top and set them \b as children \b0 for the \b Nest_1 \b0 object.\line\par
{\pntext\f0 15.\tab}Select approximately 40% of pillar fragments in the viewport starting from the bottom and set them \b as children \b0 for the \b Nest_2 \b0 object. \line\line Now you Pillar_root should have two nested clusters Nest_1 and Nest_2 and some amount of shards as it's children.\line\par
{\pntext\f0 16.\tab}Start Play Mode. \line\line Pillar will fall down and be demolished to two predefined child nested Clusters and shards which were at the middle between them will be simulated separately. In The same way you can create more nested clusters inside child nests and so on. By default nested cluster can be demolished only once. All nested cluster created after demolition won't be demolished anymore. In order to demolish nested cluster even deeper you shoul increase Depth roperty in Limitations properties.\line\par
{\pntext\f0 17.\tab}Turn off Play Mode. In Limitations properties set \b Depth \b0 to \b 2\b0 . \line\par
{\pntext\f0 18.\tab}Start Play Mode. \line\line As you see now Nest_1 and Nest_2 clusters also demolishes during simulation.\line But they are too fragile for now and demolished after first collision. Lets make them not so fragile.\line\par
{\pntext\f0 19.\tab}Turn off Play Mode. In Cluster Demolition properties set \b Demolishable \b0 to \b True\b0 . \line\par
{\pntext\f0 20.\tab}In Limitations properties set \b Solidity \b0 to 0.4.\line\par
{\pntext\f0 21.\tab}Start Play Mode.\fs24\lang1033\line\par

\pard\nowidctlpar\sl276\slmult1\tab Now Nest_1 and Nest_2 clusters demolishes only when they hit the ground.\line\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par
}
 