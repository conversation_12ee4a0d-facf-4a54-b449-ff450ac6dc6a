{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish object to prefragmented reference.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1\b Create \b0 Cube, this will be the cube which will be shattered to multiple fragments in Editor. Then these fragments will be used as references for Cube demolition in Play Mode. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Brick\i0 ", \b position \b0 to [0,10,0] and \b scale \b0 to [2,1,1]\line\par
{\pntext\f0 3.\tab}\b Destroy \b0 it\rquote s default Box collider.\line\par
{\pntext\f0 4.\tab}\b Create \b0 another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 6.\tab}\b Add RayFire Shatter \b0 to Brick.\line\par
{\pntext\f0 7.\tab}In \b Voronoi \b0 properties set \b Amount \b0 to \b 10\b0 .\line\par
{\pntext\f0 8.\tab}Click on the \b Fragment \b0 button to create Brick_root gameobject with fragments as it\rquote s children. Change it\rquote s \b name \b0 to \ldblquote\i Brick_root_10\i0\rdblquote .\line\par
{\pntext\f0 9.\tab}Click on \b Export Last Fragments \b0 at the bottom of Shatter UI in order to save all new fragments meshes into mesh asset and in the Save Fragments To Asset window set file \b name \b0 to "\i Brick_frags_10.asset\i0 " and click on \b Save\b0 . New mesh asset file will be created.\line\line This step is needed in order to save all fragments as prefab. When you generate fragments they store all mesh data in the scene. If you create prefab at this moment you will lose all mesh data because prefab can not store mesh data and it can not refer to the scene as the only source of mesh data.\line\par
{\pntext\f0 10.\tab}\b Drag and drop \b0 Brick_root_10 object from \b Hierarchy \b0 window to \b Project \b0 window into Assets folder to create prefab. \b Name \b0 it \ldblquote\i Brick_root_prefab_10\i0\rdblquote .\line\par
{\pntext\f0 11.\tab}\b Select \b0 the Brick object and click on \b Delete Last \b0 to destroy fragments, we created prefab with all fragments and do not need these fragments in scene anymore.\line\par
{\pntext\f0 12.\tab}In \b Voronoi \b0 properties set \b Amount \b0 to \b 100\b0 .\line\par
{\pntext\f0 13.\tab}Click on the \b Fragment\b0  button to create Brick_root gameobject with fragments as it\rquote s children. Change its \b name \b0 to \ldblquote\i Brick_root_100\i0\rdblquote .\line\par
{\pntext\f0 14.\tab}Click on \b Export Last Fragments \b0 and in the Save Fragments To Asset window set file \b name \b0 to "\i Brick_frags_100.asset\i0 " and click on \b Save\b0 .\line\par
{\pntext\f0 15.\tab}\b Drag and drop \b0 Brick_root_100 object from \b Hierarchy \b0 window to \b Project \b0 window into Assets folder to create prefab. \b Name \b0 it \ldblquote\i Brick_root_prefab_100\i0\rdblquote .\line\par
{\pntext\f0 16.\tab}\b Select \b0 the Brick object and click on \b Delete Last\b0 .\line\par
{\pntext\f0 17.\tab}\b Destroy \b0 Shatter component.\line\line Now we have two prefabs with different amounts of fragments as references for reference demolition.\line\par
{\pntext\f0 18.\tab}Add \b RayFire Rigid \b0 component to Brick.\line\par
{\pntext\f0 19.\tab}Set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 20.\tab}Set \b Demolition Type\b0  to \b Reference Demolition\b0 .\line\par
{\pntext\f0 21.\tab}\b Drag and drop\b0  Brick_root_10 to Element 0 and Brick_root_100 to \b Reference \b0 field in \b Reference Demolition \b0 properties\b .\b0\line\par
{\pntext\f0 22.\tab}\b Start Play Mode\b0 , Brick will fall down and will be demolished to randomly pick one of the reference prefab. But it\rquote s scale is bigger than it should be. This is because our Brick scale is [2,1,1] and by default instantiated reference inherits the original object scale.\line\par
{\pntext\f0 23.\tab}\b Turn off \b0 Play Mode and \b select \b0 Brick.\line\par
{\pntext\f0 24.\tab}In \b Reference Demolition \b0 properties turn off \b Inherit Scale \b0 checkbox.\line\par
{\pntext\f0 25.\tab}\b Start Play Mode\b0 . Now Brick demolished to the referenced prefab with the correct scale. \par

\pard\nowidctlpar\sl276\slmult1\par
Now you can create Brick prefab and instantiate it in Play Mode. Every time when Brick prefab will be demolished it will pick a random prefab from Reference List, instantiate it and replace demolished Brick to instantiated prefab.\par
\par
\par
\par
}
 