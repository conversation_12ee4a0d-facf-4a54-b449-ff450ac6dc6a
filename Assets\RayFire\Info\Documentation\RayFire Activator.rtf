{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fswiss\fcharset0 Calibri;}{\f2\fnil\fcharset2 Symbol;}{\f3\fswiss\fcharset204 Calibri;}{\f4\fnil Consolas;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 RayFire Activator\ulnone\b0\f1\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
\fs22\lang9 It's main purpose is \b Activation \b0 (making dynamic object affected by Gravity) of Rigid objects with  Inactive or Kinematik simulation type. Object which is going to be activated using Activator should have Enabled \b By Activator \b0 checkbox in Rigid's \b Activation \b0 properties.\fs24\lang1033\par
\par
\b\fs48\lang9\tab Components\par
\b0\fs24\lang1033\par
\b\fs22\lang9 Rigid\b0\lang1033 : Allows activator to activate objects with Rigid component. \fs24\par
\b\fs22\lang9\par
RigidRoot\b0\lang1033 : Allows activator to activate shards of RigidRoot component. \fs24\par
\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Gizmo\par
{\fs32{\field{\*\fldinst{HYPERLINK https://youtu.be/geA2VIKsZus }}{\fldrslt{https://youtu.be/geA2VIKsZus\ul0\cf0}}}}\b0\f1\fs24\lang1033\par
\b\fs22\lang9\par
Type\b0\lang1033 : Defines gizmo which will be used to create collider to activate objects.\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f2\'b7\tab\b\f1\fs22 Sphere\b0 : Show Sphere gizmo. \fs24\par
\f2\'b7\tab\b\f1\fs22 Box\b0 : Show Box gizmo.\fs24\par
\f2\'b7\tab\b\f1\fs22 Collider\b0 : Collider which is already applied to object with Activator will be used for activation.\fs24\par
\par

\pard\nowidctlpar\sl276\slmult1\b\fs22\lang9 Radius\b0\lang1033 : Defines size of Sphere gizmo.\par
\fs24\par
\b\fs22\lang9 Size\b0\lang1033 : Defines size of Box gizmo.\par
\par
\b\lang9 Show\b0\lang1033 : Show gizmo which will be used to create collider to activate objects. \fs24\par
\par
\b\fs48\lang9\tab Activation\par
{\fs32{\field{\*\fldinst{HYPERLINK https://youtu.be/9rnIFXpeUP4 }}{\fldrslt{https://youtu.be/9rnIFXpeUP4\ul0\cf0}}}}\f1\fs48\par
\b0\fs24\lang1033\par
\b\fs22 Type\b0 : Define when object will be activated.\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f2\'b7\tab\b\f1\fs22\lang9 On\f3\lang1049  \f1\lang1033 Trigger\lang9  Enter\b0 : \lang1033 Object will be activated when Activator trigger collider will enter object's collider.\fs24\par
\f2\'b7\tab\b\f1\fs22\lang9 On Trigger Exit\b0 : \lang1033 Object will be activated when Activator trigger collider will exit object's collider.\par
\f2\fs24\'b7\tab\b\f1\fs22\lang9 On Collision\b0 : \lang1033 Object will be activated when Activator collider will collide with object's collider.\par

\pard\nowidctlpar\sl276\slmult1\fs24\par
\b\fs22 Delay\b0 : Activation Delay in seconds\b\lang9 .\b0\fs24\lang1033\par

\pard\nowidctlpar\li360\sl276\slmult1\par

\pard\nowidctlpar\sl276\slmult1\b\fs22 Demolish Cluster\b0 : Allows to demolish Connected Cluster and detach it's shards into separate objects. \par
\par
\b\fs48\lang9\tab Force\b0\fs24\lang1033\par
\b\fs22 Apply\b0 : Allows to apply position and angular velocity to activated objects.\par
\par
\b Local Space\b0 : Position Velocity will be applied in Activator local space\b\lang9 .\b0\fs24\lang1033\par
\b\fs22\par
Velocity\b0 : Position Velocity which will be applied to activated object\b\lang9 .\b0\fs24\lang1033\par

\pard\nowidctlpar\li360\sl276\slmult1\par

\pard\nowidctlpar\sl276\slmult1\b\fs22 Spin\b0 : Angular Velocity which will be applied to activated object\b\lang9 .\b0\fs24\lang1033\par
\fs22\par
\b Mode\b0 : Force mode which will be used to apply velocity. \fs24\par
\par
\par
\b\fs48\lang9\tab Animation\par
{\fs32{\field{\*\fldinst{HYPERLINK https://youtu.be/_B2CGIF8TJ8 }}{\fldrslt{https://youtu.be/_B2CGIF8TJ8\ul0\cf0}}}}\b0\f1\fs24\lang1033\par
\fs22\lang9\par
Using Animation properties you can setup simple animation for object with Activator without using Animator and Animation Clips. Object will be animated over position list, in most cases it is enough to activate a group of objects.\par
\par
\b Show Animation\b0 : Reveals Animation properties in user interface.\fs24\lang1033\par
\par
\b\fs22\lang9 Duration\b0 : Total duration in seconds which will take whole animation over Position List.\fs24\lang1033\par
\par
\b\fs22\lang9 Scale Animation\b0 : \lang1033 Allows to animate scale of Activator gizmo. For instance, value 5 means that at the end of animation gizmo's scale will be 5 times bigger. With this property you can start activate a group of objects in all directions from the point where activation started.\fs24\par
\par
\b\fs22\lang9 Position Animation\b0 : Defines the source of animation path\lang1033 .\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f2\par

\pard{\pntext\f2\'B7\tab}{\*\pn\pnlvlblt\pnf2\pnindent0{\pntxtb\'B7}}\nowidctlpar\fi-710\li710\sl276\slmult1\tx720\b\f1\fs22 By Global Position List:\b0\lang9  Use Position list of Vector3 points in global world space. Object will be animated from one point to another starting from the first point\lang1033 .\f2\fs24\par
{\pntext\f2\'B7\tab}\b\f1\fs22 By Static Line:\b0  Use predefined Line. Path will be cached at start.\f2\fs24\par
{\pntext\f2\'B7\tab}\b\f1\fs22 By Dynamic Line:\b0  Use predefined Line. Path will be calculated at every frame by Line\par
{\pntext\f2\'B7\tab}\b By Local Position List:\b0\lang9  Use Position list of Vector3 points in local space. Object will be animated from it's current position\lang1033 .\fs24\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22\lang9 Line\b0 : Line which will be used as animation path\lang1033 .\fs24\par
\par
\b\fs22\lang9 Position List\b0 : List of Vector3 points in global space. Object will be animated from one point to another starting from the first point in list.\par
\par
\b Add\b0\lang1033 : Add current object position in Position List which will be used as animation path.\par
\fs24\par
\b\fs22\lang9 Remove\b0\lang1033 : Remove last position from Position List.\par
\fs24\par
\b\fs22\lang9 Clear All\b0\lang1033 : Clear Position List.\lang9\par
\par
\tab\b\fs48 Public Methods\b0\fs22\par
\par

\pard\sl276\slmult1\f0\lang1033 Animation can be triggered by public method:\par
\cf1\f4\fs19 public\cf2  \cf1 void\cf2  TriggerAnimation()\cf0\f0\fs22\par
\par
stopped by :\par
\cf1\f4\fs19 public\cf2  \cf1 void\cf2  StopAnimation()\par
\cf0\f0\fs22\par
and reset by:\par
\cf1\f4\fs19 public\cf2  \cf1 void\cf2  ResetAnimation()\par

\pard\nowidctlpar\sl276\slmult1\cf0\f1\fs24\par
\par
}
 