%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9061447811962879201
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-8793485379465893502
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -************5197931}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -3.5, y: -63.8, z: -298.4}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 80.39, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 8823280951484426731}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-8503397550228486495
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-8423561234443793937
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion1
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -897449540471447562}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 67.3
    m_ClipIn: 0
    m_Asset: {fileID: -1775131155141789074}
    m_Duration: 10.616666666666674
    m_TimeScale: 1
    m_ParentTrack: {fileID: -8423561234443793937}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BigExplosion1
  m_Markers:
    m_Objects: []
--- !u!114 &-8162673362213653974
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: fec38b1d8bb7aad4885c191cce5cf0f6
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 5321
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-7668481155709308931
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (4)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 637.5, y: -152, z: -124.8}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 276.27527, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 38850412941914002}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-7332197473174375882
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: abb7ffa293494b84696dd357072a2b06
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 8127
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-7171913940256412164
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6448411113288389182}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 98.26666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -1184398160392132876}
    m_Duration: 9.333333333333329
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7171913940256412164}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-6837172057007463580
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4568472260038314761}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 75.16666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 4597641950817892139}
    m_Duration: 8.916666666666657
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6837172057007463580}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!74 &-6572390728432225517
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (3)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: {x: 353.6152, y: 90, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 2.35
        value: {x: 365.1989, y: 90, z: 0}
        inSlope: {x: 3.8936596, y: 3.6281095, z: 3.3659706}
        outSlope: {x: 3.8936596, y: 3.6281095, z: 3.3659706}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 3.45
        value: {x: 364.3264, y: 97.98184, z: 7.405136}
        inSlope: {x: -1.5873518, y: 1.4312704, z: 1.3278599}
        outSlope: {x: -1.5873518, y: 1.4312704, z: 1.3278599}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 5.266667
        value: {x: 360, y: 89.99999, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 9
        value: {x: 362.5231, y: 89.99999, z: 0}
        inSlope: {x: 0.28379762, y: 0.2250372, z: 5.306833}
        outSlope: {x: 0.28379762, y: 0.2250372, z: 5.306833}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 11.55
        value: {x: 362.2471, y: 91.14768, z: 27.06485}
        inSlope: {x: -0.054117836, y: -18.361704, z: 5.306836}
        outSlope: {x: -0.054117836, y: -18.361704, z: 5.306836}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 12.916667
        value: {x: 362.2471, y: 40.34392, z: 27.06486}
        inSlope: {x: 1.9486978, y: -25.264097, z: -3.9250968}
        outSlope: {x: 1.9486978, y: -25.264097, z: -3.9250968}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 15.833333
        value: {x: 373.6145, y: 1.392684, z: 4.168446}
        inSlope: {x: 2.5828953, y: -6.757625, z: -2.6802766}
        outSlope: {x: 2.5828953, y: -6.757625, z: -2.6802766}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 24.016666
        value: {x: 383.9942, y: 0.07894838, z: 24.54206}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: {x: 83.47, y: 0.90999997, z: -67.30966}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 2
        value: {x: 83.68, y: 0.6, z: -67.3096}
        inSlope: {x: 0.1733563, y: 0.6341742, z: 0.000048933358}
        outSlope: {x: 0.1733563, y: 0.6341742, z: 0.000048933358}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 3.45
        value: {x: 83.878235, y: 2.888605, z: -67.30955}
        inSlope: {x: 0.39086172, y: 1.0675397, z: 0.0000058167807}
        outSlope: {x: 0.39086172, y: 1.0675397, z: 0.0000058167807}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 5.266667
        value: {x: 85.05, y: 3.9, z: -67.30959}
        inSlope: {x: 0.8445176, y: 0, z: 0}
        outSlope: {x: 0.8445176, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 9
        value: {x: 88.94, y: 3.65, z: -67.30954}
        inSlope: {x: 1.9307745, y: 0.21790041, z: -0.17425568}
        outSlope: {x: 1.9307745, y: 0.21790041, z: -0.17425568}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 11.55
        value: {x: 96.129944, y: 4.932051, z: -68.19828}
        inSlope: {x: 3.9488173, y: 0.4050486, z: 0.97389764}
        outSlope: {x: 3.9488173, y: 0.4050486, z: 0.97389764}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 12.916667
        value: {x: 103.06995, y: 5.352072, z: -65.059975}
        inSlope: {x: 2.9298663, y: 0.364168, z: 2.0670264}
        outSlope: {x: 2.9298663, y: 0.364168, z: 2.0670264}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 15.833333
        value: {x: 105.34985, y: 6.5799994, z: -59.699924}
        inSlope: {x: 0.41436443, y: -0.6893954, z: 2.6038015}
        outSlope: {x: 0.41436443, y: -0.6893954, z: 2.6038015}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 24.016666
        value: {x: 105.73485, y: -8.148322, z: -32.123146}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 24.016666
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: 353.6152
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.35
        value: 365.1989
        inSlope: 3.8936596
        outSlope: 3.8936596
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.45
        value: 364.3264
        inSlope: -1.5873518
        outSlope: -1.5873518
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.266667
        value: 360
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9
        value: 362.5231
        inSlope: 0.28379762
        outSlope: 0.28379762
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.55
        value: 362.2471
        inSlope: -0.054117836
        outSlope: -0.054117836
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 12.916667
        value: 362.2471
        inSlope: 1.9486978
        outSlope: 1.9486978
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.833333
        value: 373.6145
        inSlope: 2.5828953
        outSlope: 2.5828953
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.016666
        value: 383.9942
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: 90
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.35
        value: 90
        inSlope: 3.6281095
        outSlope: 3.6281095
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.45
        value: 97.98184
        inSlope: 1.4312704
        outSlope: 1.4312704
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.266667
        value: 89.99999
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9
        value: 89.99999
        inSlope: 0.2250372
        outSlope: 0.2250372
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.55
        value: 91.14768
        inSlope: -18.361704
        outSlope: -18.361704
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 12.916667
        value: 40.34392
        inSlope: -25.264097
        outSlope: -25.264097
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.833333
        value: 1.392684
        inSlope: -6.757625
        outSlope: -6.757625
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.016666
        value: 0.07894838
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.35
        value: 0
        inSlope: 3.3659706
        outSlope: 3.3659706
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.45
        value: 7.405136
        inSlope: 1.3278599
        outSlope: 1.3278599
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.266667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9
        value: 0
        inSlope: 5.306833
        outSlope: 5.306833
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.55
        value: 27.06485
        inSlope: 5.306836
        outSlope: 5.306836
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 12.916667
        value: 27.06486
        inSlope: -3.9250968
        outSlope: -3.9250968
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.833333
        value: 4.168446
        inSlope: -2.6802766
        outSlope: -2.6802766
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.016666
        value: 24.54206
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: 83.47
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2
        value: 83.68
        inSlope: 0.1733563
        outSlope: 0.1733563
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.45
        value: 83.878235
        inSlope: 0.39086172
        outSlope: 0.39086172
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.266667
        value: 85.05
        inSlope: 0.8445176
        outSlope: 0.8445176
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9
        value: 88.94
        inSlope: 1.9307745
        outSlope: 1.9307745
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.55
        value: 96.129944
        inSlope: 3.9488173
        outSlope: 3.9488173
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 12.916667
        value: 103.06995
        inSlope: 2.9298663
        outSlope: 2.9298663
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.833333
        value: 105.34985
        inSlope: 0.41436443
        outSlope: 0.41436443
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.016666
        value: 105.73485
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: 0.90999997
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2
        value: 0.6
        inSlope: 0.6341742
        outSlope: 0.6341742
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.45
        value: 2.888605
        inSlope: 1.0675397
        outSlope: 1.0675397
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.266667
        value: 3.9
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9
        value: 3.65
        inSlope: 0.21790041
        outSlope: 0.21790041
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.55
        value: 4.932051
        inSlope: 0.4050486
        outSlope: 0.4050486
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 12.916667
        value: 5.352072
        inSlope: 0.364168
        outSlope: 0.364168
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.833333
        value: 6.5799994
        inSlope: -0.6893954
        outSlope: -0.6893954
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.016666
        value: -8.148322
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 1
        value: -67.30966
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2
        value: -67.3096
        inSlope: 0.000048933358
        outSlope: 0.000048933358
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.45
        value: -67.30955
        inSlope: 0.0000058167807
        outSlope: 0.0000058167807
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.266667
        value: -67.30959
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9
        value: -67.30954
        inSlope: -0.17425568
        outSlope: -0.17425568
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.55
        value: -68.19828
        inSlope: 0.97389764
        outSlope: 0.97389764
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 12.916667
        value: -65.059975
        inSlope: 2.0670264
        outSlope: 2.0670264
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.833333
        value: -59.699924
        inSlope: 2.6038015
        outSlope: 2.6038015
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.016666
        value: -32.123146
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-6554929047438433667
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Enemy1
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -2834330598714738566}
  - {fileID: -553369793749422426}
  - {fileID: 540201678111201761}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-6542615680426452243
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5181177372887684289}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-6234091095613576471
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5181177372887684289}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 5322395171761574889}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-6160717694551367108
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-5837640940944482201
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1246711227180608537}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -10.51, y: 0.62, z: -7.12}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 270, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -6572390728432225517}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-5777333633995373946
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: bc0707b5fe85f6143baf7dbb1892799b
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 8051
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-5616872543312270960
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion1 (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -897449540471447562}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 68.133333********
    m_ClipIn: 0
    m_Asset: {fileID: -5777333633995373946}
    m_Duration: 9.400000000000006
    m_TimeScale: 1
    m_ParentTrack: {fileID: -5616872543312270960}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BigExplosion3
  m_Markers:
    m_Objects: []
--- !u!114 &-5181177372887684289
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Environment
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -6542615680426452243}
  - {fileID: 2789224096023978593}
  - {fileID: -6234091095613576471}
  - {fileID: 348469507894644156}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-5154586772628945331
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (3)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -3664121961000204361}
    m_Duration: 67.433333********
    m_TimeScale: 1
    m_ParentTrack: {fileID: -5154586772628945331}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-4952881034903433137
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (3)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -************5197931}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -777.9, y: 54.8, z: 374.5}
  m_InfiniteClipOffsetEulerAngles: {x: -0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 3070013798264868111}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-4936354040354370142
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-4568472260038314761
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Wingman2
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -6837172057007463580}
  - {fileID: 628949630723404807}
  - {fileID: 4053381459111358096}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-4491679580183008706
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset(Clone)(Clone)
  m_EditorClassIdentifier: 
--- !u!114 &-4427850801937411779
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: -0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -4253784407937970932}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!74 &-4253784407937970932
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (2)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 10.95
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 14.533334
        value: {x: 0, y: 0, z: 35}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 18.033333
        value: {x: 0, y: 0, z: -314.3}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 28.716667
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 32.25
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 38.716667
        value: {x: 0, y: 0, z: -59.6}
        inSlope: {x: 0, y: 0, z: -17.947302}
        outSlope: {x: 0, y: 0, z: -17.947302}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 45.533333
        value: {x: 0, y: 0, z: -268.1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 45.533333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 10.95
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 14.533334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 18.033333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 28.716667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 32.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 38.716667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 45.533333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 10.95
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 14.533334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 18.033333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 28.716667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 32.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 38.716667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 45.533333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 10.95
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 14.533334
        value: 35
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 18.033333
        value: -314.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 28.716667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 32.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 38.716667
        value: -59.6
        inSlope: -17.947302
        outSlope: -17.947302
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 45.533333
        value: -268.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  m_EulerEditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-3712950947551844407
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: -4611924604805506456, guid: cb418478f062c0a4d862280eaa08be74, type: 3}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!74 &-3686379236534645943
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (8)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 75.166664
        value: 0
        inSlope: 0
        outSlope: 74.14725
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 76.75
        value: 117.4
        inSlope: 83.80339
        outSlope: 83.80339
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 78.6
        value: 290.3
        inSlope: 80.92015
        outSlope: 80.92015
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 79.65
        value: 362.1
        inSlope: 68.822464
        outSlope: 68.822464
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 81.73333
        value: 506.4
        inSlope: 79.19725
        outSlope: 79.19725
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 84.03333
        value: 711.4
        inSlope: 94.515335
        outSlope: 94.515335
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 84.7
        value: 778
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 84.7
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 75.166664
        value: 0
        inSlope: 0
        outSlope: 74.14725
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 76.75
        value: 117.4
        inSlope: 83.80339
        outSlope: 83.80339
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 78.6
        value: 290.3
        inSlope: 80.92015
        outSlope: 80.92015
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 79.65
        value: 362.1
        inSlope: 68.822464
        outSlope: 68.822464
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 81.73333
        value: 506.4
        inSlope: 79.19725
        outSlope: 79.19725
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 84.03333
        value: 711.4
        inSlope: 94.515335
        outSlope: 94.515335
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 84.7
        value: 778
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-3664121961000204361
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-3501860107206398346
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 49fa0612f266fc848b692e0300ced97e
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 5692
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-2911586115139831916
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1122350058640911619}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 120.48333333333333
    m_ClipIn: 0
    m_Asset: {fileID: -8162673362213653974}
    m_Duration: 5
    m_TimeScale: 1
    m_ParentTrack: {fileID: -2911586115139831916}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Enemy2Explosion
  m_Markers:
    m_Objects: []
--- !u!114 &-2885453979261027853
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 178eb3a7f9d0f8248a8d4dff39540615
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 7609
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-2834330598714738566
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -6554929047438433667}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -1840299276462521170}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!74 &-2820060950470833149
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: {x: 350.4561, y: 89.99986, z: -0.0000007325616}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 2.0666666
        value: {x: 350.4561, y: 89.99986, z: -0.0000007325616}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 3.4
        value: {x: 362.5479, y: 89.99986, z: 0}
        inSlope: {x: 2.920559, y: 0.000003980554, z: 0.8845707}
        outSlope: {x: 2.920559, y: 0.000003980554, z: 0.8845707}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 5.3166666
        value: {x: 356.3614, y: 89.99988, z: 3.390853}
        inSlope: {x: -0.5540863, y: 0.000003980554, z: -0.10305685}
        outSlope: {x: -0.5540863, y: 0.000003980554, z: -0.10305685}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 7.0333333
        value: {x: 360, y: 89.99988, z: -0.0000007325616}
        inSlope: {x: 1.0597895, y: -0.0000051434126, z: -0.987627}
        outSlope: {x: 1.0597895, y: -0.0000051434126, z: -0.987627}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 8.516666
        value: {x: 360, y: 89.99986, z: 0}
        inSlope: {x: 0, y: -0.0000051434126, z: 0.0000002469309}
        outSlope: {x: 0, y: -0.0000051434126, z: 0.0000002469309}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 9.5
        value: {x: 360, y: 89.99986, z: 0}
        inSlope: {x: 0, y: 0, z: 3.1758432}
        outSlope: {x: 0, y: 0, z: 3.1758432}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 11.666667
        value: {x: 360, y: 89.99986, z: 13.76199}
        inSlope: {x: 0, y: -4.8779283, z: 3.5062912}
        outSlope: {x: 0, y: -4.8779283, z: 3.5062912}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 15.216666
        value: {x: 360, y: 55.36658, z: 16.10817}
        inSlope: {x: 2.6478877, y: -11.029784, z: -1.4593483}
        outSlope: {x: 2.6478877, y: -11.029784, z: -1.4593483}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 19.716667
        value: {x: 383.831, y: -0.0001335144, z: 0}
        inSlope: {x: 2.3419218, y: -6.1518545, z: -1.7897962}
        outSlope: {x: 2.3419218, y: -6.1518545, z: -1.7897962}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 24.383333
        value: {x: 380.9753, y: -0.0001182556, z: 0}
        inSlope: {x: -0.61193204, y: 0.0000032697442, z: 0}
        outSlope: {x: -0.61193204, y: 0.0000032697442, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: {x: 69.09414, y: -0.017, z: -63.329803}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 2.0666666
        value: {x: 69.09414, y: -0.017, z: -63.329803}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 3.4
        value: {x: 69.12015, y: 0.4, z: -63.333557}
        inSlope: {x: 0.03844904, y: 0.27898368, z: -0.003044626}
        outSlope: {x: 0.03844904, y: 0.27898368, z: -0.003044626}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 5.3166666
        value: {x: 69.23015, y: 0.87, z: -63.339832}
        inSlope: {x: 0, y: 0.2872964, z: 0}
        outSlope: {x: 0, y: 0.2872964, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 8.516666
        value: {x: 69.25913, y: 1.87, z: -63.329697}
        inSlope: {x: 0.77283835, y: 0.3165101, z: 0.018813837}
        outSlope: {x: 0.77283835, y: 0.3165101, z: 0.018813837}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 9.5
        value: {x: 70.77014, y: 2.1851783, z: -63.29581}
        inSlope: {x: 2.0708318, y: 0.46472716, z: 0.1003025}
        outSlope: {x: 2.0708318, y: 0.46472716, z: 0.1003025}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 10.8
        value: {x: 74.1567, y: 2.9767928, z: -63.079823}
        inSlope: {x: 2.3696146, y: 0.5538991, z: 0.5149775}
        outSlope: {x: 2.3696146, y: 0.5538991, z: 0.5149775}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 15.216666
        value: {x: 89.75014, y: 5.53, z: -60.489784}
        inSlope: {x: 2.4286258, y: 0.15793127, z: 1.3698986}
        outSlope: {x: 2.4286258, y: 0.15793127, z: 1.3698986}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 19.716667
        value: {x: 95.720116, y: 4.35, z: -50.799603}
        inSlope: {x: 0.6633267, y: -0.47611114, z: 2.009883}
        outSlope: {x: 0.6633267, y: -0.47611114, z: 2.009883}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 24.383333
        value: {x: 95.72008, y: 1.13, z: -42.08977}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 24.383333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 69.09414
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.0666666
        value: 69.09414
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.4
        value: 69.12015
        inSlope: 0.03844904
        outSlope: 0.03844904
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.3166666
        value: 69.23015
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 8.516666
        value: 69.25913
        inSlope: 0.77283835
        outSlope: 0.77283835
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9.5
        value: 70.77014
        inSlope: 2.0708318
        outSlope: 2.0708318
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 10.8
        value: 74.1567
        inSlope: 2.3696146
        outSlope: 2.3696146
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.216666
        value: 89.75014
        inSlope: 2.4286258
        outSlope: 2.4286258
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 19.716667
        value: 95.720116
        inSlope: 0.6633267
        outSlope: 0.6633267
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.383333
        value: 95.72008
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: -0.017
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.0666666
        value: -0.017
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.4
        value: 0.4
        inSlope: 0.27898368
        outSlope: 0.27898368
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.3166666
        value: 0.87
        inSlope: 0.2872964
        outSlope: 0.2872964
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 8.516666
        value: 1.87
        inSlope: 0.3165101
        outSlope: 0.3165101
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9.5
        value: 2.1851783
        inSlope: 0.46472716
        outSlope: 0.46472716
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 10.8
        value: 2.9767928
        inSlope: 0.5538991
        outSlope: 0.5538991
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.216666
        value: 5.53
        inSlope: 0.15793127
        outSlope: 0.15793127
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 19.716667
        value: 4.35
        inSlope: -0.47611114
        outSlope: -0.47611114
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.383333
        value: 1.13
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: -63.329803
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.0666666
        value: -63.329803
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.4
        value: -63.333557
        inSlope: -0.003044626
        outSlope: -0.003044626
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.3166666
        value: -63.339832
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 8.516666
        value: -63.329697
        inSlope: 0.018813837
        outSlope: 0.018813837
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9.5
        value: -63.29581
        inSlope: 0.1003025
        outSlope: 0.1003025
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 10.8
        value: -63.079823
        inSlope: 0.5149775
        outSlope: 0.5149775
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.216666
        value: -60.489784
        inSlope: 1.3698986
        outSlope: 1.3698986
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 19.716667
        value: -50.799603
        inSlope: 2.009883
        outSlope: 2.009883
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.383333
        value: -42.08977
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 350.4561
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.0666666
        value: 350.4561
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.4
        value: 362.5479
        inSlope: 2.920559
        outSlope: 2.920559
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.3166666
        value: 356.3614
        inSlope: -0.5540863
        outSlope: -0.5540863
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 7.0333333
        value: 360
        inSlope: 1.0597895
        outSlope: 1.0597895
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 8.516666
        value: 360
        inSlope: 0
        outSlope: 0
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9.5
        value: 360
        inSlope: 0
        outSlope: 0
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.666667
        value: 360
        inSlope: 0
        outSlope: 0
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.216666
        value: 360
        inSlope: 2.6478877
        outSlope: 2.6478877
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 19.716667
        value: 383.831
        inSlope: 2.3419218
        outSlope: 2.3419218
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.383333
        value: 380.9753
        inSlope: -0.61193204
        outSlope: -0.61193204
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 89.99986
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.0666666
        value: 89.99986
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.4
        value: 89.99986
        inSlope: 0.000003980554
        outSlope: 0.000003980554
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.3166666
        value: 89.99988
        inSlope: 0.000003980554
        outSlope: 0.000003980554
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 7.0333333
        value: 89.99988
        inSlope: -0.0000051434126
        outSlope: -0.0000051434126
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 8.516666
        value: 89.99986
        inSlope: -0.0000051434126
        outSlope: -0.0000051434126
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9.5
        value: 89.99986
        inSlope: 0
        outSlope: 0
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.666667
        value: 89.99986
        inSlope: -4.8779283
        outSlope: -4.8779283
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.216666
        value: 55.36658
        inSlope: -11.029784
        outSlope: -11.029784
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 19.716667
        value: -0.0001335144
        inSlope: -6.1518545
        outSlope: -6.1518545
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.383333
        value: -0.0001182556
        inSlope: 0.0000032697442
        outSlope: 0.0000032697442
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: -0.0000007325616
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 2.0666666
        value: -0.0000007325616
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 3.4
        value: 0
        inSlope: 0.8845707
        outSlope: 0.8845707
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 5.3166666
        value: 3.390853
        inSlope: -0.10305685
        outSlope: -0.10305685
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 7.0333333
        value: -0.0000007325616
        inSlope: -0.987627
        outSlope: -0.987627
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 8.516666
        value: 0
        inSlope: 0.0000002469309
        outSlope: 0.0000002469309
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 9.5
        value: 0
        inSlope: 3.1758432
        outSlope: 3.1758432
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 11.666667
        value: 13.76199
        inSlope: 3.5062912
        outSlope: 3.5062912
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 15.216666
        value: 16.10817
        inSlope: -1.4593483
        outSlope: -1.4593483
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 19.716667
        value: 0
        inSlope: -1.7897962
        outSlope: -1.7897962
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.383333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 16
  m_EulerEditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-2758740602832269911
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1122350058640911619}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 100.45
    m_ClipIn: 0
    m_Asset: {fileID: -9061447811962879201}
    m_Duration: 20.29999999999999
    m_TimeScale: 1
    m_ParentTrack: {fileID: -2758740602832269911}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!74 &-2608549741211874619
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (7)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 57.816666
        value: 1441
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 67.433334
        value: 3283
        inSlope: 65.30644
        outSlope: 65.30644
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.09075501
        outWeight: 0.115197524
      - serializedVersion: 3
        time: 75.166664
        value: 3840.5447
        inSlope: 90.89344
        outSlope: 90.89344
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.26994675
        outWeight: 0.********
      - serializedVersion: 3
        time: 79.25
        value: 4088
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: bce80088733fe3946b425e55b668ff29, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 79.25
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 57.816666
        value: 1441
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 67.433334
        value: 3283
        inSlope: 65.30644
        outSlope: 65.30644
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.09075501
        outWeight: 0.115197524
      - serializedVersion: 3
        time: 75.166664
        value: 3840.5447
        inSlope: 90.89344
        outSlope: 90.89344
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.26994675
        outWeight: 0.********
      - serializedVersion: 3
        time: 79.25
        value: 4088
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: bce80088733fe3946b425e55b668ff29, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-************5197931
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: HangarShips
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 1834202079220336600}
  - {fileID: 68177831816509333}
  - {fileID: -2052179891044703782}
  - {fileID: -4952881034903433137}
  - {fileID: 4690309470236859617}
  - {fileID: -8793485379465893502}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-2196246529474993746
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 67.433333********
    m_ClipIn: 0
    m_Asset: {fileID: 1871357503382142647}
    m_Duration: 21.483334604898666
    m_TimeScale: 1
    m_ParentTrack: {fileID: -2196246529474993746}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-2052179891044703782
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -************5197931}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -4936354040354370142}
    m_Duration: 42.************
    m_TimeScale: 1
    m_ParentTrack: {fileID: -2052179891044703782}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-2040819457966409499
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!74 &-1840299276462521170
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (5)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 74.916664
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 76.7
        value: 99.4
        inSlope: 72.425735
        outSlope: 72.425735
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 80.083336
        value: 374.2
        inSlope: 74.8781
        outSlope: 74.8781
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 83.26667
        value: 591.1
        inSlope: 84.36172
        outSlope: 84.36172
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 86.166664
        value: 887.4
        inSlope: 119.04387
        outSlope: 119.04387
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 88.6
        value: 1226
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 88.6
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 74.916664
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 76.7
        value: 99.4
        inSlope: 72.425735
        outSlope: 72.425735
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 80.083336
        value: 374.2
        inSlope: 74.8781
        outSlope: 74.8781
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 83.26667
        value: 591.1
        inSlope: 84.36172
        outSlope: 84.36172
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 86.166664
        value: 887.4
        inSlope: 119.04387
        outSlope: 119.04387
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 88.6
        value: 1226
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &-1775131155141789074
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 67edee620ce132349ba140c77e33eb38
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 4928
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &-1646430794192326072
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 270.00015, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -2820060950470833149}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-1606887109491071955
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1246711227180608537}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -2040819457966409499}
    m_Duration: 87.133333********
    m_TimeScale: 1
    m_ParentTrack: {fileID: -1606887109491071955}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  - m_Version: 1
    m_Start: 103.1
    m_ClipIn: 0
    m_Asset: {fileID: -4491679580183008706}
    m_Duration: 27.05000000000001
    m_TimeScale: 1
    m_ParentTrack: {fileID: -1606887109491071955}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-1547256206058737415
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Control Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1246711227180608537}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 103.1
    m_ClipIn: 0
    m_Asset: {fileID: -7332197473174375882}
    m_Duration: 27.05000000000001
    m_TimeScale: 1
    m_ParentTrack: {fileID: -1547256206058737415}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Shots
  m_Markers:
    m_Objects: []
--- !u!114 &-1524696171510789751
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1246711227180608537}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -11.601726, y: 4.5797405, z: -6.704723}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 270, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 8177341179493784056}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &-1507516265080249465
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Broken Cruiser
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-1184398160392132876
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-897449540471447562
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: ParticleSystem
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -8423561234443793937}
  - {fileID: 2067405279438487993}
  - {fileID: -5616872543312270960}
  - {fileID: 7917946424577706921}
  - {fileID: 7188112315776441601}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-881400876132788985
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-553369793749422426
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -6554929047438433667}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 74.91666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -8503397550228486495}
    m_Duration: 18.83333333333332
    m_TimeScale: 1
    m_ParentTrack: {fileID: -553369793749422426}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: FlightSequence
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: -1646430794192326072}
  - {fileID: 1092238054094967969}
  - {fileID: -5154586772628945331}
  - {fileID: -7668481155709308931}
  - {fileID: 3803684777848885023}
  - {fileID: -2196246529474993746}
  - {fileID: 4018887810916128407}
  - {fileID: -4427850801937411779}
  - {fileID: 1246711227180608537}
  - {fileID: -4568472260038314761}
  - {fileID: -6554929047438433667}
  - {fileID: 1122350058640911619}
  - {fileID: 6448411113288389182}
  - {fileID: -1507516265080249465}
  - {fileID: 2445314014754719820}
  - {fileID: 4742810798386972232}
  - {fileID: -************5197931}
  - {fileID: -5181177372887684289}
  - {fileID: -897449540471447562}
  m_FixedDuration: 0
  m_EditorSettings:
    m_Framerate: 60
    m_ScenePreview: 1
  m_DurationMode: 0
  m_MarkerTrack: {fileID: 0}
--- !u!74 &38850412941914002
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (6)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.183334
        value: {x: -1100.8151, y: 0, z: 0.17590332}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 64.61667
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.4
        value: {x: 0.8, y: 0.1, z: 0.1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 64.61667
        value: {x: 0.8, y: 0.8, z: 0.8}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 3
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 64.61667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.183334
        value: -1100.8151
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 64.61667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.183334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 64.61667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.183334
        value: 0.17590332
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 64.61667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.4
        value: 0.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 64.61667
        value: 0.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.4
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 64.61667
        value: 0.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.4
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 64.61667
        value: 0.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &68177831816509333
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -************5197931}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: -387, y: -61, z: 181}
  m_InfiniteClipOffsetEulerAngles: {x: -0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 4877593372121646093}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &348469507894644156
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5181177372887684289}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 89.01667022705078
    m_ClipIn: 0
    m_Asset: {fileID: 5307782724731511637}
    m_Duration: 12.395833333333329
    m_TimeScale: 0.4
    m_ParentTrack: {fileID: 348469507894644156}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 89.01667022705078
    m_DisplayName: Asteroid_Explosion
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &540201678111201761
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Control Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -6554929047438433667}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 87.86666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 6809779251188258669}
    m_Duration: 5
    m_TimeScale: 1
    m_ParentTrack: {fileID: 540201678111201761}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Explosion (1)
  m_Markers:
    m_Objects: []
--- !u!114 &628949630723404807
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4568472260038314761}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -3686379236534645943}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &1092238054094967969
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 9076259540051581182}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &1122350058640911619
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Enemy2
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -2758740602832269911}
  - {fileID: 8626135245451553206}
  - {fileID: -2911586115139831916}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &1246711227180608537
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Wingman1
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -1524696171510789751}
  - {fileID: -5837640940944482201}
  - {fileID: -1606887109491071955}
  - {fileID: -1547256206058737415}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &1834202079220336600
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -************5197931}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &1871357503382142647
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &2067405279438487993
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion1 (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -897449540471447562}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 67.91666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 3639973657564191363}
    m_Duration: 10
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2067405279438487993}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BigExplosion2
  m_Markers:
    m_Objects: []
--- !u!114 &2445314014754719820
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 56.68333333333333
    m_ClipIn: 0
    m_Asset: {fileID: 8849208201388429314}
    m_Duration: 68.066241519774
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2445314014754719820}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &2789224096023978593
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5181177372887684289}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -881400876132788985}
    m_Duration: 78.10000000000001
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2789224096023978593}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!74 &2871539410202007830
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (13)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 100.48333
        value: 25.2
        inSlope: 0
        outSlope: 126.11961
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 102.15
        value: 235.4
        inSlope: 109.29167
        outSlope: 109.29167
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 104.666664
        value: 468.1
        inSlope: 85.52363
        outSlope: 85.52363
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 108.839294
        value: 796
        inSlope: 91.71088
        outSlope: 91.71088
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 112.14534
        value: 1142.6
        inSlope: 82.13186
        outSlope: 82.13186
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 115.416664
        value: 1337
        inSlope: 54.79814
        outSlope: 54.79814
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 120.74845
        value: 1604.5
        inSlope: 50.170795
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 120.74845
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 100.48333
        value: 25.2
        inSlope: 0
        outSlope: 126.11961
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 102.15
        value: 235.4
        inSlope: 109.29167
        outSlope: 109.29167
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 104.666664
        value: 468.1
        inSlope: 85.52363
        outSlope: 85.52363
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 108.839294
        value: 796
        inSlope: 91.71088
        outSlope: 91.71088
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 112.14534
        value: 1142.6
        inSlope: 82.13186
        outSlope: 82.13186
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 115.416664
        value: 1337
        inSlope: 54.79814
        outSlope: 54.79814
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 120.74845
        value: 1604.5
        inSlope: 50.170795
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &2935803995245410816
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6448411113288389182}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 107.36666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 7146523518727616681}
    m_Duration: 5
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2935803995245410816}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Enemy3Explosion
  m_Markers:
    m_Objects: []
--- !u!74 &3070013798264868111
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (11)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.4
        value: {x: -2507, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 25.733334
        value: {x: -8.600006, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.566668
        value: {x: 3, y: 0.1, z: 0.1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 25.733334
        value: {x: 1.3, y: 1.3, z: 1.3}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 3
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 25.733334
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.4
        value: -2507
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 25.733334
        value: -8.600006
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.4
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 25.733334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.4
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 25.733334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.566668
        value: 3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 25.733334
        value: 1.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.566668
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 25.733334
        value: 1.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 25.566668
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 25.733334
        value: 1.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &3639973657564191363
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 5c7b5630c2b052e41903a03ddd5e322e
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 846
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &3803684777848885023
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 9125062671267597316}
    m_Duration: 46.63333333333333
    m_TimeScale: 1
    m_ParentTrack: {fileID: 3803684777848885023}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &4018887810916128407
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track (3)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 66.51666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -3712950947551844407}
    m_Duration: 9.9583333********
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4018887810916128407}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 66.51666666666667
    m_DisplayName: HangarShip_Explosion
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &4053381459111358096
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: DeathExplosion
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4568472260038314761}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 83.98333333333333
    m_ClipIn: 0
    m_Asset: {fileID: -2885453979261027853}
    m_Duration: 5
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4053381459111358096}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Explosion
  m_Markers:
    m_Objects: []
--- !u!114 &4597641950817892139
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &4690309470236859617
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -************5197931}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -6160717694551367108}
    m_Duration: 42.************
    m_TimeScale: 1
    m_ParentTrack: {fileID: 4690309470236859617}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &4742810798386972232
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Worm1
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 8928009602769893315}
  - {fileID: 6789895956183767117}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!74 &4877593372121646093
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (10)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.283333
        value: {x: -2228, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 26.616667
        value: {x: -8.600006, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.45
        value: {x: 3, y: 0.1, z: 0.1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 26.616667
        value: {x: 1.3, y: 1.3, z: 1.3}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 3
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 26.616667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.283333
        value: -2228
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 26.616667
        value: -8.600006
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.283333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 26.616667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.283333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 26.616667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.45
        value: 3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 26.616667
        value: 1.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.45
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 26.616667
        value: 1.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 26.45
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 26.616667
        value: 1.3
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &5307782724731511637
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 9001170690567159620, guid: ba74f3632e8a2f24c950d00cbc4e3df6, type: 3}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!74 &5322395171761574889
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (9)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.03333
        value: 0
        inSlope: 0
        outSlope: 0.119521886
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 72.4
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: T
    path: 
    classID: 114
    script: {fileID: ********, guid: 17b2c54359de95a46ab4154f56d50904, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 3187964512
      script: {fileID: ********, guid: 17b2c54359de95a46ab4154f56d50904, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 72.4
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 64.03333
        value: 0
        inSlope: 0
        outSlope: 0.119521886
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 72.4
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: T
    path: 
    classID: 114
    script: {fileID: ********, guid: 17b2c54359de95a46ab4154f56d50904, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &5396592895727837372
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 6448411113288389182}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 6024523024883883090}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!114 &5720028354865500565
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!74 &6024523024883883090
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (14)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 98.333336
        value: 0
        inSlope: 0
        outSlope: 258.5108
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 99.11667
        value: 202.5
        inSlope: 173.43027
        outSlope: 173.43027
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 107.6
        value: 952
        inSlope: 88.34974
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 107.6
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 98.333336
        value: 0
        inSlope: 0
        outSlope: 258.5108
        tangentMode: 65
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 99.11667
        value: 202.5
        inSlope: 173.43027
        outSlope: 173.43027
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 107.6
        value: 952
        inSlope: 88.34974
        outSlope: 0
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &6448411113288389182
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: Enemy3
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -7171913940256412164}
  - {fileID: 5396592895727837372}
  - {fileID: 2935803995245410816}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &6789895956183767117
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 4742810798386972232}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 55.641666666666666
    m_ClipIn: 0
    m_Asset: {fileID: 5720028354865500565}
    m_Duration: 45.133333333333326
    m_TimeScale: 1
    m_ParentTrack: {fileID: 6789895956183767117}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &6809779251188258669
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 45605e32122553a4481dd05959da3e14
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 7034
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &7037985786817094756
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 6c453cae2328c4f4d86a0736824d4ab1
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 3163
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &7146523518727616681
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 48853ae485fa386428341ac1ea122570, type: 3}
  m_Name: ControlPlayableAsset
  m_EditorClassIdentifier: 
  sourceGameObject:
    exposedName: 8256db1af818d4948aeff1989aa696e7
    defaultValue: {fileID: 0}
  prefabGameObject: {fileID: 0}
  updateParticle: 1
  particleRandomSeed: 8204
  updateDirector: 1
  updateITimeControl: 1
  searchHierarchy: 0
  active: 1
  postPlayback: 2
--- !u!114 &7188112315776441601
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: AsteroidExplosion
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -897449540471447562}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 88.71666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 7037985786817094756}
    m_Duration: 8.833333333333329
    m_TimeScale: 1
    m_ParentTrack: {fileID: 7188112315776441601}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BigExplosion5
  m_Markers:
    m_Objects: []
--- !u!114 &7917946424577706921
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 15e0374501f39d54eb30235764636e0e, type: 3}
  m_Name: Explosion4
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -897449540471447562}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 66.95
    m_ClipIn: 0
    m_Asset: {fileID: -3501860107206398346}
    m_Duration: 5
    m_TimeScale: 1
    m_ParentTrack: {fileID: 7917946424577706921}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: BigExplosion4
  m_Markers:
    m_Objects: []
--- !u!74 &8177341179493784056
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (4)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 17.283333
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 23.2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 87.11667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: HandAnimationWeight
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 17.283333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 23.2
        value: 30.6
        inSlope: 6.6724954
        outSlope: 6.6724954
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.75
        value: 125
        inSlope: 19.143377
        outSlope: 19.143377
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 44.433334
        value: 416.6
        inSlope: 29.043976
        outSlope: 29.043976
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 52.233334
        value: 634.8
        inSlope: 24.665346
        outSlope: 24.665346
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 62.6752
        value: 857.8
        inSlope: 21.555511
        outSlope: 21.555511
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 80.933334
        value: 1255
        inSlope: 27.292439
        outSlope: 27.292439
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 87.11667
        value: 1458
        inSlope: 56.682983
        outSlope: 56.682983
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 103.1
        value: 2745.23
        inSlope: 81.95019
        outSlope: 81.95019
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 110.53333
        value: 3377
        inSlope: 73.15964
        outSlope: 73.15964
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 118.62102
        value: 3873
        inSlope: 45.78421
        outSlope: 45.78421
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 128.08333
        value: 4159.146
        inSlope: 2.7747998
        outSlope: 2.7747998
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.23712176
      - serializedVersion: 3
        time: 130.15
        value: 4167.205
        inSlope: 3.8995578
        outSlope: 5.0106335
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.41582716
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 17.283333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 23.2
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 30.116667
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.75
        value: 0.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: InitialModule.startColor.maxColor.a
    path: EngineSparks
    classID: 198
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 103.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 110.3
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 110.316666
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 111.76667
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 111.78333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 113.53333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 113.55
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 114.26667
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 114.28333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 119.745605
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 119.76667
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 121.46131
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 121.48333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: EmissionModule.rateOverTime.scalar
    path: Shots
    classID: 198
    script: {fileID: 0}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 3213738009
      script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 3867240104
      attribute: 2007035361
      script: {fileID: 0}
      typeID: 198
      customType: 27
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 2262273411
      attribute: 2883525743
      script: {fileID: 0}
      typeID: 198
      customType: 27
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 130.15
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 17.283333
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 23.2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 87.11667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: HandAnimationWeight
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 17.283333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 23.2
        value: 30.6
        inSlope: 6.6724954
        outSlope: 6.6724954
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.75
        value: 125
        inSlope: 19.143377
        outSlope: 19.143377
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 44.433334
        value: 416.6
        inSlope: 29.043976
        outSlope: 29.043976
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 52.233334
        value: 634.8
        inSlope: 24.665346
        outSlope: 24.665346
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 62.6752
        value: 857.8
        inSlope: 21.555511
        outSlope: 21.555511
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 80.933334
        value: 1255
        inSlope: 27.292439
        outSlope: 27.292439
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 87.11667
        value: 1458
        inSlope: 56.682983
        outSlope: 56.682983
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 103.1
        value: 2745.23
        inSlope: 81.95019
        outSlope: 81.95019
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 110.53333
        value: 3377
        inSlope: 73.15964
        outSlope: 73.15964
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 118.62102
        value: 3873
        inSlope: 45.78421
        outSlope: 45.78421
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 128.08333
        value: 4159.146
        inSlope: 2.7747998
        outSlope: 2.7747998
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.23712176
      - serializedVersion: 3
        time: 130.15
        value: 4167.205
        inSlope: 3.8995578
        outSlope: 5.0106335
        tangentMode: 5
        weightedMode: 0
        inWeight: 0.41582716
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 7eebeb256d37425489eb57474c7ed69f, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 17.283333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 23.2
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 30.116667
        value: 0.1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.75
        value: 0.8
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: InitialModule.startColor.maxColor.a
    path: EngineSparks
    classID: 198
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 103.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 110.3
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 110.316666
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 111.76667
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 111.78333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 113.53333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 113.55
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 114.26667
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 114.28333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 119.745605
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 119.76667
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 121.46131
        value: 6
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 121.48333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: EmissionModule.rateOverTime.scalar
    path: Shots
    classID: 198
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &8626135245451553206
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 1122350058640911619}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 2871539410202007830}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!74 &8823280951484426731
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (12)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 40.333332
        value: {x: 0, y: 279.591, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 44.35
        value: {x: -17.753, y: 228.045, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Mesh/TurretCannon.001
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 38.066666
        value: {x: -5.072, y: 149.808, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 42.033333
        value: {x: -35.44, y: 149.808, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Mesh/TurretCannon
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 43.183334
        value: {x: 0, y: 63.544, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 45.933334
        value: {x: 0, y: 119.270035, z: 0}
        inSlope: {x: 0, y: 31.431469, z: 0}
        outSlope: {x: 0, y: 31.431469, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 47.216667
        value: {x: -17.585, y: 158.305, z: 0}
        inSlope: {x: -8.251347, y: 27.42122, z: 0}
        outSlope: {x: -8.251347, y: 27.42122, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      - serializedVersion: 3
        time: 48.9
        value: {x: -24.479, y: 220.302, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.********, y: 0.********, z: 0.********}
        outWeight: {x: 0.********, y: 0.********, z: 0.********}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Mesh/TurretCannon.002
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 3377123791
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 422107137
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1346601077
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 48.9
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 40.333332
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 44.35
        value: -17.753
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Mesh/TurretCannon.001
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 40.333332
        value: 279.591
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 44.35
        value: 228.045
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Mesh/TurretCannon.001
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 40.333332
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 44.35
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Mesh/TurretCannon.001
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 38.066666
        value: -5.072
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 42.033333
        value: -35.44
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Mesh/TurretCannon
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 38.066666
        value: 149.808
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 42.033333
        value: 149.808
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Mesh/TurretCannon
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 38.066666
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 42.033333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Mesh/TurretCannon
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 43.183334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 45.933334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 47.216667
        value: -17.585
        inSlope: -8.251347
        outSlope: -8.251347
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.9
        value: -24.479
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Mesh/TurretCannon.002
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 43.183334
        value: 63.544
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 47.216667
        value: 158.305
        inSlope: 27.42122
        outSlope: 27.42122
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.9
        value: 220.302
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Mesh/TurretCannon.002
    classID: 4
    script: {fileID: 0}
    flags: 16
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 43.183334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 47.216667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.9
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Mesh/TurretCannon.002
    classID: 4
    script: {fileID: 0}
    flags: 16
  m_EulerEditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Mesh/TurretCannon.001
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Mesh/TurretCannon.001
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Mesh/TurretCannon.001
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Mesh/TurretCannon
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Mesh/TurretCannon
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Mesh/TurretCannon
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Mesh/TurretCannon.002
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Mesh/TurretCannon.002
    classID: 4
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Mesh/TurretCannon.002
    classID: 4
    script: {fileID: 0}
    flags: 0
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &8849208201388429314
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &8928009602769893315
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 4742810798386972232}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 1
  m_InfiniteClipPostExtrapolation: 1
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 1
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: -2608549741211874619}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
--- !u!74 &9076259540051581182
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Recorded (1)
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 20
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.516666
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 107.333336
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 116.48333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: animationLerp
    path: 
    classID: 114
    script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 20
        value: 0
        inSlope: 0.35793665
        outSlope: 0.35793665
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.061470345
      - serializedVersion: 3
        time: 26.15
        value: 26.94
        inSlope: 7.249105
        outSlope: 7.249105
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.95
        value: 115.97597
        inSlope: 19.656082
        outSlope: 19.656082
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 39.016666
        value: 234.7
        inSlope: 28.19357
        outSlope: 28.19357
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.15
        value: 483.06
        inSlope: 28.150093
        outSlope: 28.150093
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 55.5
        value: 697
        inSlope: 23.302837
        outSlope: 23.302837
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 62.866665
        value: 826
        inSlope: 19.98855
        outSlope: 19.98855
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 76.3121
        value: 1113.0038
        inSlope: 35.181816
        outSlope: 35.181816
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 81.933334
        value: 1496.8
        inSlope: 59.779602
        outSlope: 59.779602
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 85.46667
        value: 1678
        inSlope: 63.62837
        outSlope: 63.62837
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 116.48333
        value: 4034.4514
        inSlope: 53.102524
        outSlope: 53.102524
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 130.15
        value: 4447.613
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 28.4
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.95
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.15
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 49.616665
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 53.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 55.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 57.933334
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 62.866665
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 73.4
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 79.2
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 84.65
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 90
        value: 0.2
        inSlope: -0.03532562
        outSlope: -0.03532562
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 95.97323
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 97.31348
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 107.333336
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 112.48472
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 116.48333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 120.2
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 124.316666
        value: 0.********
        inSlope: -0.*************
        outSlope: -0.*************
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 130.15
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: BankContribution
    path: 
    classID: 114
    script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: **********
      script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 130.15
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 20
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 24.516666
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 107.333336
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 116.48333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: animationLerp
    path: 
    classID: 114
    script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 20
        value: 0
        inSlope: 0.35793665
        outSlope: 0.35793665
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.061470345
      - serializedVersion: 3
        time: 26.15
        value: 26.94
        inSlope: 7.249105
        outSlope: 7.249105
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.95
        value: 115.97597
        inSlope: 19.656082
        outSlope: 19.656082
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 39.016666
        value: 234.7
        inSlope: 28.19357
        outSlope: 28.19357
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.15
        value: 483.06
        inSlope: 28.150093
        outSlope: 28.150093
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 55.5
        value: 697
        inSlope: 23.302837
        outSlope: 23.302837
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 62.866665
        value: 826
        inSlope: 19.98855
        outSlope: 19.98855
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 76.3121
        value: 1113.0038
        inSlope: 35.181816
        outSlope: 35.181816
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 81.933334
        value: 1496.8
        inSlope: 59.779602
        outSlope: 59.779602
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 85.46667
        value: 1678
        inSlope: 63.62837
        outSlope: 63.62837
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 116.48333
        value: 4034.4514
        inSlope: 53.102524
        outSlope: 53.102524
        tangentMode: 34
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 130.15
        value: 4447.613
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: Distance
    path: 
    classID: 114
    script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 28.4
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 34.95
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 48.15
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 49.616665
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 53.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 55.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 57.933334
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 62.866665
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 73.4
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 79.2
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 84.65
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 90
        value: 0.2
        inSlope: -0.03532562
        outSlope: -0.03532562
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 95.97323
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 97.31348
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 107.333336
        value: 0.5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 112.48472
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 116.48333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 120.2
        value: 0.4
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 124.316666
        value: 0.********
        inSlope: -0.*************
        outSlope: -0.*************
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      - serializedVersion: 3
        time: 130.15
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
        weightedMode: 0
        inWeight: 0.********
        outWeight: 0.********
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: BankContribution
    path: 
    classID: 114
    script: {fileID: ********, guid: 8187891e77147754fb5731660a4f663d, type: 3}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
--- !u!114 &9125062671267597316
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
