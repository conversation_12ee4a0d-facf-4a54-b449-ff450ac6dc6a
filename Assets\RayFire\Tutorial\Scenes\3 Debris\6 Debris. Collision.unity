%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 63f2840ffacd2fb47a548d34e7782000,
    type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &81134960
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 81134961}
  - component: {fileID: 81134964}
  - component: {fileID: 81134963}
  - component: {fileID: 81134962}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &81134961
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81134960}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 397037065}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &81134962
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81134960}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &81134963
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81134960}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &81134964
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81134960}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &81349429
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 81349430}
  m_Layer: 0
  m_Name: Dense Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &81349430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81349429}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 749940119}
  m_Father: {fileID: 489251381}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &140272559
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 140272560}
  - component: {fileID: 140272564}
  - component: {fileID: 140272563}
  - component: {fileID: 140272562}
  - component: {fileID: 140272561}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &140272560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272559}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 606190271}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &140272561
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 1
    speedMax: 3
    velocityMin: 0.5
    velocityMax: 1
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &140272562
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 140272560}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 140272564}
  meshRenderer: {fileID: 140272563}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &140272563
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272559}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &140272564
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272559}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &156758136
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 156758137}
  - component: {fileID: 156758141}
  - component: {fileID: 156758140}
  - component: {fileID: 156758139}
  - component: {fileID: 156758138}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &156758137
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156758136}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1652592294}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &156758138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156758136}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 15
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 1
    dampenMin: 0.2
    dampenMax: 0.2
    bounceType: 1
    bounceMin: 0
    bounceMax: 0.1
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &156758139
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156758136}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 3
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 156758137}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 156758141}
  meshRenderer: {fileID: 156758140}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &156758140
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156758136}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &156758141
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156758136}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &220919382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 220919383}
  m_Layer: 0
  m_Name: Glass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &220919383
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 220919382}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1548652933}
  m_Father: {fileID: 489251381}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &397037064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 397037065}
  m_Layer: 0
  m_Name: Everything
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &397037065
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 397037064}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1411412790}
  - {fileID: 81134961}
  m_Father: {fileID: 1818918359}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &398122396
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 398122397}
  - component: {fileID: 398122401}
  - component: {fileID: 398122400}
  - component: {fileID: 398122399}
  - component: {fileID: 398122398}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &398122397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 398122396}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 2102908425}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &398122398
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 398122396}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 1
    speedMax: 3
    velocityMin: 0.5
    velocityMax: 1
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 1
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &398122399
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 398122396}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 398122397}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 398122401}
  meshRenderer: {fileID: 398122400}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &398122400
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 398122396}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &398122401
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 398122396}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &462524945
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 462524946}
  m_Layer: 0
  m_Name: Bounce_Properties
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &462524946
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 462524945}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1652592294}
  - {fileID: 497850525}
  - {fileID: 1574186729}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &489251380
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 489251381}
  m_Layer: 0
  m_Name: Dampen_Material
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &489251381
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 489251380}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1587254719}
  - {fileID: 81349430}
  - {fileID: 220919383}
  - {fileID: 839165004}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &497850524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 497850525}
  m_Layer: 0
  m_Name: 0.7-0.8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &497850525
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 497850524}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1621193637}
  - {fileID: 750292640}
  m_Father: {fileID: 462524946}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &511514761
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 511514762}
  - component: {fileID: 511514765}
  - component: {fileID: 511514764}
  - component: {fileID: 511514763}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &511514762
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 511514761}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2014348150}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &511514763
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 511514761}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &511514764
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 511514761}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &511514765
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 511514761}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &606190270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 606190271}
  m_Layer: 0
  m_Name: 0.1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &606190271
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 606190270}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 140272560}
  - {fileID: 2123601317}
  m_Father: {fileID: 1490460261}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &636836964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 636836965}
  m_Layer: 0
  m_Name: Dampen_Properties
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &636836965
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 636836964}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2115413959}
  - {fileID: 641148377}
  - {fileID: 992419031}
  - {fileID: 2017210368}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &637351185
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 637351186}
  - component: {fileID: 637351189}
  - component: {fileID: 637351188}
  - component: {fileID: 637351187}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &637351186
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 637351185}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1574186729}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &637351187
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 637351185}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &637351188
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 637351185}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &637351189
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 637351185}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &641148376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 641148377}
  m_Layer: 0
  m_Name: 0-0.1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &641148377
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 641148376}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1115626672}
  m_Father: {fileID: 636836965}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &664680498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 664680499}
  m_Layer: 0
  m_Name: Quality
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &664680499
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664680498}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2102908425}
  - {fileID: 1501590131}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &690702134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 690702135}
  - component: {fileID: 690702138}
  - component: {fileID: 690702137}
  - component: {fileID: 690702136}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &690702135
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 690702134}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2102908425}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &690702136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 690702134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &690702137
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 690702134}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &690702138
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 690702134}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &703970542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 703970543}
  m_Layer: 0
  m_Name: Nothing
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &703970543
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 703970542}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2115748292}
  - {fileID: 1255317613}
  m_Father: {fileID: 1818918359}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &749940118
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 749940119}
  - component: {fileID: 749940123}
  - component: {fileID: 749940122}
  - component: {fileID: 749940121}
  - component: {fileID: 749940120}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &749940119
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 749940118}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 81349430}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &749940120
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 749940118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0
    dampenMax: 0
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &749940121
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 749940118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 2
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 749940119}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 749940123}
  meshRenderer: {fileID: 749940122}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &749940122
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 749940118}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &749940123
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 749940118}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &750292639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 750292640}
  - component: {fileID: 750292643}
  - component: {fileID: 750292642}
  - component: {fileID: 750292641}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &750292640
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 750292639}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 497850525}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &750292641
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 750292639}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &750292642
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 750292639}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &750292643
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 750292639}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &839165003
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 839165004}
  m_Layer: 0
  m_Name: Ice
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &839165004
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 839165003}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1073600787}
  m_Father: {fileID: 489251381}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &923361067
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 923361068}
  - component: {fileID: 923361072}
  - component: {fileID: 923361071}
  - component: {fileID: 923361070}
  - component: {fileID: 923361069}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &923361068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923361067}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 19.32, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1574186729}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &923361069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923361067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 15
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 1
    dampenMin: 0.2
    dampenMax: 0.2
    bounceType: 1
    bounceMin: 0.2
    bounceMax: 0.4
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &923361070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923361067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 3
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 923361068}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 923361072}
  meshRenderer: {fileID: 923361071}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &923361071
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923361067}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &923361072
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923361067}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &992419030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 992419031}
  m_Layer: 0
  m_Name: 0,8-0.9
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &992419031
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992419030}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1761719718}
  m_Father: {fileID: 636836965}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1073600786
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1073600787}
  - component: {fileID: 1073600791}
  - component: {fileID: 1073600790}
  - component: {fileID: 1073600789}
  - component: {fileID: 1073600788}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1073600787
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073600786}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 839165004}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1073600788
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073600786}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0
    dampenMax: 0
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1073600789
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073600786}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 8
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1073600787}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1073600791}
  meshRenderer: {fileID: 1073600790}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1073600790
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073600786}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1073600791
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073600786}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1115626671
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1115626672}
  - component: {fileID: 1115626676}
  - component: {fileID: 1115626675}
  - component: {fileID: 1115626674}
  - component: {fileID: 1115626673}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1115626672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115626671}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 641148377}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1115626673
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115626671}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 1
    dampenMin: 0
    dampenMax: 0.1
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1115626674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115626671}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1115626672}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1115626676}
  meshRenderer: {fileID: 1115626675}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1115626675
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115626671}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1115626676
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115626671}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1228170861
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1228170862}
  - component: {fileID: 1228170866}
  - component: {fileID: 1228170865}
  - component: {fileID: 1228170864}
  - component: {fileID: 1228170863}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1228170862
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228170861}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1501590131}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1228170863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228170861}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 1
    speedMax: 3
    velocityMin: 0.5
    velocityMax: 1
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1228170864
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228170861}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1228170862}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1228170866}
  meshRenderer: {fileID: 1228170865}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1228170865
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228170861}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1228170866
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228170861}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1255317612
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1255317613}
  - component: {fileID: 1255317616}
  - component: {fileID: 1255317615}
  - component: {fileID: 1255317614}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1255317613
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1255317612}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 703970543}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1255317614
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1255317612}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1255317615
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1255317612}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1255317616
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1255317612}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &1411412789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1411412790}
  - component: {fileID: 1411412794}
  - component: {fileID: 1411412793}
  - component: {fileID: 1411412792}
  - component: {fileID: 1411412791}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1411412790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1411412789}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18.65, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 397037065}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1411412791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1411412789}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 1
    speedMax: 3
    velocityMin: 0.5
    velocityMax: 1
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1411412792
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1411412789}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1411412790}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1411412794}
  meshRenderer: {fileID: 1411412793}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1411412793
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1411412789}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1411412794
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1411412789}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1001 &1445489124
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1014529264720340, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1026965256934996, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1026965256934996, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1260722613888276, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1359285263087640, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1736276488727964, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalScale.x
      value: 62.527
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalScale.z
      value: 48.85341
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 16.36
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 35.18
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -43.55
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.995008
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.099795856
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 11.455001
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -40.18
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!1 &1490460260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1490460261}
  m_Layer: 0
  m_Name: Radius
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1490460261
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1490460260}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 606190271}
  - {fileID: 2014348150}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1501590130
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1501590131}
  m_Layer: 0
  m_Name: High
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1501590131
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1501590130}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1228170862}
  - {fileID: 1760217280}
  m_Father: {fileID: 664680499}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1548652932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1548652933}
  - component: {fileID: 1548652937}
  - component: {fileID: 1548652936}
  - component: {fileID: 1548652935}
  - component: {fileID: 1548652934}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1548652933
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548652932}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 220919383}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1548652934
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548652932}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0
    dampenMax: 0
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1548652935
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548652932}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 6
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1548652933}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1548652937}
  meshRenderer: {fileID: 1548652936}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1548652936
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548652932}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1548652937
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548652932}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1574186728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1574186729}
  m_Layer: 0
  m_Name: 0.2-0.4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1574186729
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1574186728}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 923361068}
  - {fileID: 637351186}
  m_Father: {fileID: 462524946}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1587254718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1587254719}
  - component: {fileID: 1587254722}
  - component: {fileID: 1587254721}
  - component: {fileID: 1587254720}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1587254719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587254718}
  m_LocalRotation: {x: -0.3602466, y: -0, z: -0, w: 0.93285716}
  m_LocalPosition: {x: 8.83, y: 5.21, z: -3.04}
  m_LocalScale: {x: 32.11055, y: 1, z: 16.132854}
  m_Children: []
  m_Father: {fileID: 489251381}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -42.231003, y: 0, z: 0}
--- !u!65 &1587254720
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587254718}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1587254721
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587254718}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1587254722
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587254718}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1621193636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1621193637}
  - component: {fileID: 1621193641}
  - component: {fileID: 1621193640}
  - component: {fileID: 1621193639}
  - component: {fileID: 1621193638}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1621193637
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1621193636}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18.67, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 497850525}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1621193638
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1621193636}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 15
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 1
    dampenMin: 0.2
    dampenMax: 0.2
    bounceType: 1
    bounceMin: 0.7
    bounceMax: 0.8
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1621193639
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1621193636}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 3
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1621193637}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1621193641}
  meshRenderer: {fileID: 1621193640}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1621193640
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1621193636}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1621193641
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1621193636}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1643337391
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1643337392}
  - component: {fileID: 1643337395}
  - component: {fileID: 1643337394}
  - component: {fileID: 1643337393}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1643337392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643337391}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1652592294}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1643337393
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643337391}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1643337394
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643337391}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1643337395
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643337391}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &1652592293
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1652592294}
  m_Layer: 0
  m_Name: 0-0.1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1652592294
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1652592293}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 156758137}
  - {fileID: 1643337392}
  m_Father: {fileID: 462524946}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1691943171
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1691943172}
  - component: {fileID: 1691943176}
  - component: {fileID: 1691943175}
  - component: {fileID: 1691943174}
  - component: {fileID: 1691943173}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1691943172
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691943171}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 2014348150}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1691943173
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691943171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 1
    speedMax: 3
    velocityMin: 0.5
    velocityMax: 1
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1691943174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691943171}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1691943172}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1691943176}
  meshRenderer: {fileID: 1691943175}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1691943175
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691943171}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1691943176
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1691943171}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1760217279
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1760217280}
  - component: {fileID: 1760217283}
  - component: {fileID: 1760217282}
  - component: {fileID: 1760217281}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1760217280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1760217279}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1501590131}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1760217281
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1760217279}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1760217282
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1760217279}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1760217283
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1760217279}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &1761719717
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1761719718}
  - component: {fileID: 1761719722}
  - component: {fileID: 1761719721}
  - component: {fileID: 1761719720}
  - component: {fileID: 1761719719}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1761719718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761719717}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 992419031}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1761719719
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761719717}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 1
    dampenMin: 0.8
    dampenMax: 0.9
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1761719720
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761719717}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1761719718}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1761719722}
  meshRenderer: {fileID: 1761719721}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1761719721
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761719717}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1761719722
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761719717}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1818918358
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1818918359}
  m_Layer: 0
  m_Name: Collides With
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1818918359
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1818918358}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.85, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 703970543}
  - {fileID: 397037065}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1949620243
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1949620244}
  - component: {fileID: 1949620248}
  - component: {fileID: 1949620247}
  - component: {fileID: 1949620246}
  - component: {fileID: 1949620245}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1949620244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949620243}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 2017210368}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1949620245
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949620243}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 0
    speedMax: 0
    velocityMin: 0.5
    velocityMax: 1.5
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    quality: 0
    radiusScale: 0.1
    dampenType: 1
    dampenMin: 0.1
    dampenMax: 0.9
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &1949620246
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949620243}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 1949620244}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 1949620248}
  meshRenderer: {fileID: 1949620247}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1949620247
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949620243}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1949620248
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949620243}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &2014348149
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2014348150}
  m_Layer: 0
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2014348150
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2014348149}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1691943172}
  - {fileID: 511514762}
  m_Father: {fileID: 1490460261}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2017210367
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2017210368}
  m_Layer: 0
  m_Name: 0.1-0.9
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2017210368
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2017210367}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1949620244}
  m_Father: {fileID: 636836965}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2102908424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2102908425}
  m_Layer: 0
  m_Name: Medium
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2102908425
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2102908424}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 398122397}
  - {fileID: 690702135}
  m_Father: {fileID: 664680499}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2115413958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2115413959}
  - component: {fileID: 2115413962}
  - component: {fileID: 2115413961}
  - component: {fileID: 2115413960}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2115413959
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115413958}
  m_LocalRotation: {x: -0.3602466, y: -0, z: -0, w: 0.93285716}
  m_LocalPosition: {x: 8.83, y: 5.21, z: -3.04}
  m_LocalScale: {x: 32.11055, y: 1, z: 16.132854}
  m_Children: []
  m_Father: {fileID: 636836965}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -42.231003, y: 0, z: 0}
--- !u!65 &2115413960
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115413958}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &2115413961
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115413958}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2115413962
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115413958}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2115748291
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2115748292}
  - component: {fileID: 2115748296}
  - component: {fileID: 2115748295}
  - component: {fileID: 2115748294}
  - component: {fileID: 2115748293}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2115748292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115748291}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.09, y: 18, z: -0.91}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 703970543}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2115748293
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115748291}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33750d88507d69040b28ccc857c648d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  onDemolition: 1
  onActivation: 0
  onImpact: 0
  debrisReference: {fileID: 1443819839238964, guid: 6a6c940155772b84980d334830bbff55,
    type: 3}
  debrisMaterial: {fileID: 2100000, guid: 4ece9c91fc0a6e440ba0a9d4ff2e2c65, type: 2}
  emissionMaterial: {fileID: 0}
  emission:
    burstType: 3
    burstAmount: 10
    burstVar: 5
    distanceRate: 1
    duration: 4
    lifeMin: 2
    lifeMax: 13
    sizeMin: 0.5
    sizeMax: 2
  dynamic:
    speedMin: 1
    speedMax: 3
    velocityMin: 0.5
    velocityMax: 1
    gravityMin: 0.8
    gravityMax: 1.1
    rotationSpeed: 0.5
  noise:
    enabled: 1
    quality: 2
    strengthMin: 0.3
    strengthMax: 0.6
    frequency: 0.3
    scrollSpeed: 0.7
    damping: 1
  collision:
    collidesWith:
      serializedVersion: 2
      m_Bits: 0
    quality: 0
    radiusScale: 0.1
    dampenType: 0
    dampenMin: 0.2
    dampenMax: 0.4
    bounceType: 0
    bounceMin: 0.2
    bounceMax: 0.6
  limitations:
    minParticles: 3
    maxParticles: 100
    visible: 0
    percentage: 100
    sizeThreshold: 0.5
  rendering:
    castShadows: 1
    receiveShadows: 1
    motionVectors: 1
    lightProbes: 0
    l: 0
    layer: 0
    t: 0
    tag: 
  pool:
    id: 0
    enable: 0
    warmup: 0
    cap: 10
    rate: 1
    skip: 0
    reuse: 1
    over: 5
    repooliflowfps: 0
--- !u!114 &2115748294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115748291}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 5
  physics:
    mt: 5
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0
    vel: 0
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0.1
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 11
    var: 0
    dpf: 0.5
    bias: 0.5
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 9
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 4
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 2
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: -0, y: -0, z: -0, w: 1}
  transForm: {fileID: 2115748292}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 2115748296}
  meshRenderer: {fileID: 2115748295}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2115748295
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115748291}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2115748296
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2115748291}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Collision
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1818918359}
  - {fileID: 664680499}
  - {fileID: 1490460261}
  - {fileID: 636836965}
  - {fileID: 489251381}
  - {fileID: 462524946}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2123601316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2123601317}
  - component: {fileID: 2123601320}
  - component: {fileID: 2123601319}
  - component: {fileID: 2123601318}
  m_Layer: 0
  m_Name: Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2123601317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2123601316}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.052, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 606190271}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2123601318
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2123601316}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    loc: 0
    off: 0.1
    vel: 0.03
    dmg: 0
    act: 0
    imp: 0
    con: 0
    uny: 0
    atb: 0
    l: 0
    lay: 0
    cnt: {fileID: 0}
  limitations:
    col: 1
    sol: 0
    tag: Untagged
    depth: 1
    time: 1
    size: 0.1
    vis: 0
    bld: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    am: 5
    var: 0
    dpf: 1
    bias: 0
    sd: 1
    use: 0
    cld: 1
    cls: 0
    sim: 4
    inp: 6
    prp:
      col: 0
      szF: 0
      dec: 0
      rem: 1
      l: 1
      lay: 0
      t: 1
      tag: 
    ch:
      tp: 0
      frm: 3
      frg: 5
      skp: 0
    sht: {fileID: 0}
    rotStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      cachedHost: 0
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    en: 0
    max: 100
    cur: 0
    col: 0
    mlt: 1
    shr: 0
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  rfMeshes: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skr: {fileID: 0}
  rest: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2123601319
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2123601316}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2123601320
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2123601316}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
