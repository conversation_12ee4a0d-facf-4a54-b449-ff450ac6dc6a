{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to activate Inactive objects by collision\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Sphere\b0 . This is the object which will collide with Inactive fragments and activate them by collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Rock\i0 " and \b position \b0 to [0,10,0]\line\par
{\pntext\f0 3.\tab}Add \b RayFire Rigid \b0 component to Rock and set \b Initialization \b0 to \b At Start.\b0\line\par
{\pntext\f0 4.\tab}Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 6.\tab}Create another \b Cube\b0 . \line\par
{\pntext\f0 7.\tab}Set its \b name \b0 to "\i Floor\i0 ", \b position \b0 to [0,5,0] and \b scale \b0 to [4,0.2,4]\line\par
{\pntext\f0 8.\tab}Add \b RayFire Shatter \b0 to the Floor object, in \b Voronoi \b0 properties set \b Amount \b0 to \b 300\b0  and click the \b Fragment \b0 button. New object Floor_root will be created.\line\par
{\pntext\f0 9.\tab}\b Destroy \b0 or \b Deactivate \b0 Floor object, we won\rquote t need it anymore.\line\par
{\pntext\f0 10.\tab}Add \b RayFire Rigid \b0 to the Floor_root object and set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 11.\tab}Set \b Simulation type \b0 to \b Inactive\b0 . Inactive objects simulate like dynamic objects but they always have 0 gravity and 0 velocity until they will be activated.\line\par
{\pntext\f0 12.\tab}Set \b Object type \b0 to \b Mesh Root\b0 . It means that this Rigid component will be copied to all children objects with mesh with object Type Mesh instead of Mesh Root. \line\par
{\pntext\f0 13.\tab}\b Start \b0 Play Mode. \line\line\tab Rock will fall to the floor fragments and start to slowly push them and at some point it will make hole big enough to fall to the Ground. Notice that some pushed floor fragments freeze in the air. Obviously this looks unrealistic and to fix this we need to activate such fragments.\line\par
{\pntext\f0 14.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 15.\tab}\b Select \b0 Floor_root object and in \b Activation \b0 properties set \b Velocity \b0 property to \b 0.3\b0\line\line\tab When something hits an inactive object it gets velocity and if this velocity will be higher than By Velocity value object will be activated, otherwise it\rquote s velocity will be set back to 0.\line\par
{\pntext\f0 16.\tab}\b Start \b0 Play Mode. \line\line\tab Now Rock activates too many fragments, even fragments which got very low velocity because they were pushed by other pushed fragments.\line\par
{\pntext\f0 17.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 18.\tab}\b Select \b0 Floor_root object and in \b Activation \b0 properties set \b Velocity \b0 property to \b 3\b0 .\line\par
{\pntext\f0 19.\tab}\b Start \b0 Play Mode. \line\line\tab It looks much better now, some fragments close to the collision point were activated, but some fragments around the collision point still freeze in the air. We can fix this by lowering By Velocity value so they also will be activated but there is better way specially for fragments which can be in constant low collision with other dynamic objects, such fragments always will be pushed with low velocity and you will have to set By Velocity property very low to activate them which is not what you may need. \line\par
{\pntext\f0 20.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 21.\tab}\b Select \b0 Floor_root object and in \b Activation \b0 properties set \b Offset \b0 property to \b 0.1. \b0 This property measures in units and allows to activate the object if it was moved from its initial position to By Offset value.\line\par
{\pntext\f0 22.\tab}\b Start \b0 Play Mode. \line\line\fs24\lang1033\tab\fs22\lang9 Now fragments close to the collision point activates By Velocity and fragments around collision points activates By Offset.\fs24\lang1033\line\par
{\pntext\f0 23.\tab}\b\fs22\lang9 Select \b0 the group of fragments in scene view and click on \b Activate \b0 button on top of Rigid component UI. This is a manual way to activate specific fragments, to do this in your code you can use the Rigid component public method \b Activate().\b0\fs24\lang1033\par
}
 