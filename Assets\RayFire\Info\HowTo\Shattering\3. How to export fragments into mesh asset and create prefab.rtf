{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to export fragments into mesh asset and create prefab.\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create a \b Cube\b0 .\line\par
{\pntext\f0 2.\tab}Add \b RayFire Shatter \b0 component to the Cube. \line\par
{\pntext\f0 3.\tab}Click on the \b Fragment \b0 button. A new gameobject \ldblquote\i Cube_root\i0\rdblquote  with fragments as it\rquote s children will be created.\line\par
{\pntext\f0 4.\tab}\b Drag and drop\b0  Cube_root object from \b Hierarchy \b0 window to \b Project \b0 window into Assets folder to create \b prefab\b0 . Name it \ldblquote\i Cube_root_prefab\i0\rdblquote .\line\par
{\pntext\f0 5.\tab}\b Drag and drop \b0 Cube_root_prefab from \b Project \b0 window to \b Scene \b0 view.\line\line\tab As you can see, instantiated Cube_root_prefab doesn\rquote t show any fragment meshes in scene view. Select one of it\rquote s child fragment and you will see that the Mesh Filter component has no mesh, it\rquote s Mesh field shows None. \line\tab This happens because after shattering all new generated mesh data stores only in scene, not in project asset. Since prefab itself can not store Unity mesh it loses all mesh data. To fix this we need to save all fragments meshes into Mesh Asset before creating prefab.\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 Cube_root_prefab in the \b Scene \b0 view and prefab in the \b Asset \b0 folder.\line\par
{\pntext\f0 7.\tab}\b Select \b0 original Cube.\line\par
{\pntext\f0 8.\tab}At the bottom of the RayFire Shatter component open \b Export \b0 properties and set \b Source \b0 to \b Last Fragments\b0 .\line\par
{\pntext\f0 9.\tab}Click on \b Export last Fragments \b0 Button.\line\par
{\pntext\f0 10.\tab}In the \b Save Fragments to Asset \b0 window select the project folder where you want to store mesh asset and click on \b Save\b0 . New Unity Mesh asset will be created in a defined folder. Keep in mind that by defualt mesh asset will be saved in \b\i Assets \b0\i0 folder.\line\par
{\pntext\f0 11.\tab}\b Select \b0 one of the fragments and open it\rquote s Mesh Filter component. Click on \b Mesh \b0 field to highlight in the Project window it\rquote s mesh saved in Mesh asset. Now fragment\rquote s mesh referencing to asset and can be saved as a prefab.\line\par
{\pntext\f0 12.\tab}\b Drag and drop \b0 Cube_root object from \b Hierarchy \b0 window to \b Project \b0 window into Assets folder to create prefab. Name it \ldblquote Cube_root_prefab_new\rdblquote .\line\par
{\pntext\f0 13.\tab}\b Drag and drop \b0 Cube_root_prefab_new from \b Project \b0 window to \b Scene \b0 view.\par

\pard\nowidctlpar\sl276\slmult1\par
\tab Now you can see that instantiated prefab shows all meshes in the scene window.\par
\par
\tab In this example we created a mesh asset for last fragments, but in case you have a root with fragments which you created long ago and you don\rquote t have an original object with Shatter you can Export Meshes by adding Shatter component to the root itself and then in Export properties set Source to Children. In this case Shatter will export all children meshes to mesh asset\par
\par
\lang1033 IMPORTANT: It is recommended to export root with fragments into FBX asset using external plugins. Unity mesh file format is not optimized and size can be twice bigger than the same meshes stored in FBX asset.\lang9\par
\par

\pard\nowidctlpar\sl276\slmult1\qc\par
\par
}
 