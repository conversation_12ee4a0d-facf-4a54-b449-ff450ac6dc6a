{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to destroy demolition fragments after delay\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create Cube, this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its name to "\i Brick\i0 ", position to [0,5,0], rotation to [30, 0, 50] and scale to [2,1,1]\line\par
{\pntext\f0 3.\tab}Remove it's Box Collider because Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "\i Ground\i0 ", position to [0,0,0] and scale to [5,1,5]\line\par
{\pntext\f0 6.\tab}Add \b RayFire Rigid \b0 component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid \b Initialization to At Start\b0 . \line\par
{\pntext\f0 8.\tab}Set \b Simulation \b0 type to \b Dynamic\b0 .\line\par
{\pntext\f0 9.\tab}Set \b Object \b0 type to \b Mesh\b0 .\line\par
{\pntext\f0 10.\tab}Set \b Demolition \b0 type to \b Runtime\b0 .\line\par
{\pntext\f0 11.\tab}In \b Fading \b0 properties enable \b On Demolition \b0 property. Fading allows you to destroy demolition fragments or activated objects after some delay.\line\par
{\pntext\f0 12.\tab}Set \b Life Type \b0 to \b By Simulation And Life Time\b0 . It means that the object will be simulated and only when it gets rest will the Life Time timer start ticking. Object will start Fade when the Life Time timer will stop ticking.\line\par
{\pntext\f0 13.\tab}Set \b Life Time \b0 to \b 2\b0  seconds and \b Life Variation \b0 to \b 1\b0  second. It means that every fragment will have Life Time in a random range between 2 and 3 (2+1) seconds.\line\par
{\pntext\f0 14.\tab}Set \b Fade Type \b0 to \b Scale Down\b0 . This type allows the fragments to slowly scale fragments to [0,0,0] scale and they destroy them completely.\line\par
{\pntext\f0 15.\tab}\b Start \b0 Play Mode. \line\line\tab Brick will fall down and will be demolished to fragments. Notice how fragments start to scale down to [0,0,0] after several seconds and getting destroyed at the end.\line\par
{\pntext\f0 16.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 17.\tab}\b Select \b0 Brick, in \b Fading \b0 properties set \b Fade Type \b0 to \b Sim Exclude\b0 .\line\par
{\pntext\f0 18.\tab}\b Start \b0 Play Mode. \line\line\tab Brick will fall down and will be demolished to fragments. This nothing noticeable won\rquote t happen but if you will select one of the fragments you will see that it is already lost or about to lose its Collider, Rigid Body and RayFire Rigid components. This Fade Type allows you to keep all meshes in scene but remove objects from the physics world to save resources.\line\par
{\pntext\f0 19.\tab}\b Turn Off \b0 Play Mode. \line\line\tab Now let's fade Brick itself.\line\par
{\pntext\f0 20.\tab}\b Select \b0 Brick and set \b Demolition Type \b0 to \b None\b0 .\line\par
{\pntext\f0 21.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 22.\tab}Click on the \b Fade \b0 button on top of Brick's RayFireRigid component UI.\line\line\tab In this way you can manually fade any object with Rigid component, to fade objects inside your code you can use RayFire Rigid's public method \b Fade().\b0\line\tab Also keep in mind that you can initiate fading when Inactive or Kinematic objects get activated, to do so enable On Activation property in Fading properties.\par

\pard\nowidctlpar\sl276\slmult1\line\par

\pard\nowidctlpar\li710\sl276\slmult1\par
\par
}
 