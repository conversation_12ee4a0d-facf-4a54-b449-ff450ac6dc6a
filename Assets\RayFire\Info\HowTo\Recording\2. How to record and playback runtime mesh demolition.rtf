{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to record and playback runtime mesh demolition\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\lang1033\par
\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 2.\tab}Create another \b Cube\b0 , set its \b name \b0 to "\i Brick\i0 ", \b position \b0 to [0,10,0], \b rotation \b0 to [30, 0, 50] and \b scale \b0 to [2,1,1]\line\par
{\pntext\f0 3.\tab}\b Remove \b0 it's Box Collider because the Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Add \b Rayfire Rigid \b0 component to Brick, set \b Initialization \b0 to \b At Start \b0 and \b Demolition \b0 type to \b Runtime\b0 .\line\par
{\pntext\f0 5.\tab}In \b Limitations \b0 properties det \b Depth \b0 to \b 2\b0 , so every fragment will be demolished again.\line\par
{\pntext\f0 6.\tab}In \b Mesh Demolition\b0  properties set \b Contact Bias \b0 to \b 0.8\b0 , so there will be more tiny fragments near collision point.\line\par
{\pntext\f0 7.\tab}\b Start \b0 Play Mode.\line\line Brick will fall down and will be demolished by collision. Now let's record this demolition.\line\par
{\pntext\f0 8.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 9.\tab}\b\lang1033 Create \b0 empty Gameobject and set its name to "\i Recorder\i0 "\lang9 .\line\par
{\pntext\f0 10.\tab}Add \b Rayfire Recorder \b0 component to Recorder object and set \b Clip Name \b0 to "\i BrickDemolition\i0 "\line\par
{\pntext\f0 11.\tab}\b Select \b0 Brick object and in \b Hierarchy \b0 drag and drop it so it will be \b child \b0 of Recorder object. \line\par
{\pntext\f0 12.\tab}\b Start \b0 Play Mode. \line\line Brick will be demolished and \i RayFireRecords \i0 folder will be created in Assets folder. In this folder "\i BrickDemolition_animation\i0 " animation clip and "\i BrickDemolition_controller\i0 " runtime animation controller assets will be created.\line\line Also, there will be created two folders. "\i BrickDemolition_meshes\i0 " will contain all runtime fragments mesh assets and "\i BrickDemolition_prefabs\i0 " will contain all new fragments prefab assets. \line\par
{\pntext\f0 13.\tab}\b Turn Off \b0 Play Mode.\line\line Now we need to playback recorded simulation.\line\par
{\pntext\f0 14.\tab}\b Select \b0 recorder object and change \b Mode \b0 to \b Play\b0 . New properties will be revealed.\fs24\lang1033\line\par
{\pntext\f0 15.\tab}Enable \b On Start \b0 toggle.\line\fs22\lang9\par
{\pntext\f0 16.\tab}\b\fs24\lang1033 Drag and drop "\b0\i\fs22\lang9 BrickDemolition_animation\i0 " asset to \b Clip \b0 property field.\line\par
{\pntext\f0 17.\tab}\b\fs24\lang1033 Drag and drop \b0 "\i\fs22\lang9 BrickDemolition_controller\i0 " asset to Controller property field.\line\par
{\pntext\f0 18.\tab}\b Select\b0  all prefab assets in "\i BrickDemolition_prefabs\i0 " folder and \b drag and drop \b0 them into \b Hierarchy \b0 window under Recorder object, so they will be its \b children\b0 .\line\par
{\pntext\f0 19.\tab}\b Deactivate \b0 all prefabs using toggle at the top of the Inspector, we need them to be deactivated at start so they will be activated when Brick object will be demolished.\line\par
{\pntext\f0 20.\tab}\b Select \b0 Brick object and \b destroy \b0 Rayfire Rigid component since we do not need it anymore. This step is optional.\line\par
{\pntext\f0 21.\tab}\b Start \b0 Play Mode. \line\line Brick will fall down and will be demolished to several fragments. Every fragment then will be demolished at collision again. \par

\pard\nowidctlpar\sl276\slmult1\par
\line\par
\tab\par
}
 