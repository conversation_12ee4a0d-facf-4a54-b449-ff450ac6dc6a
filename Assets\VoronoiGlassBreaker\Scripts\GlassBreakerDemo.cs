using UnityEditor;
using UnityEngine;

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Demo script that sets up a complete glass breaking scene
    /// </summary>
    public class GlassBreakerDemo : MonoBehaviour
    {
        [Header("Glass Setup")]
        public GameObject glassPrefab;
        public Material glassMaterial;
        public Vector3 glassPosition = Vector3.zero;
        public Vector3 glassScale = Vector3.one;
        
        [Header("Demo Objects")]
        public GameObject projectilePrefab;
        public Transform playerSpawnPoint;
        
        [Header("Environment")]
        public GameObject floorPrefab;
        public GameObject[] decorationPrefabs;
        public int decorationCount = 5;
        public float decorationRadius = 10f;
        
        private VoronoiGlassBreaker glassBreaker;
        private GlassBreakerTester tester;
        private GlassAudioManager audioManager;
        
        private void Start()
        {
            SetupScene();
        }
        
        private void SetupScene()
        {
            CreateGlass();
            CreateFloor();
            CreateDecorations();
            SetupPlayer();
            SetupAudio();
            SetupTester();
        }
        
        private void CreateGlass()
        {
            GameObject glassObj;
            
            if (glassPrefab != null)
            {
                glassObj = Instantiate(glassPrefab, glassPosition, Quaternion.identity);
            }
            else
            {
                // Create a simple glass plane
                glassObj = GameObject.CreatePrimitive(PrimitiveType.Plane);
                glassObj.name = "VoronoiGlass";
                glassObj.transform.position = glassPosition;
                glassObj.transform.rotation = Quaternion.Euler(90, 0, 0); // Make it vertical
            }
            
            glassObj.transform.localScale = glassScale;
            
            // Add Voronoi Glass Breaker component
            glassBreaker = glassObj.AddComponent<VoronoiGlassBreaker>();
            
            // Configure settings
            var settings = glassBreaker.Settings;
            settings.cellCount = 30;
            settings.relaxationIterations = 2;
            settings.breakForce = 5f;
            settings.breakRadius = 1f;
            settings.enableProgressiveBreaking = true;
            settings.propagationSpeed = 3f;
            settings.propagationRadius = 0.4f;
            settings.fragmentLifetime = 8f;
            settings.fragmentMass = 0.1f;
            settings.impactForceMultiplier = 1.2f;
            
            // Set material
            if (glassMaterial != null)
            {
                glassObj.GetComponent<MeshRenderer>().material = glassMaterial;
            }
            else
            {
                // Create a simple transparent material with URP shader
                Material defaultGlass = new Material(GetURPShader());
                defaultGlass.name = "DefaultDemoGlass";

                // Set up transparency for URP
                defaultGlass.SetFloat("_Surface", 1); // Transparent surface type
                defaultGlass.SetFloat("_Blend", 0); // Alpha blend mode
                defaultGlass.SetFloat("_SrcBlend", (float)UnityEngine.Rendering.BlendMode.SrcAlpha);
                defaultGlass.SetFloat("_DstBlend", (float)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                defaultGlass.SetFloat("_ZWrite", 0);
                defaultGlass.SetFloat("_AlphaClip", 0);
                defaultGlass.EnableKeyword("_SURFACE_TYPE_TRANSPARENT");
                defaultGlass.EnableKeyword("_ALPHABLEND_ON");
                defaultGlass.renderQueue = 3000;
                defaultGlass.color = new Color(0.8f, 0.9f, 1f, 0.3f);
                defaultGlass.SetFloat("_Metallic", 0.1f);
                defaultGlass.SetFloat("_Smoothness", 0.9f); // URP uses _Smoothness

                glassObj.GetComponent<MeshRenderer>().material = defaultGlass;
            }
            
            // Set up collider - ensure it's suitable for triggers
            Collider collider = glassObj.GetComponent<Collider>();
            if (collider != null)
            {
                // Check if it's a MeshCollider and if it's concave
                MeshCollider meshCol = collider as MeshCollider;
                if (meshCol != null && !meshCol.convex)
                {
                    if (Application.isPlaying)
                    {
                        // At runtime, just make it convex
                        meshCol.convex = true;
                        meshCol.isTrigger = true;
                    }
                    else
                    {
                        // Make the MeshCollider convex for trigger support
                        meshCol.convex = true;
                        meshCol.isTrigger = true;
                    }
                }
                else
                {
                    collider.isTrigger = true;
                }
            }
            
            Debug.Log("Glass created and configured");
        }
        
        private void CreateFloor()
        {
            if (floorPrefab != null)
            {
                GameObject floor = Instantiate(floorPrefab, Vector3.down * 2f, Quaternion.identity);
                floor.transform.localScale = new Vector3(20, 1, 20);
            }
            else
            {
                // Create a simple floor
                GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
                floor.name = "Floor";
                floor.transform.position = Vector3.down * 2f;
                floor.transform.localScale = new Vector3(20, 1, 20);
                
                // Add a simple material with URP shader
                Material floorMat = new Material(GetURPShader());
                floorMat.color = new Color(0.5f, 0.5f, 0.5f);
                floor.GetComponent<MeshRenderer>().material = floorMat;
            }
        }
        
        private void CreateDecorations()
        {
            if (decorationPrefabs == null || decorationPrefabs.Length == 0) return;
            
            for (int i = 0; i < decorationCount; i++)
            {
                GameObject prefab = decorationPrefabs[Random.Range(0, decorationPrefabs.Length)];
                if (prefab == null) continue;
                
                Vector3 randomPos = Random.insideUnitCircle * decorationRadius;
                Vector3 position = new Vector3(randomPos.x, 0, randomPos.y);
                
                Instantiate(prefab, position, Random.rotation);
            }
        }
        
        private void SetupPlayer()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }
            
            if (mainCamera != null && playerSpawnPoint != null)
            {
                mainCamera.transform.position = playerSpawnPoint.position;
                mainCamera.transform.rotation = playerSpawnPoint.rotation;
            }
            else if (mainCamera != null)
            {
                // Position camera to view the glass
                mainCamera.transform.position = glassPosition + Vector3.back * 5f + Vector3.up * 2f;
                mainCamera.transform.LookAt(glassPosition);
            }
        }
        
        private void SetupAudio()
        {
            audioManager = gameObject.AddComponent<GlassAudioManager>();
            
            // You can configure audio settings here
            // audioManager.Settings.breakVolume = 1f;
            // audioManager.Settings.crackVolume = 0.7f;
        }
        
        private void SetupTester()
        {
            tester = gameObject.AddComponent<GlassBreakerTester>();
            tester.glassBreaker = glassBreaker;
            tester.enableMouseBreaking = true;
            tester.enableKeyboardBreaking = true;
            tester.testImpactForce = 10f;
            
            if (projectilePrefab != null)
            {
                tester.projectilePrefab = projectilePrefab;
                tester.projectileForce = 500f;
                tester.projectileLifetime = 5f;
            }
            else
            {
                // Create a simple projectile
                GameObject projectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                projectile.name = "Projectile";
                projectile.transform.localScale = Vector3.one * 0.2f;

                // Try to set the tag, but don't fail if it doesn't exist
                try
                {
                    projectile.tag = "Breakable";
                }
                catch (UnityException)
                {
                    Debug.LogWarning("'Breakable' tag not found. Projectile may not break glass properly.");
                }
                
                // Add rigidbody
                Rigidbody rb = projectile.AddComponent<Rigidbody>();
                rb.mass = 0.5f;
                
                // Make it a prefab-like object
                projectile.SetActive(false);
                tester.projectilePrefab = projectile;
            }
        }
        
        /// <summary>
        /// Reset the demo scene
        /// </summary>
        public void ResetDemo()
        {
            if (glassBreaker != null)
            {
                glassBreaker.RepairGlass();
            }
        }
        
        /// <summary>
        /// Trigger a demo break
        /// </summary>
        public void TriggerDemoBreak()
        {
            if (glassBreaker != null)
            {
                Vector3 randomPoint = glassBreaker.transform.position + Random.insideUnitSphere * 0.5f;
                glassBreaker.BreakGlass(randomPoint, 15f);
            }
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(Screen.width - 220, 10, 200, 150));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Voronoi Glass Demo", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
            GUILayout.Space(10);
            
            if (GUILayout.Button("Trigger Demo Break"))
            {
                TriggerDemoBreak();
            }
            
            if (GUILayout.Button("Reset Demo"))
            {
                ResetDemo();
            }
            
            GUILayout.Space(10);
            
            if (glassBreaker != null)
            {
                GUILayout.Label($"Cells: {glassBreaker.Settings.cellCount}");
                GUILayout.Label($"Progressive: {(glassBreaker.Settings.enableProgressiveBreaking ? "On" : "Off")}");
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        /// <summary>
        /// Get URP compatible shader
        /// </summary>
        private static Shader GetURPShader()
        {
            Shader urpShader = Shader.Find("Universal Render Pipeline/Lit");
            if (urpShader == null)
            {
                urpShader = Shader.Find("Lit"); // Fallback for newer URP versions
            }
            if (urpShader == null)
            {
                Debug.LogWarning("Could not find URP Lit shader. Using Unlit as fallback.");
                urpShader = Shader.Find("Universal Render Pipeline/Unlit");
            }
            if (urpShader == null)
            {
                Debug.LogError("No URP shaders found! Make sure you're using URP.");
                urpShader = Shader.Find("Unlit/Color"); // Last resort fallback
            }
            return urpShader;
        }
    }
}
