%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_TemporalCoherenceThreshold: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &134695926
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 134695927}
  m_Layer: 0
  m_Name: SkipFirst
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &134695927
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 134695926}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 9.2, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 772161705}
  - {fileID: 407900931}
  - {fileID: 1316056380}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &163173276
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1711136442}
    m_Modifications:
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.x
      value: 1.73
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.y
      value: 0.052
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
  m_IsPrefabParent: 0
--- !u!4 &341544940
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1172104127}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.1000004, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1307922443}
  - {fileID: 575145194}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &407900930
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 134695927}
    m_Modifications:
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.x
      value: 1.73
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.y
      value: 0.052
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
  m_IsPrefabParent: 0
--- !u!4 &407900931 stripped
Transform:
  m_PrefabParentObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 407900930}
--- !u!1 &407900932 stripped
GameObject:
  m_PrefabParentObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 407900930}
--- !u!114 &407900933
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 407900932}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0.03
    byOffset: 0.1
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.28
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 50
    variation: 0
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 2
      frames: 3
      fragments: 1
      skipFirstDemolition: 1
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!1 &575145193 stripped
GameObject:
  m_PrefabParentObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 1958849793}
--- !u!4 &575145194 stripped
Transform:
  m_PrefabParentObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 1958849793}
--- !u!114 &575145195
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 575145193}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0.03
    byOffset: 0.1
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 50
    variation: 0
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 5
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!1 &772161704
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 772161705}
  - component: {fileID: 772161708}
  - component: {fileID: 772161707}
  - component: {fileID: 772161706}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &772161705
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 772161704}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.8999996, y: 19.73, z: 0.029999971}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 134695927}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &772161706
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 772161704}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 0
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 140
    variation: 0
    depthFade: 0.5
    contactBias: 0.5
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!23 &772161707
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 772161704}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &772161708
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 772161704}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1172104127
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 341544940}
  m_Layer: 0
  m_Name: Disabled
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1307922442
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1307922443}
  - component: {fileID: 1307922446}
  - component: {fileID: 1307922445}
  - component: {fileID: 1307922444}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1307922443
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1307922442}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.8999996, y: 18, z: 0.029999971}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 341544940}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1307922444
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1307922442}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 0
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 140
    variation: 0
    depthFade: 0.5
    contactBias: 0.5
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!23 &1307922445
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1307922442}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1307922446
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1307922442}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1316056379
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1316056380}
  - component: {fileID: 1316056383}
  - component: {fileID: 1316056382}
  - component: {fileID: 1316056381}
  m_Layer: 0
  m_Name: Rock (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1316056380
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1316056379}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.93, y: 22.84, z: 0.029999971}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 134695927}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1316056381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1316056379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 0
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 140
    variation: 0
    depthFade: 0.5
    contactBias: 0.5
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!23 &1316056382
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1316056379}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1316056383
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1316056379}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1001 &1445489124
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.x
      value: 16.36
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 2}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.z
      value: -43.55
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.y
      value: 35.18
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0.099795856
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.w
      value: 0.995008
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 11.455001
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.z
      value: -40.18
      objectReference: {fileID: 0}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 2}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 1026965256934996, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
  m_IsPrefabParent: 0
--- !u!1 &1711136441
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1711136442}
  m_Layer: 0
  m_Name: Enabled
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1711136442
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1711136441}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.7, y: 0, z: -2.29}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1873437328}
  - {fileID: 1750787821}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1750787820 stripped
GameObject:
  m_PrefabParentObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 163173276}
--- !u!4 &1750787821 stripped
Transform:
  m_PrefabParentObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 163173276}
--- !u!114 &1750787822
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1750787820}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0.03
    byOffset: 0.1
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.28
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 50
    variation: 0
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 2
      frames: 3
      fragments: 1
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!1 &1873437327
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1873437328}
  - component: {fileID: 1873437331}
  - component: {fileID: 1873437330}
  - component: {fileID: 1873437329}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1873437328
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1873437327}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.8999996, y: 18.83, z: 0.029999971}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1711136442}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1873437329
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1873437327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  initialized: 0
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 0
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    recorder: 0
    exclude: 0
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 0}
    velocity: {x: 0, y: 0, z: 0}
    collMult: 0.8
    birthPos: {x: 0, y: 0, z: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    rigidBody: {fileID: 0}
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
  limitations:
    demolished: 0
    birthTime: 0
    bboxSize: 0
    currentDepth: 0
    solidity: 0.1
    depth: 1
    time: 1
    size: 0.1
    sliceByBlade: 0
    slicePlanes: []
    available: 1
    demolitionShould: 0
  meshDemolition:
    amount: 140
    variation: 0
    depthFade: 0.5
    contactBias: 0.5
    seed: 1
    useShatter: 0
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
    badMesh: 0
    shatterMode: 1
    totalAmount: 0
    innerSubId: 0
    mesh: {fileID: 0}
    compressPrefab: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    scrShatter: {fileID: 0}
  clusterDemolition:
    meshDemolition: 0
    connectivity: 0
    contactRadius: 15
    cluster:
      id: 0
      tm: {fileID: 0}
      rootParent: {fileID: 0}
      depth: 0
    damageRadius: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    addRigid: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    collectCollisions: 0
    maxDamage: 100
    currentDamage: 0
  fading:
    fadeType: 0
    sizeFilter: 0
    lifeTime: 10
    lifeVariation: 3
    fadeTime: 5
  subIds: []
  pivots: []
  rfMeshes: []
  fragments: []
  contactPoint: {x: 0, y: 0, z: 0}
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  bound:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshes: []
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  scrDebris: {fileID: 0}
  scrDust: {fileID: 0}
--- !u!23 &1873437330
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1873437327}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1873437331
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1873437327}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1001 &1958849793
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 341544940}
    m_Modifications:
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.x
      value: 1.73
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.y
      value: 0.052
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: c0cb72a37d202fd4a8c90d16dfd52432, type: 2}
  m_IsPrefabParent: 0
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: RuntimeCaching
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 341544940}
  - {fileID: 1711136442}
  - {fileID: 134695927}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
