using UnityEngine;
using UnityEditor;
using System.IO;
using Object = UnityEngine.Object;

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Editor utility to create glass materials for the Voronoi Glass Breaker system
    /// </summary>
    public class GlassMaterialCreator : EditorWindow
    {
        private string materialName = "VoronoiGlass";
        private Color glassColor = new Color(0.8f, 0.9f, 1f, 0.3f);
        private float metallic = 0.1f;
        private float smoothness = 0.9f;
        private float refractionIndex = 1.5f;
        private bool useTransparent = true;
        private bool useReflections = true;
        
        [MenuItem("Tools/Voronoi Glass Breaker/Create Glass Material")]
        public static void ShowWindow()
        {
            GetWindow<GlassMaterialCreator>("Glass Material Creator");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("Glass Material Creator", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            materialName = EditorGUILayout.TextField("Material Name", materialName);
            EditorGUILayout.Space();
            
            GUILayout.Label("Appearance", EditorStyles.boldLabel);
            glassColor = EditorGUILayout.ColorField("Glass Color", glassColor);
            metallic = EditorGUILayout.Slider("Metallic", metallic, 0f, 1f);
            smoothness = EditorGUILayout.Slider("Smoothness", smoothness, 0f, 1f);
            refractionIndex = EditorGUILayout.Slider("Refraction Index", refractionIndex, 1f, 2f);
            
            EditorGUILayout.Space();
            GUILayout.Label("Options", EditorStyles.boldLabel);
            useTransparent = EditorGUILayout.Toggle("Use Transparency", useTransparent);
            useReflections = EditorGUILayout.Toggle("Use Reflections", useReflections);
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Create Material"))
            {
                CreateGlassMaterial();
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("This will create a glass material optimized for the Voronoi Glass Breaker system.", MessageType.Info);
        }
        
        private void CreateGlassMaterial()
        {
            // Create the material
            Material glassMaterial = new Material(Shader.Find("Standard"));
            glassMaterial.name = materialName;
            
            // Set basic properties
            glassMaterial.color = glassColor;
            glassMaterial.SetFloat("_Metallic", metallic);
            glassMaterial.SetFloat("_Glossiness", smoothness);
            
            if (useTransparent)
            {
                // Set up transparency
                glassMaterial.SetFloat("_Mode", 3); // Transparent mode
                glassMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                glassMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                glassMaterial.SetInt("_ZWrite", 0);
                glassMaterial.DisableKeyword("_ALPHATEST_ON");
                glassMaterial.EnableKeyword("_ALPHABLEND_ON");
                glassMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                glassMaterial.renderQueue = 3000;
            }
            
            if (useReflections)
            {
                // Enable reflections
                glassMaterial.EnableKeyword("_METALLICGLOSSMAP");
                glassMaterial.EnableKeyword("_NORMALMAP");
            }
            
            // Create directory if it doesn't exist
            string folderPath = "Assets/VoronoiGlassBreaker/Materials";
            if (!AssetDatabase.IsValidFolder(folderPath))
            {
                Directory.CreateDirectory(folderPath);
                AssetDatabase.Refresh();
            }
            
            // Save the material
            string materialPath = $"{folderPath}/{materialName}.mat";
            AssetDatabase.CreateAsset(glassMaterial, materialPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            // Select the created material
            Selection.activeObject = glassMaterial;
            EditorGUIUtility.PingObject(glassMaterial);
            
            Debug.Log($"Glass material '{materialName}' created at {materialPath}");
        }
    }
    
    /// <summary>
    /// Menu items for quick setup
    /// </summary>
    public class VoronoiGlassBreakerMenuItems
    {
        [MenuItem("GameObject/3D Object/Voronoi Glass", false, 10)]
        public static void CreateVoronoiGlass()
        {
            // Ensure "Breakable" tag exists
            EnsureBreakableTagExists();

            // Create a plane for the glass
            GameObject glassObj = GameObject.CreatePrimitive(PrimitiveType.Plane);
            glassObj.name = "VoronoiGlass";
            glassObj.transform.rotation = Quaternion.Euler(90, 0, 0); // Make it vertical
            
            // Add the Voronoi Glass Breaker component
            VoronoiGlassBreaker glassBreaker = glassObj.AddComponent<VoronoiGlassBreaker>();
            
            // Configure default settings
            var settings = glassBreaker.Settings;
            settings.cellCount = 30;
            settings.relaxationIterations = 2;
            settings.breakForce = 10f;
            settings.breakRadius = 1f;
            settings.enableProgressiveBreaking = true;
            settings.propagationSpeed = 3f;
            settings.propagationRadius = 0.4f;
            settings.fragmentLifetime = 8f;
            
            // Set up collider - replace MeshCollider with BoxCollider for trigger support
            Collider collider = glassObj.GetComponent<Collider>();
            if (collider != null)
            {
                // Check if it's a problematic MeshCollider
                MeshCollider meshCol = collider as MeshCollider;
                if (meshCol != null && !meshCol.convex)
                {
                    // Remove the default MeshCollider and add BoxCollider
                    Object.DestroyImmediate(collider);
                    BoxCollider boxCol = glassObj.AddComponent<BoxCollider>();
                    boxCol.center = Vector3.zero;
                    boxCol.size = new Vector3(10f, 0.1f, 10f); // Plane-like dimensions
                    boxCol.isTrigger = true;
                }
                else
                {
                    collider.isTrigger = true;
                }
            }
            
            // Create a basic glass material
            Material glassMaterial = new Material(Shader.Find("Standard"));
            glassMaterial.name = "DefaultVoronoiGlass";
            glassMaterial.SetFloat("_Mode", 3); // Transparent mode
            glassMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            glassMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            glassMaterial.SetInt("_ZWrite", 0);
            glassMaterial.DisableKeyword("_ALPHATEST_ON");
            glassMaterial.EnableKeyword("_ALPHABLEND_ON");
            glassMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            glassMaterial.renderQueue = 3000;
            glassMaterial.color = new Color(0.8f, 0.9f, 1f, 0.3f);
            glassMaterial.SetFloat("_Metallic", 0.1f);
            glassMaterial.SetFloat("_Glossiness", 0.9f);
            
            glassObj.GetComponent<MeshRenderer>().material = glassMaterial;
            
            // Position in scene view
            if (SceneView.lastActiveSceneView != null)
            {
                glassObj.transform.position = SceneView.lastActiveSceneView.pivot;
            }
            
            // Select the created object
            Selection.activeGameObject = glassObj;
            
            Debug.Log("Voronoi Glass object created. Configure the settings in the inspector and add breakable objects to test.");
        }
        
        [MenuItem("Tools/Voronoi Glass Breaker/Create Breakable Tag")]
        public static void CreateBreakableTag()
        {
            EnsureBreakableTagExists();
        }

        [MenuItem("Tools/Voronoi Glass Breaker/Setup Demo Scene")]
        public static void SetupDemoScene()
        {
            // Ensure "Breakable" tag exists
            EnsureBreakableTagExists();

            // Create demo manager
            GameObject demoObj = new GameObject("VoronoiGlassDemo");
            GlassBreakerDemo demo = demoObj.AddComponent<GlassBreakerDemo>();

            // Create a simple projectile prefab
            GameObject projectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            projectile.name = "DemoProjectile";
            projectile.transform.localScale = Vector3.one * 0.2f;
            projectile.tag = "Breakable";
            projectile.AddComponent<Rigidbody>().mass = 0.5f;
            projectile.SetActive(false);
            
            demo.projectilePrefab = projectile;
            
            // Position camera if available
            Camera mainCamera = Camera.main;
            // if (mainCamera == null)
            // {
            //     mainCamera = FindObjectOfType<Camera>();
            // }
            
            if (mainCamera != null)
            {
                mainCamera.transform.position = new Vector3(0, 2, -5);
                mainCamera.transform.LookAt(Vector3.zero);
            }
            
            Selection.activeGameObject = demoObj;
            
            Debug.Log("Demo scene setup complete. Press Play to test the glass breaking system.");
        }

        /// <summary>
        /// Ensure the "Breakable" tag exists in the project
        /// </summary>
        private static void EnsureBreakableTagExists()
        {
            // Get the TagManager asset
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty tagsProp = tagManager.FindProperty("tags");

            // Check if "Breakable" tag already exists
            bool tagExists = false;
            for (int i = 0; i < tagsProp.arraySize; i++)
            {
                SerializedProperty tag = tagsProp.GetArrayElementAtIndex(i);
                if (tag.stringValue == "Breakable")
                {
                    tagExists = true;
                    break;
                }
            }

            // Add the tag if it doesn't exist
            if (!tagExists)
            {
                tagsProp.InsertArrayElementAtIndex(tagsProp.arraySize);
                SerializedProperty newTag = tagsProp.GetArrayElementAtIndex(tagsProp.arraySize - 1);
                newTag.stringValue = "Breakable";
                tagManager.ApplyModifiedProperties();

                Debug.Log("Added 'Breakable' tag to project.");
            }
        }
    }
}
