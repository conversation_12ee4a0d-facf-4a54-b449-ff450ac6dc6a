{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to destroy moving structure.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par
\lang1033 Sometimes you may need to destroy prefragmented structure which parent is moving, one of the possible issues is that after demolition fallen fragments may continue to move because of the moving parent. In this How To you can learn how to deal with this issue and in which cases it may occur.\lang9\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create Cube, this will be a ground cube which will be used for collisions.\line\par
{\pntext\f0 2.\tab}Set its name to "Ground", position to [0,0,0] and scale to [20,1,20]\line\par
{\pntext\f0 3.\tab}Create another Cube, this will be our moving platform.\line\par
{\pntext\f0 4.\tab}Set its name to "Platform", position to [-7,6,0] and scale to [7,1,3]\line\par
{\pntext\f0 5.\tab}\lang1033 Add Rigidbody component to Platform, enable Kinematic checkbox and set Collision detection to Continuous Dynamic. We need this so fragments which will fall to platform will collide with it and will be moved by friction.\lang9\line\par
{\pntext\f0 6.\tab}Add TranslateScript component to Platform, you can find it in \line\i Assets\\RayFire\\Tutorial\\Scripts \i0 folder. \line\line This script provides two ways to move object. First one uses Rigidbody.MovePosition() method in FixedUpdate() to move Platform. Second uses Transform.Translate() but in this case, dynamic objects on the platform will not be moved by platform because, from Physics point of view, it will be teleportation between two frames and not physical movement from one point in space to another.\line\line In the first example we will move Platform using Rigidbody.MovePosition() method. \line\par
{\pntext\f0 7.\tab}Create another Cube, set its name to "Wall", position to [-7,10,0] and scale to [5,7,0.2]\line\par
{\pntext\f0 8.\tab}Add RayFire Shatter component to Wall, set Amount to 100 and click on Fragment button. Wall_root object with all fragments will be created.\line\par
{\pntext\f0 9.\tab}Destroy Wall object, we do not need it anymore.\line\par
{\pntext\f0 10.\tab}Set Wall_root as child for Platform.\line\par
{\pntext\f0 11.\tab}Add Rigid component to Wall_root, set Initialization to At Start, Object Type to Mesh Root and Simulation Type to Kinematic.\line\line By this moment we created prefragmented wall which will be moved with our Platform. Now lets break it while it will be moving.\line\par
{\pntext\f0 12.\tab}Create Cube, set its name "Block", position to [0,10,0] and scale to [2,2,2]. This will be the object which will collide with our moving Wall and break it.\line\par
{\pntext\f0 13.\tab}Start Play Mode. \line\line As you see, Wall fragments do not collide with Block for now. This is because Wall fragments and Block are kinematic and kinematic objects do not collide with each other. To make them collide and fall we need to Activate fragments to make them dynamic a moment before they collide with Block.\line\par
{\pntext\f0 14.\tab}Turn Off Play Mode. \line\par
{\pntext\f0 15.\tab}In Wall_root Rigid component Activation properties enable Activator property.\line\par
{\pntext\f0 16.\tab}Select Block, add RayFire Activator component, set Type to Box, Size to [1.2, 1.2, 1]\line\par
{\pntext\f0 17.\tab}Start Play Mode. \line\line Now Wall fragments activate a couple of frames before collide with Block and fall down to Platform. Some fragments fall to the Ground and stay there. Now lets fix frozen upper fragments which are not connected to bottom fragments anyhow but still continue to move with Platform.\line\par
{\pntext\f0 18.\tab}Turn Off Play Mode. \line\par
{\pntext\f0 19.\tab}Select Wall_root object and in Rigid component Activation properties enable Connectivity checkbox.\fs24\lang1033\line\par
{\pntext\f0 20.\tab}\fs22\lang9 Add RayFire Connectivity component and set Type to Polygons, this type works good for fragments created by Voronoi fragmentation type.\fs24\lang1033\line\par
{\pntext\f0 21.\tab}Add RayFire Unyielding component, set its Size to [5,1,1] and Center to [0,-3,0] so it will overlap bottom Wall fragments by its gizmo.\line\par
{\pntext\f0 22.\tab}\fs22\lang9 Start Play Mode. \line\line Now you can see that upper group of fragments turns to Connected Cluster when it loses all connections with bottom group of fragments. \line\par
{\pntext\f0 23.\tab}Turn Off Play Mode. \line\line Now lets try different type of moving. This time we will use Transform.Translate() to see the difference.\line\par
{\pntext\f0 24.\tab}Select Platform and in Translate component change Move Type to Translate.\line\par
{\pntext\f0 25.\tab}Start Play Mode. \line\line Now you may notice that fragments which fall to the Ground continue moving with Platform. This happens because they are still children of Wall_root object, which is child of platform. To fix this you need to reparent them after activation to some other steady object.\line\par
{\pntext\f0 26.\tab}Turn Off Play Mode. \line\par
{\pntext\f0 27.\tab}Create empty game object, set its name "RayFire Man", and add RayFire Man component to it. Every time you start play mode RayFire creates object with RayFire Man automatically with default settings, but if you create it manually, you can edit default settings and this RayFire Man component will be used.\line\par
{\pntext\f0 28.\tab}Create another empty game object and name it "Steady_root"\line\par
{\pntext\f0 29.\tab}Select RayFire Man and define Steady_root object in Activation Parent field. This will force all activated fragments to change their parent to Steady_root object.\line\par
{\pntext\f0 30.\tab}Start Play Mode. \par

\pard\nowidctlpar\sl276\slmult1\line\line This time fragments fall down to the Ground and stay there but also you may notice that fragments that fall to the Platform just slide over it and then fall to the Ground. This happens because for Physics Transform.Translate() simply teleports platform between frames.\line\par
\tab\line\fs24\lang1033\par
}
 