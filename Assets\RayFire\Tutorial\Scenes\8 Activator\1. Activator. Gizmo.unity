%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &1990850
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1990851}
  - component: {fileID: 1990853}
  - component: {fileID: 1990852}
  m_Layer: 0
  m_Name: Activator_Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1990851
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990850}
  m_LocalRotation: {x: 0.5, y: 0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: -7.59, y: 2.86, z: -0.07}
  m_LocalScale: {x: 0.52214, y: 0.52214, z: 0.52214}
  m_Children: []
  m_Father: {fileID: 830370377}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: -90}
--- !u!64 &1990852
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990850}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 3
  m_Convex: 1
  m_CookingOptions: 14
  m_Mesh: {fileID: 4300000, guid: 875377aa2c263eb4b911c59f5f8b6737, type: 3}
--- !u!114 &1990853
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990850}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13a2260124482ed4aac2c705989354dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gizmoType: 2
  sphereRadius: 2.2341938
  boxSize: {x: 5, y: 2, z: 5}
  type: 0
  delay: 0
  demolishCluster: 0
  duration: 3
  scaleAnimation: 1
  positionAnimation: 0
  line: {fileID: 0}
  positionList: []
  showGizmo: 1
--- !u!1 &9658884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9658885}
  - component: {fileID: 9658886}
  m_Layer: 0
  m_Name: Activator_Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9658885
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9658884}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.66, y: 2.07, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1273418818}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9658886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9658884}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13a2260124482ed4aac2c705989354dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gizmoType: 1
  sphereRadius: 2.2341938
  boxSize: {x: 5, y: 2.6175327, z: 5}
  type: 0
  delay: 0
  demolishCluster: 0
  duration: 3
  scaleAnimation: 1
  positionAnimation: 0
  line: {fileID: 0}
  positionList: []
  showGizmo: 1
--- !u!1001 &31209002
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 653504060}
    m_Modifications:
    - target: {fileID: 100610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_Name
      value: slabs
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.17
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
--- !u!1 &167601518 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100610, guid: 8a312a861d188f4459a64acbef7e2716,
    type: 3}
  m_PrefabInstance: {fileID: 379843740}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &167601519 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716,
    type: 3}
  m_PrefabInstance: {fileID: 379843740}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &167601520
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 167601518}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 2
  objectType: 1
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 1
    colliderType: 0
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 1
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 0.2
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 0
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: -1
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 7
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &335503643
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 335503644}
  m_Layer: 0
  m_Name: Gizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &335503644
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 335503643}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -3.2530298, y: 1.4905682, z: -4.7083645}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 385906872}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &379843740
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 830370377}
    m_Modifications:
    - target: {fileID: 100610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_Name
      value: slabs
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.17
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
--- !u!1 &385906871
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 385906872}
  m_Layer: 0
  m_Name: Type
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &385906872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 385906871}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 653504060}
  - {fileID: 1273418818}
  - {fileID: 830370377}
  m_Father: {fileID: 335503644}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &653504059
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 653504060}
  m_Layer: 0
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &653504060
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 653504059}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3.2530298, y: -1.4905682, z: 13.598364}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2128266611}
  - {fileID: 1066609845}
  m_Father: {fileID: 385906872}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &830370376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 830370377}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &830370377
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 830370376}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3.2530298, y: -1.4905682, z: -2.49}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1990851}
  - {fileID: 167601519}
  m_Father: {fileID: 385906872}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1033695049 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100610, guid: 8a312a861d188f4459a64acbef7e2716,
    type: 3}
  m_PrefabInstance: {fileID: 1525351970}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1033695050 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716,
    type: 3}
  m_PrefabInstance: {fileID: 1525351970}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1033695051
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033695049}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 2
  objectType: 1
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 1
    colliderType: 0
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 1
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 0.2
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 0
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: -1
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 7
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &1066609844 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100610, guid: 8a312a861d188f4459a64acbef7e2716,
    type: 3}
  m_PrefabInstance: {fileID: 31209002}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1066609845 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716,
    type: 3}
  m_PrefabInstance: {fileID: 31209002}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1066609846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066609844}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 2
  objectType: 1
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 1
    colliderType: 0
    useGravity: 1
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 1
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
  limitations:
    solidity: 0.1
    depth: 1
    time: 0.2
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0
    seed: 1
    useShatter: 0
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 0
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: -1
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    lifeType: 4
    lifeTime: 7
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
  reset:
    transform: 1
    damage: 1
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
    shards: 0
  initialized: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &1273418817
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1273418818}
  m_Layer: 0
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1273418818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1273418817}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3.2530298, y: -1.4905682, z: 5.138364}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 9658885}
  - {fileID: 1033695050}
  m_Father: {fileID: 385906872}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1445489124
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1014529264720340, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1260722613888276, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1359285263087640, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1736276488727964, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 15.2336445
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -43.55
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 32.32
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.055917617
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99843544
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 6.4110003
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -40.18
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!1001 &1525351970
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1273418818}
    m_Modifications:
    - target: {fileID: 100610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_Name
      value: slabs
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.17
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400610, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 8a312a861d188f4459a64acbef7e2716, type: 3}
--- !u!1 &2128266610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2128266611}
  - component: {fileID: 2128266612}
  m_Layer: 0
  m_Name: Activator_Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2128266611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2128266610}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.66, y: 2.07, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 653504060}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2128266612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2128266610}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13a2260124482ed4aac2c705989354dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gizmoType: 0
  sphereRadius: 2.2341938
  boxSize: {x: 5, y: 2, z: 5}
  type: 0
  delay: 0
  demolishCluster: 0
  duration: 3
  scaleAnimation: 1
  positionAnimation: 0
  line: {fileID: 0}
  positionList: []
  showGizmo: 1
