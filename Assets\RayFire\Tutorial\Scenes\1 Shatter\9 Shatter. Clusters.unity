%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_TemporalCoherenceThreshold: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &22247923
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 22247924}
  - component: {fileID: 22247927}
  - component: {fileID: 22247926}
  - component: {fileID: 22247925}
  m_Layer: 0
  m_Name: Slabs_Count:15
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &22247924
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22247923}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 128.4, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 21
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &22247925
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22247923}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 2
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 15
    seed: 39
    relax: 0.5
    amount: 5
    layers: 0
    scale: 1
    min: 1
    max: 2
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 22247924}
  meshFilter: {fileID: 22247927}
  meshRenderer: {fileID: 22247926}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.13641733
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 32.799995, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &22247926
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22247923}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &22247927
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22247923}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &153658827
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 153658828}
  - component: {fileID: 153658831}
  - component: {fileID: 153658830}
  - component: {fileID: 153658829}
  m_Layer: 0
  m_Name: Voronoi_Count:3_Amount:50_Layer:0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &153658828
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 153658827}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 39, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &153658829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 153658827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0.5
    amount: 50
    layers: 0
    scale: 1
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 153658828}
  meshFilter: {fileID: 153658831}
  meshRenderer: {fileID: 153658830}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.09428571
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -56.6, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &153658830
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 153658827}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &153658831
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 153658827}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &226154377
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 226154378}
  - component: {fileID: 226154381}
  - component: {fileID: 226154380}
  - component: {fileID: 226154379}
  m_Layer: 0
  m_Name: Slabs_Count:5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &226154378
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 226154377}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 134.7, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 22
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &226154379
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 226154377}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 2
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 5
    seed: 39
    relax: 0.5
    amount: 20
    layers: 0
    scale: 1
    min: 1
    max: 2
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470458, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 226154378}
  meshFilter: {fileID: 226154381}
  meshRenderer: {fileID: 226154380}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.2455512
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 39.1, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &226154380
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 226154377}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &226154381
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 226154377}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &284587995
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 284587996}
  m_Layer: 0
  m_Name: '--------'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &284587996
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 284587995}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 30
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &304783911
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 304783912}
  - component: {fileID: 304783915}
  - component: {fileID: 304783914}
  - component: {fileID: 304783913}
  m_Layer: 0
  m_Name: Voronoi_Layers:1 Scale:0.4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &304783912
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 304783911}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 65.4, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &304783913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 304783911}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0.5
    amount: 0
    layers: 1
    scale: 0.4
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 304783912}
  meshFilter: {fileID: 304783915}
  meshRenderer: {fileID: 304783914}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.004419724
  size: 7.5498343
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 4}
  bound:
    m_Center: {x: -30.199997, y: 2, z: 1.75}
    m_Extent: {x: 2.5, y: 2, z: 2}
  resetState: 0
--- !u!23 &304783914
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 304783911}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &304783915
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 304783911}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &367357838
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 367357839}
  - component: {fileID: 367357842}
  - component: {fileID: 367357841}
  - component: {fileID: 367357840}
  m_Layer: 0
  m_Name: Voronoi_Amount:30_Count:3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &367357839
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 367357838}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.300001, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &367357840
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 367357838}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470575, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 367357839}
  meshFilter: {fileID: 367357842}
  meshRenderer: {fileID: 367357841}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.11051162
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -89.299995, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &367357841
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 367357838}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &367357842
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 367357838}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &397632207
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 397632208}
  - component: {fileID: 397632211}
  - component: {fileID: 397632210}
  - component: {fileID: 397632209}
  m_Layer: 0
  m_Name: Radial_Count:24
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &397632208
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 397632207}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 154.5, y: 2.54, z: 0}
  m_LocalScale: {x: 5, y: 5, z: 0.13539}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 25
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &397632209
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 397632207}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 3
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0.141
    restrictToPlane: 1
    rings: 18
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 28
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 24
    seed: 0
    relax: 0.5
    amount: 57
    layers: 0
    scale: 1
    min: 1
    max: 2
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.0205719, y: 0.019961834, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 397632208}
  meshFilter: {fileID: 397632211}
  meshRenderer: {fileID: 397632210}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.30011812
  size: 7.072364
  rescaleFix: 1
  originalScale: {x: 5, y: 5, z: 0.13539}
  bound:
    m_Center: {x: 58.9, y: 2.54, z: 0}
    m_Extent: {x: 2.5, y: 2.5, z: 0.067695}
  resetState: 0
--- !u!23 &397632210
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 397632207}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &397632211
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 397632207}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &399585496
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 399585497}
  - component: {fileID: 399585500}
  - component: {fileID: 399585499}
  - component: {fileID: 399585498}
  m_Layer: 0
  m_Name: Voronoi_Amount:30_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &399585497
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &399585498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 3
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785383, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 399585497}
  meshFilter: {fileID: 399585500}
  meshRenderer: {fileID: 399585499}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.10590697
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -95.6, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &399585499
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &399585500
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &511161381
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 511161382}
  - component: {fileID: 511161385}
  - component: {fileID: 511161384}
  - component: {fileID: 511161383}
  m_Layer: 0
  m_Name: Slabs_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &511161382
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 511161381}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 116.4, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 19
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &511161383
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 511161381}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 2
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 3
    seed: 39
    relax: 0.5
    amount: 0
    layers: 1
    scale: 1
    min: 2
    max: 5
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 511161382}
  meshFilter: {fileID: 511161385}
  meshRenderer: {fileID: 511161384}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.26114175
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 20.800003, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &511161384
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 511161381}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &511161385
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 511161381}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &540435995
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 540435996}
  - component: {fileID: 540435999}
  - component: {fileID: 540435998}
  - component: {fileID: 540435997}
  m_Layer: 0
  m_Name: Column_Count:20
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &540435996
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540435995}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 193.39, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 32
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &540435997
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540435995}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 300
    centerBias: 1
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 20
    seed: 1
    relax: 0.5
    amount: 10
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: 0.09881592, y: 4.0477533, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 540435996}
  meshFilter: {fileID: 540435999}
  meshRenderer: {fileID: 540435998}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.26893702
  size: 7.9992795
  rescaleFix: 1
  originalScale: {x: 1, y: 1, z: 1}
  bound:
    m_Center: {x: 97.79, y: 3.7068398, z: -0.0000011622906}
    m_Extent: {x: 0.99083453, y: 3.7461464, z: 0.99083424}
  resetState: 0
--- !u!23 &540435998
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540435995}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &540435999
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540435995}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &699142604
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 699142605}
  m_Layer: 0
  m_Name: '--------'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &699142605
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 699142604}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 23
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &919343795
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 919343796}
  - component: {fileID: 919343799}
  - component: {fileID: 919343798}
  - component: {fileID: 919343797}
  m_Layer: 0
  m_Name: Tets_Count:15
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &919343796
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919343795}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 179.64, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 29
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &919343797
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919343795}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 11
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 10
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 15
    seed: 0
    relax: 0.6
    amount: 0
    layers: 0
    scale: 1
    min: 7
    max: 8
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470458, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 919343796}
  meshFilter: {fileID: 919343799}
  meshRenderer: {fileID: 919343798}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.14877269
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 84.04, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &919343798
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919343795}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &919343799
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919343795}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1102887361
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1102887362}
  m_Layer: 0
  m_Name: '--------'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1102887362
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1102887361}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 26
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1173028605
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1173028606}
  m_Layer: 0
  m_Name: '--------'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1173028606
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1173028605}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1289618861
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1289618862}
  - component: {fileID: 1289618865}
  - component: {fileID: 1289618864}
  - component: {fileID: 1289618863}
  m_Layer: 0
  m_Name: Splinters_Count:35
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1289618862
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1289618861}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 91.5, y: 3.61, z: 0}
  m_LocalScale: {x: 5, y: 7.129715, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1289618863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1289618861}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.85
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 35
    seed: 1
    relax: 0.5
    amount: 10
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.4847122, y: 0.48785776, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1289618862}
  meshFilter: {fileID: 1289618865}
  meshRenderer: {fileID: 1289618864}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.21826771
  size: 8.722548
  rescaleFix: 1
  originalScale: {x: 5, y: 7.129715, z: 0.5}
  bound:
    m_Center: {x: -4.0999985, y: 3.61, z: 0}
    m_Extent: {x: 2.5, y: 3.5648575, z: 0.25}
  resetState: 0
--- !u!23 &1289618864
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1289618861}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1289618865
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1289618861}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1314131388
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1314131389}
  - component: {fileID: 1314131392}
  - component: {fileID: 1314131391}
  - component: {fileID: 1314131390}
  m_Layer: 0
  m_Name: Voronoi_Amount:0_Layers:1 Min:1_Max:1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1314131389
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1314131388}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 45.6, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1314131390
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1314131388}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0.5
    amount: 0
    layers: 1
    scale: 1
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1314131389}
  meshFilter: {fileID: 1314131392}
  meshRenderer: {fileID: 1314131391}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.25786045
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -50, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &1314131391
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1314131388}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1314131392
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1314131388}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1336813172
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1336813173}
  - component: {fileID: 1336813176}
  - component: {fileID: 1336813175}
  - component: {fileID: 1336813174}
  m_Layer: 0
  m_Name: Voronoi_Layers:1 Scale:1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1336813173
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1336813172}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 58.8, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1336813174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1336813172}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0.5
    amount: 0
    layers: 1
    scale: 1
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1336813173}
  meshFilter: {fileID: 1336813176}
  meshRenderer: {fileID: 1336813175}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.0012574962
  size: 7.5498343
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 4}
  bound:
    m_Center: {x: -36.8, y: 2, z: 1.75}
    m_Extent: {x: 2.5, y: 2, z: 2}
  resetState: 0
--- !u!23 &1336813175
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1336813172}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1336813176
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1336813172}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1347800030
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1347800031}
  - component: {fileID: 1347800034}
  - component: {fileID: 1347800033}
  - component: {fileID: 1347800032}
  m_Layer: 0
  m_Name: Voronoi_Relax:1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1347800031
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1347800030}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 78.9, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1347800032
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1347800030}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 1
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1347800031}
  meshFilter: {fileID: 1347800034}
  meshRenderer: {fileID: 1347800033}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.027283467
  size: 7.5498343
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 4}
  bound:
    m_Center: {x: -16.699997, y: 2, z: 1.75}
    m_Extent: {x: 2.5, y: 2, z: 2}
  resetState: 0
--- !u!23 &1347800033
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1347800030}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1347800034
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1347800030}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1374640622
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1374640623}
  - component: {fileID: 1374640626}
  - component: {fileID: 1374640625}
  - component: {fileID: 1374640624}
  m_Layer: 0
  m_Name: Voronoi_Amount:400_Count:10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1374640623
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1374640622}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 25.800001, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1374640624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1374640622}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470575, y: 0.48785383, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1374640623}
  meshFilter: {fileID: 1374640626}
  meshRenderer: {fileID: 1374640625}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.10438777
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -69.799995, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &1374640625
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1374640622}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1374640626
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1374640622}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1461517691
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1461517692}
  - component: {fileID: 1461517695}
  - component: {fileID: 1461517694}
  - component: {fileID: 1461517693}
  m_Layer: 0
  m_Name: Slabs_Count:35
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1461517692
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1461517691}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 122.4, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 20
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1461517693
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1461517691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 2
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 35
    seed: 39
    relax: 0.5
    amount: 10
    layers: 0
    scale: 1
    min: 1
    max: 2
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1461517692}
  meshFilter: {fileID: 1461517695}
  meshRenderer: {fileID: 1461517694}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.2455512
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 26.800003, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &1461517694
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1461517691}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1461517695
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1461517691}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1464246284
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1464246285}
  - component: {fileID: 1464246288}
  - component: {fileID: 1464246287}
  - component: {fileID: 1464246286}
  m_Layer: 0
  m_Name: Voronoi_Amount:400_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1464246285
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1464246284}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 19.2, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1464246286
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1464246284}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470688, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1464246285}
  meshFilter: {fileID: 1464246288}
  meshRenderer: {fileID: 1464246287}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.1450465
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -76.399994, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &1464246287
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1464246284}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1464246288
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1464246284}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1479334424
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1479334425}
  - component: {fileID: 1479334428}
  - component: {fileID: 1479334427}
  - component: {fileID: 1479334426}
  m_Layer: 0
  m_Name: Voronoi_Amount:30_Count:8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1479334425
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1479334424}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 12.6, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1479334426
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1479334424}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 8
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.4847065, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1479334425}
  meshFilter: {fileID: 1479334428}
  meshRenderer: {fileID: 1479334427}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.16499999
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -83, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &1479334427
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1479334424}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1479334428
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1479334424}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1582368733
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1582368734}
  - component: {fileID: 1582368737}
  - component: {fileID: 1582368736}
  - component: {fileID: 1582368735}
  m_Layer: 0
  m_Name: Splinters_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1582368734
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1582368733}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 84.9, y: 3.61, z: 0}
  m_LocalScale: {x: 5, y: 7.129715, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1582368735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1582368733}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.85
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 51
    seed: 1
    relax: 0.5
    amount: 30
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.4847122, y: 0.48785776, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1582368734}
  meshFilter: {fileID: 1582368737}
  meshRenderer: {fileID: 1582368736}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.23944186
  size: 8.722548
  rescaleFix: 1
  originalScale: {x: 5, y: 7.129715, z: 0.5}
  bound:
    m_Center: {x: -10.699997, y: 3.61, z: 0}
    m_Extent: {x: 2.5, y: 3.5648575, z: 0.25}
  resetState: 0
--- !u!23 &1582368736
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1582368733}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1582368737
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1582368733}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1609341778
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1609341779}
  - component: {fileID: 1609341782}
  - component: {fileID: 1609341781}
  - component: {fileID: 1609341780}
  m_Layer: 0
  m_Name: Splinters_Count:5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1609341779
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1609341778}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 104.7, y: 3.61, z: 0}
  m_LocalScale: {x: 5, y: 7.1297197, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1609341780
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1609341778}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.85
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 5
    seed: 14
    relax: 0.5
    amount: 5
    layers: 0
    scale: 1
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.4847122, y: 0.48785254, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1609341779}
  meshFilter: {fileID: 1609341782}
  meshRenderer: {fileID: 1609341781}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.22996065
  size: 8.722551
  rescaleFix: 1
  originalScale: {x: 5, y: 7.1297197, z: 0.5}
  bound:
    m_Center: {x: 9.099998, y: 3.61, z: 0}
    m_Extent: {x: 2.5, y: 3.5648599, z: 0.25}
  resetState: 0
--- !u!23 &1609341781
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1609341778}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1609341782
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1609341778}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1617574847
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1617574848}
  - component: {fileID: 1617574851}
  - component: {fileID: 1617574850}
  - component: {fileID: 1617574849}
  m_Layer: 0
  m_Name: Voronoi_Count:3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1617574848
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1617574847}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 32.4, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1617574849
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1617574847}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785383, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1617574848}
  meshFilter: {fileID: 1617574851}
  meshRenderer: {fileID: 1617574850}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.13615441
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -63.199997, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &1617574850
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1617574847}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1617574851
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1617574847}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1648664993
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1648664994}
  m_Layer: 0
  m_Name: '--------'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1648664994
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1648664993}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 33
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1649885951
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1649885952}
  - component: {fileID: 1649885955}
  - component: {fileID: 1649885954}
  - component: {fileID: 1649885953}
  m_Layer: 0
  m_Name: Tets_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1649885952
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1649885951}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 167.64, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 27
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1649885953
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1649885951}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 11
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 10
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 3
    seed: 0
    relax: 1
    amount: 0
    layers: 0
    scale: 1
    min: 2
    max: 5
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470458, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1649885952}
  meshFilter: {fileID: 1649885955}
  meshRenderer: {fileID: 1649885954}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.20657481
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 72.04, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &1649885954
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1649885951}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1649885955
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1649885951}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1700536654
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1700536655}
  m_Layer: 0
  m_Name: '--------'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1700536655
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1700536654}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1762396893
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.x
      value: 15.2336445
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
  m_IsPrefabParent: 0
--- !u!1 &1813663508
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1813663509}
  - component: {fileID: 1813663512}
  - component: {fileID: 1813663511}
  - component: {fileID: 1813663510}
  m_Layer: 0
  m_Name: Voronoi_Relax:0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1813663509
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1813663508}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 72.3, y: 2, z: 1.75}
  m_LocalScale: {x: 5, y: 4, z: 4}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1813663510
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1813663508}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 1
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1813663509}
  meshFilter: {fileID: 1813663512}
  meshRenderer: {fileID: 1813663511}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.1073494
  size: 7.5498343
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 4}
  bound:
    m_Center: {x: -23.299995, y: 2, z: 1.75}
    m_Extent: {x: 2.5, y: 2, z: 2}
  resetState: 0
--- !u!23 &1813663511
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1813663508}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1813663512
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1813663508}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1865106161
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1865106162}
  - component: {fileID: 1865106165}
  - component: {fileID: 1865106164}
  - component: {fileID: 1865106163}
  m_Layer: 0
  m_Name: Splinters_Count:10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1865106162
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1865106161}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 98.1, y: 3.61, z: 0}
  m_LocalScale: {x: 5, y: 7.1297197, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1865106163
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1865106161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 30
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.85
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 10
    seed: 1
    relax: 0.5
    amount: 5
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.4847122, y: 0.48785722, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1865106162}
  meshFilter: {fileID: 1865106165}
  meshRenderer: {fileID: 1865106164}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.21437009
  size: 8.722551
  rescaleFix: 1
  originalScale: {x: 5, y: 7.1297197, z: 0.5}
  bound:
    m_Center: {x: 2.5, y: 3.61, z: 0}
    m_Extent: {x: 2.5, y: 3.5648599, z: 0.25}
  resetState: 0
--- !u!23 &1865106164
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1865106161}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1865106165
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1865106161}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1907680703
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1907680704}
  - component: {fileID: 1907680707}
  - component: {fileID: 1907680706}
  - component: {fileID: 1907680705}
  m_Layer: 0
  m_Name: Radial_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1907680704
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1907680703}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 148.5, y: 2.54, z: 0}
  m_LocalScale: {x: 5, y: 5, z: 0.13539475}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 24
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1907680705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1907680703}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 3
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0.141
    restrictToPlane: 1
    rings: 18
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 28
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 24
    seed: 0
    relax: 0.5
    amount: 57
    layers: 0
    scale: 1
    min: 1
    max: 2
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.0205719, y: 0.019961834, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1907680704}
  meshFilter: {fileID: 1907680707}
  meshRenderer: {fileID: 1907680706}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.18782608
  size: 7.072364
  rescaleFix: 1
  originalScale: {x: 5, y: 5, z: 0.13539475}
  bound:
    m_Center: {x: 52.9, y: 2.54, z: 0}
    m_Extent: {x: 2.5, y: 2.5, z: 0.067697376}
  resetState: 0
--- !u!23 &1907680706
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1907680703}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1907680707
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1907680703}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1908601954
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1908601955}
  - component: {fileID: 1908601958}
  - component: {fileID: 1908601957}
  - component: {fileID: 1908601956}
  m_Layer: 0
  m_Name: Tets_Count:35
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1908601955
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1908601954}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 173.64, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 2}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 28
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1908601956
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1908601954}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 11
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 200
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 10
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 35
    seed: 0
    relax: 0.6
    amount: 0
    layers: 0
    scale: 1
    min: 2
    max: 7
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470458, y: 0.487854, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1908601955}
  meshFilter: {fileID: 1908601958}
  meshRenderer: {fileID: 1908601957}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.17149608
  size: 6.708204
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 2}
  bound:
    m_Center: {x: 78.04, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 1}
  resetState: 0
--- !u!23 &1908601957
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1908601954}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1908601958
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1908601954}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1939444863
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1939444864}
  - component: {fileID: 1939444867}
  - component: {fileID: 1939444866}
  - component: {fileID: 1939444865}
  m_Layer: 0
  m_Name: Voronoi_Layers:1 Min:2_Max:5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1939444864
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1939444863}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 52.199997, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1939444865
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1939444863}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 400
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 1
    count: 3
    seed: 39
    relax: 0.5
    amount: 0
    layers: 1
    scale: 1
    min: 2
    max: 5
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785388, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1939444864}
  meshFilter: {fileID: 1939444867}
  meshRenderer: {fileID: 1939444866}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.22662652
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: -43.4, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
--- !u!23 &1939444866
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1939444863}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1939444867
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1939444863}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &2058777725
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 190.09001, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 31
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2058777726
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2058777725}
  - component: {fileID: 2058777729}
  - component: {fileID: 2058777728}
  - component: {fileID: 2058777727}
  m_Layer: 0
  m_Name: Column_Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2058777727
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  voronoi:
    amount: 300
    centerBias: 1
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: 0.09881592, y: 4.0477533, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 2058777725}
  meshFilter: {fileID: 2058777729}
  meshRenderer: {fileID: 2058777728}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.3196063
  size: 7.9992795
  rescaleFix: 1
  originalScale: {x: 1, y: 1, z: 1}
  bound:
    m_Center: {x: 94.49001, y: 3.7068398, z: -0.0000011622906}
    m_Extent: {x: 0.99083453, y: 3.7461464, z: 0.99083424}
  resetState: 0
--- !u!23 &2058777728
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2058777729
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Clusters
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -95.6, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 399585497}
  - {fileID: 367357839}
  - {fileID: 1479334425}
  - {fileID: 1464246285}
  - {fileID: 1374640623}
  - {fileID: 1617574848}
  - {fileID: 153658828}
  - {fileID: 1314131389}
  - {fileID: 1939444864}
  - {fileID: 1336813173}
  - {fileID: 304783912}
  - {fileID: 1813663509}
  - {fileID: 1347800031}
  - {fileID: 1700536655}
  - {fileID: 1582368734}
  - {fileID: 1289618862}
  - {fileID: 1865106162}
  - {fileID: 1609341779}
  - {fileID: 1173028606}
  - {fileID: 511161382}
  - {fileID: 1461517692}
  - {fileID: 22247924}
  - {fileID: 226154378}
  - {fileID: 699142605}
  - {fileID: 1907680704}
  - {fileID: 397632208}
  - {fileID: 1102887362}
  - {fileID: 1649885952}
  - {fileID: 1908601955}
  - {fileID: 919343796}
  - {fileID: 284587996}
  - {fileID: 2058777725}
  - {fileID: 540435996}
  - {fileID: 1648664994}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
