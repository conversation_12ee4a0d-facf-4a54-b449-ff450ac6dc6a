%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &21834412
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 21834413}
  m_Layer: 0
  m_Name: Trigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &21834413
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 21834412}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 700314537}
  - {fileID: 567885223}
  - {fileID: 1514757838}
  - {fileID: 2113316298}
  - {fileID: 1234714260}
  - {fileID: 1873976114}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &425615854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 425615855}
  m_Layer: 0
  m_Name: Target
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &425615855
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 425615854}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 14.6, y: 27.27, z: 8.85}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1525006851}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &567885222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 567885223}
  - component: {fileID: 567885226}
  - component: {fileID: 567885225}
  - component: {fileID: 567885224}
  - component: {fileID: 567885227}
  m_Layer: 0
  m_Name: Inside_Delay_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &567885223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567885222}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -22.31, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 21834413}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &567885224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567885222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &567885225
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567885222}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &567885226
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567885222}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &567885227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 567885222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 700314538}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &700314536
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 700314537}
  - component: {fileID: 700314540}
  - component: {fileID: 700314539}
  - component: {fileID: 700314538}
  m_Layer: 0
  m_Name: Trigger_Inside
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &700314537
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 700314536}
  m_LocalRotation: {x: -0.26527587, y: -0, z: -0, w: 0.96417254}
  m_LocalPosition: {x: -16.4, y: 22.6, z: 34.9}
  m_LocalScale: {x: 28.987082, y: 13.400316, z: 23.578089}
  m_Children: []
  m_Father: {fileID: 21834413}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -30.767002, y: 0, z: 0}
--- !u!65 &700314538
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 700314536}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &700314539
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 700314536}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f9933908c7a01f747950cacb398f2949, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &700314540
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 700314536}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &892074812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 892074813}
  - component: {fileID: 892074816}
  - component: {fileID: 892074815}
  - component: {fileID: 892074814}
  m_Layer: 0
  m_Name: DistanceToTarget_20
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &892074813
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 892074812}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -9.7, y: 37.33, z: 8.68}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 2136098481}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &892074814
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 892074812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &892074815
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 892074812}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &892074816
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 892074812}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1045447077
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1045447078}
  - component: {fileID: 1045447081}
  - component: {fileID: 1045447080}
  - component: {fileID: 1045447079}
  - component: {fileID: 1045447082}
  m_Layer: 0
  m_Name: PostDemolition_Deactivate
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1045447078
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045447077}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 22.71, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 2135428140}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1045447079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045447077}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 1
    lifeVariation: 2
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1045447080
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045447077}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1045447081
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045447077}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1045447082
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045447077}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 9
  actionDelay: 0
  checkInterval: 5
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1128504535
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1128504536}
  - component: {fileID: 1128504539}
  - component: {fileID: 1128504538}
  - component: {fileID: 1128504537}
  - component: {fileID: 1128504540}
  m_Layer: 0
  m_Name: PostDemolition_Destroy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1128504536
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128504535}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 7.7, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 2135428140}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1128504537
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128504535}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 1
    lifeVariation: 2
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1128504538
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128504535}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1128504539
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128504535}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1128504540
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128504535}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 9
  actionDelay: 0
  checkInterval: 5
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1187965590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1187965591}
  - component: {fileID: 1187965594}
  - component: {fileID: 1187965593}
  - component: {fileID: 1187965592}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1187965591
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187965590}
  m_LocalRotation: {x: -0.2545283, y: -0, z: -0, w: 0.96706533}
  m_LocalPosition: {x: 1.1, y: 10.68, z: 27.599998}
  m_LocalScale: {x: 64.24841, y: 0.45833892, z: 40.24305}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: -29.491001, y: 0, z: 0}
--- !u!65 &1187965592
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187965590}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1187965593
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187965590}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1187965594
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187965590}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1234714259
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1234714260}
  - component: {fileID: 1234714263}
  - component: {fileID: 1234714262}
  - component: {fileID: 1234714261}
  - component: {fileID: 1234714264}
  m_Layer: 0
  m_Name: Outside_Delay_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1234714260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1234714259}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 7.7, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 21834413}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1234714261
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1234714259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1234714262
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1234714259}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1234714263
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1234714259}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1234714264
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1234714259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 2113316299}
  region: 2
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1351041417
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1351041418}
  - component: {fileID: 1351041421}
  - component: {fileID: 1351041420}
  - component: {fileID: 1351041419}
  - component: {fileID: 1351041422}
  m_Layer: 0
  m_Name: Distance_10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1351041418
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351041417}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -22.31, y: 27.6, z: 8.68}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 1525006851}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1351041419
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351041417}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1351041420
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351041417}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1351041421
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351041417}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1351041422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351041417}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 10
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1514757837
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1514757838}
  - component: {fileID: 1514757841}
  - component: {fileID: 1514757840}
  - component: {fileID: 1514757839}
  - component: {fileID: 1514757842}
  m_Layer: 0
  m_Name: Inside_Delay_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1514757838
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514757837}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.3, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 21834413}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1514757839
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514757837}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1514757840
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514757837}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1514757841
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514757837}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1514757842
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514757837}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 5
  checkInterval: 1
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 700314538}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1525006850
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1525006851}
  m_Layer: 0
  m_Name: Distance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1525006851
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525006850}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.1, z: 33.8}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1351041418}
  - {fileID: 1605951967}
  - {fileID: 425615855}
  - {fileID: 1904367222}
  - {fileID: 1638777788}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1605951966
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1605951967}
  - component: {fileID: 1605951970}
  - component: {fileID: 1605951969}
  - component: {fileID: 1605951968}
  - component: {fileID: 1605951971}
  m_Layer: 0
  m_Name: Distance_20
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1605951967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605951966}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.3, y: 27.6, z: 8.68}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 1525006851}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1605951968
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605951966}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1605951969
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605951966}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1605951970
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605951966}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1605951971
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605951966}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 20
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1638777787
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1638777788}
  - component: {fileID: 1638777791}
  - component: {fileID: 1638777790}
  - component: {fileID: 1638777789}
  - component: {fileID: 1638777792}
  m_Layer: 0
  m_Name: Target_20
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1638777788
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1638777787}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 22.71, y: 27.6, z: 8.68}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 1525006851}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1638777789
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1638777787}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1638777790
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1638777787}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1638777791
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1638777787}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1638777792
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1638777787}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 20
  position: 2
  target: {fileID: 425615855}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1645076040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1645076041}
  - component: {fileID: 1645076042}
  m_Layer: 0
  m_Name: Restriction_host
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1645076041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1645076040}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -21.6, y: 27.6, z: 8.68}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 2136098481}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1645076042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1645076040}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 20
  position: 2
  target: {fileID: 1645076041}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 892074814}
  broke: 0
--- !u!1 &1688991745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1688991746}
  - component: {fileID: 1688991749}
  - component: {fileID: 1688991748}
  - component: {fileID: 1688991747}
  - component: {fileID: 1688991750}
  m_Layer: 0
  m_Name: Reset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1688991746
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1688991745}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.3, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 2135428140}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1688991747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1688991745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 1
    lifeVariation: 2
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1688991748
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1688991745}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1688991749
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1688991745}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1688991750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1688991745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 5
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1766467676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1766467677}
  - component: {fileID: 1766467680}
  - component: {fileID: 1766467679}
  - component: {fileID: 1766467678}
  - component: {fileID: 1766467681}
  m_Layer: 0
  m_Name: Fade
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1766467677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766467676}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -22.31, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 2135428140}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1766467678
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766467676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 1
    lifeVariation: 2
    fadeType: 3
    fadeTime: 1
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1766467679
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766467676}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1766467680
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766467676}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1766467681
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766467676}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 2
  actionDelay: 0
  checkInterval: 5
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1873976113
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1873976114}
  - component: {fileID: 1873976117}
  - component: {fileID: 1873976116}
  - component: {fileID: 1873976115}
  - component: {fileID: 1873976118}
  m_Layer: 0
  m_Name: Outside_Delay_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1873976114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873976113}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 22.71, y: 28.7, z: 42.48}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 21834413}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1873976115
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873976113}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 1
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1873976116
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873976113}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1873976117
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873976113}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1873976118
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873976113}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 5
  checkInterval: 1
  distance: 30
  position: 0
  target: {fileID: 0}
  Collider: {fileID: 2113316299}
  region: 2
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &1904367221
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1904367222}
  - component: {fileID: 1904367225}
  - component: {fileID: 1904367224}
  - component: {fileID: 1904367223}
  - component: {fileID: 1904367226}
  m_Layer: 0
  m_Name: Target_10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1904367222
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1904367221}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 7.7, y: 27.6, z: 8.68}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children: []
  m_Father: {fileID: 1525006851}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1904367223
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1904367221}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    mt: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    ct: 0
    pc: 1
    ine: 0
    gr: 1
    si: 6
    st: 0.005
    dm: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 40
    variation: 50
    depthFade: 0.5
    contactBias: 0.9
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
      var: 0
      seed: 0
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 0
  fading:
    onDemolition: 0
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 0
    lifeVariation: 0
    fadeType: 3
    fadeTime: 3
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1904367224
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1904367221}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 531f23781ad1cd446a6005665c959178, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1904367225
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1904367221}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!114 &1904367226
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1904367221}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a46345fd0d60284d9ae9dc18463d5e5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enable: 1
  breakAction: 4
  actionDelay: 0
  checkInterval: 1
  distance: 10
  position: 2
  target: {fileID: 425615855}
  Collider: {fileID: 0}
  region: 0
  rigid: {fileID: 0}
  broke: 0
--- !u!1 &2113316297
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2113316298}
  - component: {fileID: 2113316301}
  - component: {fileID: 2113316300}
  - component: {fileID: 2113316299}
  m_Layer: 0
  m_Name: Trigger_Outside
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2113316298
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113316297}
  m_LocalRotation: {x: -0.2652759, y: -0, z: -0, w: 0.9641726}
  m_LocalPosition: {x: 14.9, y: 13, z: 18.8}
  m_LocalScale: {x: 28.98708, y: 13.400322, z: 23.57809}
  m_Children: []
  m_Father: {fileID: 21834413}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: -30.767002, y: 0, z: 0}
--- !u!65 &2113316299
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113316297}
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &2113316300
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113316297}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f9933908c7a01f747950cacb398f2949, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2113316301
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2113316297}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Restriction
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2135428140}
  - {fileID: 1525006851}
  - {fileID: 21834413}
  - {fileID: 2136098481}
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2135428139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2135428140}
  m_Layer: 0
  m_Name: Actions
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2135428140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2135428139}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1766467677}
  - {fileID: 1688991746}
  - {fileID: 1128504536}
  - {fileID: 1045447078}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2136098480
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2136098481}
  m_Layer: 0
  m_Name: Demolition
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2136098481
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2136098480}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.1, z: 33.8}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1645076041}
  - {fileID: 892074813}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1026963950960560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4259815983461174}
  - component: {fileID: 33857885910659656}
  - component: {fileID: 23019430795422228}
  m_Layer: 12
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1384260950984344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4579781773256926}
  - component: {fileID: 108437905908397444}
  m_Layer: 12
  m_Name: Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1788531205039602
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4295906938612066}
  - component: {fileID: 108900795556225642}
  m_Layer: 12
  m_Name: Light (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1809096528030580
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4352527058251224}
  - component: {fileID: 20430599617074592}
  - component: {fileID: 81292997168438664}
  m_Layer: 12
  m_Name: Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1910325953121180
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4289063560959584}
  m_Layer: 12
  m_Name: Scene_pf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4259815983461174
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1026963950960560}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -11.7, y: 22.42, z: -20.90508}
  m_LocalScale: {x: 231.25212, y: 0.5, z: 32.296818}
  m_Children: []
  m_Father: {fileID: 4289063560959584}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &4289063560959584
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1910325953121180}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 16.36, y: -22.73637, z: 20.90508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 4352527058251224}
  - {fileID: 4259815983461174}
  - {fileID: 4579781773256926}
  - {fileID: 4295906938612066}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &4295906938612066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1788531205039602}
  m_LocalRotation: {x: 0.36599845, y: 0.45315284, z: -0.2113086, w: 0.7848862}
  m_LocalPosition: {x: -15.2336445, y: 25.73637, z: -40.18}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 4289063560959584}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 50.000004, y: 60.000004, z: 0}
--- !u!4 &4352527058251224
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1809096528030580}
  m_LocalRotation: {x: 0.099795856, y: -0, z: -0, w: 0.995008}
  m_LocalPosition: {x: -15.2336445, y: 35.18, z: -43.55}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 4289063560959584}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 11.455001, y: 0, z: 0}
--- !u!4 &4579781773256926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1384260950984344}
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: -15.2336445, y: 25.73637, z: -40.18}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 4289063560959584}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!20 &20430599617074592
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1809096528030580}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 1
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!23 &23019430795422228
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1026963950960560}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &33857885910659656
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1026963950960560}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!81 &81292997168438664
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1809096528030580}
  m_Enabled: 1
--- !u!108 &108437905908397444
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1384260950984344}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 0.45
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: 3
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.098
    m_NormalBias: 0
    m_NearPlane: 0.1
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!108 &108900795556225642
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1788531205039602}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 0.6273585, g: 0.8935312, b: 1, a: 1}
  m_Intensity: 0.1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: 3
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.098
    m_NormalBias: 0
    m_NearPlane: 0.1
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
