%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8659792085630772696
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-8443426302137519131
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot05
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-8367335585536855652
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4632512736741815040}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 6885145928231949665}
    m_Duration: 12
    m_TimeScale: 1
    m_ParentTrack: {fileID: -8367335585536855652}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-8071650420828585343
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &-7923005073686993148
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -3692370111866969794}
--- !u!114 &-7558426159854215598
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: VFX_Out_Dust
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 3446901768065710383}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-7411227013429271677
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  settings: {fileID: 2381134488722200688}
--- !u!114 &-7319778633309927989
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 05acc715f855ced458d76ee6f8ac6c61, type: 3}
  m_Name: Cinemachine Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5788976874535330713}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 4876430421855771180}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot01
  - m_Version: 1
    m_Start: 1
    m_ClipIn: 0
    m_Asset: {fileID: -5928684818333617330}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot02
  - m_Version: 1
    m_Start: 5
    m_ClipIn: 0
    m_Asset: {fileID: 1294208039998022590}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot06
  - m_Version: 1
    m_Start: 3
    m_ClipIn: 0
    m_Asset: {fileID: 5379048351997434059}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot04
  - m_Version: 1
    m_Start: 7
    m_ClipIn: 0
    m_Asset: {fileID: -2583560218939425648}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot08
  - m_Version: 1
    m_Start: 2
    m_ClipIn: 0
    m_Asset: {fileID: 2206548565761669424}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot03
  - m_Version: 1
    m_Start: 4
    m_ClipIn: 0
    m_Asset: {fileID: -6299216329163192965}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot05
  - m_Version: 1
    m_Start: 6
    m_ClipIn: 0
    m_Asset: {fileID: -2572199041655257856}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot07
  - m_Version: 1
    m_Start: 8
    m_ClipIn: 0
    m_Asset: {fileID: 6081653142438819871}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot09
  - m_Version: 1
    m_Start: 9
    m_ClipIn: 0
    m_Asset: {fileID: -5231533884574638565}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot10
  - m_Version: 1
    m_Start: 10
    m_ClipIn: 0
    m_Asset: {fileID: 4906588928013840324}
    m_Duration: 2
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot11
  - m_Version: 1
    m_Start: 12
    m_ClipIn: 0
    m_Asset: {fileID: -3213819562512076936}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot12
  - m_Version: 1
    m_Start: 13
    m_ClipIn: 0
    m_Asset: {fileID: -1261432296168871512}
    m_Duration: 1
    m_TimeScale: 1
    m_ParentTrack: {fileID: -7319778633309927989}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: GardenScreenshot13
  m_Markers:
    m_Objects: []
--- !u!114 &-7317137437559076913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 19
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot03
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-7131008805498637963
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -1103628738332058895}
--- !u!114 &-6570922307837657876
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (2)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4632512736741815040}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -8659792085630772696}
    m_Duration: 12
    m_TimeScale: 1
    m_ParentTrack: {fileID: -6570922307837657876}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-6451023458599925408
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5967542528911370929}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-6299216329163192965
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: f54671fe079036b4d9c27ff882908e79
    defaultValue: {fileID: 0}
--- !u!114 &-5967542528911370929
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: DustSpecks
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -6451023458599925408}
  - {fileID: -2607940619743124712}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-5928684818333617330
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 481e1b013d845c64d871af55ab02fd15
    defaultValue: {fileID: 0}
--- !u!114 &-5788976874535330713
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: MainCamera
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -7319778633309927989}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-5231533884574638565
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 67c4307875a15014f90be3153e63f35b
    defaultValue: {fileID: 0}
--- !u!114 &-5177876756704051802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 21
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot02
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-5134022885697489008
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -5177876756704051802}
--- !u!114 &-4632512736741815040
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CherryPetals
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: 6550219880454677243}
  - {fileID: -8367335585536855652}
  - {fileID: 2203789471623145653}
  - {fileID: -6570922307837657876}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-4618804887658133495
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -2366603001919265515}
--- !u!114 &-4131389977999528553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 17
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot13
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-3692370111866969794
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot08
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-3213819562512076936
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 139fcf79b91077341983f5a6b64a4f11
    defaultValue: {fileID: 0}
--- !u!114 &-3165178761444835798
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot12
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-2742059487785709288
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (4)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -2258655273363599782}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-2736932531141350138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot09
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-2607940619743124712
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -5967542528911370929}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &-2583560218939425648
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: de683f2279cc48a44a2fa8af3fb5b400
    defaultValue: {fileID: 0}
--- !u!114 &-2572199041655257856
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 4e8c5b7861d3bc1489c933eb8eb6bf19
    defaultValue: {fileID: 0}
--- !u!114 &-2366603001919265515
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot10
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-2258655273363599782
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: CrosshairCanvas
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -2742059487785709288}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &-1593680701497704342
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: 3465242168112935285}
--- !u!114 &-1261432296168871512
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 98054452b5904bb4184e39b55c4ea119
    defaultValue: {fileID: 0}
--- !u!114 &-1103628738332058895
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot11
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &-972088304204620511
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -2736932531141350138}
--- !u!114 &-332972253703311305
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 4697827433006530773}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: GardenScreenshots_Timeline
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: 9152765956985389056}
  - {fileID: -5788976874535330713}
  - {fileID: -2258655273363599782}
  - {fileID: 4697827433006530773}
  - {fileID: -5967542528911370929}
  - {fileID: -7558426159854215598}
  - {fileID: -4632512736741815040}
  m_FixedDuration: 0
  m_EditorSettings:
    m_Framerate: 30
    m_ScenePreview: 1
  m_DurationMode: 0
  m_MarkerTrack: {fileID: 0}
--- !u!114 &272268456012572621
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -8443426302137519131}
--- !u!114 &605009706291128125
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -4131389977999528553}
--- !u!114 &838194057308367842
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -3165178761444835798}
--- !u!114 &1294208039998022590
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: fe4f40290a4a27d45b4709a2e44a7fe9
    defaultValue: {fileID: 0}
--- !u!114 &1895764289320978762
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: 4507766153558120237}
--- !u!114 &2203789471623145653
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (1)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4632512736741815040}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -8071650420828585343}
    m_Duration: 12
    m_TimeScale: 1
    m_ParentTrack: {fileID: 2203789471623145653}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &2206548565761669424
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 475c5060d1f4bc24b87da0e9952f3090
    defaultValue: {fileID: 0}
--- !u!114 &2381134488722200688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 23
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot01
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &3446901768065710383
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -7558426159854215598}
  m_Children: []
  m_Clips: []
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &3465242168112935285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot07
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &4507766153558120237
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 16
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot06
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &4697827433006530773
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0fc6f5187a81dc47999eefade6f0935, type: 3}
  m_Name: SceneSetup
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children:
  - {fileID: -332972253703311305}
  m_Clips: []
  m_Markers:
    m_Objects: []
--- !u!114 &4876430421855771180
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 54c29c62aba40744397353c6998aa533
    defaultValue: {fileID: 0}
--- !u!114 &4906588928013840324
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: 3c1745d99e44a6e48b5e6f46acf5380f
    defaultValue: {fileID: 0}
--- !u!114 &5219041132031849148
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: 7511783726695304660}
--- !u!114 &5379048351997434059
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: dd9d2ddf54ba2d74eab4bba745b60a5b
    defaultValue: {fileID: 0}
--- !u!114 &5853592649447242171
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &6081653142438819871
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  DisplayName: 
  VirtualCamera:
    exposedName: c4dc1566b51dc2e4a80f657e74d86478
    defaultValue: {fileID: 0}
--- !u!114 &6550219880454677243
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 21bf7f712d84d26478ebe6a299f21738, type: 3}
  m_Name: Activation Track (3)
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: -4632512736741815040}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: 5853592649447242171}
    m_Duration: 12
    m_TimeScale: 1
    m_ParentTrack: {fileID: 6550219880454677243}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Active
  m_Markers:
    m_Objects: []
  m_PostPlaybackState: 3
--- !u!114 &6885145928231949665
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fde0d25a170598d46a0b9dc16b4527a5, type: 3}
  m_Name: ActivationPlayableAsset
  m_EditorClassIdentifier: 
--- !u!114 &7511783726695304660
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 06397f605c749fe4dbe2df4bbd1ef4a1, type: 3}
  m_Name: RecorderClip
  m_EditorClassIdentifier: 
  enabled: 1
  take: 17
  captureEveryNthFrame: 1
  fileNameGenerator:
    m_Path:
      m_Root: 0
      m_Leaf: Recordings\Screenshots
      m_ForceAssetFolder: 0
      m_AbsolutePath: C:/Users/<USER>/Documents/Unity/Templates-ContentTemplate_10_URP_Template/Recordings/Screenshots
    m_FileName: GardenScreenshot04
  m_Version: 1
  outputFormat: 0
  captureAlpha: 0
  m_JpegQuality: 75
  m_ImageInputSelector:
    m_Selected: gameViewInputSettings
    gameViewInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 2160
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      flipFinalOutput: 0
    cameraInputSettings:
      m_OutputResolution:
        m_CustomWidth: 1024
        m_CustomHeight: 1024
        imageHeight: 0
        maxSupportedHeight: 4320
        m_AspectRatio:
          m_CustomAspectX: 1
          m_CustomAspectY: 1
          m_ImageAspect: 1
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      captureUI: 0
    camera360InputSettings:
      source: 2
      cameraTag: 
      flipFinalOutput: 0
      renderStereo: 1
      stereoSeparation: 0.065
      mapSize: 1024
      m_OutputWidth: 1024
      m_OutputHeight: 2048
    renderTextureInputSettings:
      renderTexture: {fileID: 0}
      flipFinalOutput: 0
    renderTextureSamplerSettings:
      source: 1
      m_RenderWidth: 1280
      m_RenderHeight: 720
      m_OutputWidth: 1280
      m_OutputHeight: 720
      outputAspectRatio:
        m_CustomAspectX: 1
        m_CustomAspectY: 1
        m_ImageAspect: 1
      superSampling: 1
      superKernelPower: 16
      superKernelScale: 1
      cameraTag: 
      colorSpace: 0
      flipFinalOutput: 0
  m_EXRCompression: 2
  m_ColorSpace: 1
  _accumulationSettings:
    rid: 3761459294354276354
  references:
    version: 2
    RefIds:
    - rid: 3761459294354276354
      type: {class: AccumulationSettings, ns: , asm: Unity.Recorder.Editor}
      data:
        captureAccumulation: 0
        samples: 1
        shutterInterval: 1
        shutterProfileType: 0
        shutterProfileCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        shutterFullyOpen: 0.25
        shutterBeginsClosing: 0.75
        useSubPixelJitter: 1
--- !u!114 &8187341087091456269
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1e95aa6d658d694785bfde37c857fff, type: 3}
  m_Name: RecorderClip(Clone)(Clone)
  m_EditorClassIdentifier: 
  settings: {fileID: -7317137437559076913}
--- !u!114 &9152765956985389056
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0e6cf5671577b7344ba25c25b4346ce4, type: 3}
  m_Name: Recorder Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0.9666666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -7411227013429271677}
    m_Duration: 0.033333333333333326
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 1.9666666666666666
    m_ClipIn: 0
    m_Asset: {fileID: -5134022885697489008}
    m_Duration: 0.033333333333333215
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 2.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 8187341087091456269}
    m_Duration: 0.033333333333333215
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 3.9666666666666663
    m_ClipIn: 0
    m_Asset: {fileID: 5219041132031849148}
    m_Duration: 0.03333333333333366
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 4.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 272268456012572621}
    m_Duration: 0.033333333333333215
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 5.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 1895764289320978762}
    m_Duration: 0.033333333333332216
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 6.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -1593680701497704342}
    m_Duration: 0.033333333333332216
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 7.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -7923005073686993148}
    m_Duration: 0.033333333333332216
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 8.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -972088304204620511}
    m_Duration: 0.033333333333332216
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 9.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -4618804887658133495}
    m_Duration: 0.03333333333333133
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 11.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: -7131008805498637963}
    m_Duration: 0.033333333333333215
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 12.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 838194057308367842}
    m_Duration: 0.03333333333333133
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  - m_Version: 1
    m_Start: 13.966666666666667
    m_ClipIn: 0
    m_Asset: {fileID: 605009706291128125}
    m_Duration: 0.03333333333333133
    m_TimeScale: 1
    m_ParentTrack: {fileID: 9152765956985389056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0
    m_BlendOutDuration: 0
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: RecorderClip
  m_Markers:
    m_Objects: []
