{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to apply different material to fragments inner surface\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be shattered to multiple fragments. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Wall\i0 ", \b position \b0 to [0,0,0], and \b scale \b0 to [5,5,1]\line\par
{\pntext\f0 3.\tab}Add \b RayFire Shatter \b0 component to the Wall. \line\par
{\pntext\f0 4.\tab}\b Create \b0 new Material asset in Project window and name it "\i InnerMaterial\i0 ".\line\par
{\pntext\f0 5.\tab}Set \b InnerMaterial\b0 's \b Albedo \b0 color to \b Red\b0 .\line\par
{\pntext\f0 6.\tab}\b Select \b0 Wall.\line\par
{\pntext\f0 7.\tab}Drag and drop \b InnerMaterial \b0 from Project window to \b Inner Material \b0 field in Material group of properties.\line\par
{\pntext\f0 8.\tab}Click on the \b Fragment \b0 button. A new gameobject \ldblquote\i Wall_root\i0\rdblquote  with fragments as it\rquote s children will be created.\line\par
{\pntext\f0 9.\tab}Start to \b drag Scale Preview \b0 slider from the left to the right. You will see that the original object's MeshRender disabled so it won't cover fragments and fagment's scale is getting lower so it is possible to see every fragment's shape and inner surface with Inner Material applied.\line\par
{\pntext\f0 10.\tab}\b Toggle OFF Scale \b0 button to reset fragment's scale and enable original object's MeshRender component.\line\par
}
 