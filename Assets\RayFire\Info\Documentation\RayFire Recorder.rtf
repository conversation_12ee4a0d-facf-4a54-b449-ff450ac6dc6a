{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fnil\fcharset2 Symbol;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 RayFire Recorder\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
\fs22\lang9 RayFire Recorder can record it's children simulation animation into animation clip in Editor mode and then play this animation in Play Mode\fs24\lang1033\par
\fs28\lang9\tab\fs24\lang1033\par
\b\fs48\lang9\tab Main\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Mode\b0\lang1033 : Defines component active mode.\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f1\'b7\tab\b\f0\fs22 None\b0 : Component is disabled. \fs24\par
\f1\'b7\tab\b\f0\fs22 Record\b0 : Component will record it's children animation in animation clip.\fs24\par
\f1\'b7\tab\b\f0\fs22 Play\b0 : Component will playback defined animation clip for it's children. If mode set to Play and you start Play Mode but animation clip and animator controller were not defined then mode will be set to None.\fs24\par

\pard\nowidctlpar\sl276\slmult1\qc\par

\pard\nowidctlpar\sl276\slmult1\b\fs48\lang9\tab Record Properties\b0\fs24\lang1033\par
\par
\b\fs22 Record On Start\b0 : Recorder will automatically start recording animation at Start. If Record On Start is disabled then you will be able to start recording animation using Start Record button on top of UI.\fs24\par
\par
\b\fs22 Clip Name\b0 : Defines base name for animation clip and controller. After animation recorded new animation clip and controller will be created in \b\i Asset/RayFireRecords \b0\i0 folder. After that you can move these asset files in any folder you need.\fs24\par
\par
\b\fs22 Duration\b0 : Defines duration for animation recording in seconds. You can stop recording using Stop Record button on top of UI if needed.\fs24\par
\par
\b\fs22 Rate\b0 : Defines amount of keys per second for every animation track in recorded animation clip.\fs24\par
\par
\b\fs22 Reduce Keys\b0 : Allows to remove unneeded keys to optimize animation clip and reduce it's size. \fs24\par
\par
\b\fs22 Threshold\b0 : Defines difference threshold between keys, key will be removed if difference is less than \b Threshold \b0 value. Zero value means that \b Threshold \b0 is turned off and only the same keys will be removed.\fs24\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Playback Properties\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Play On Start\b0 : \lang1033 Recorder will automatically start play animation at Start. If Play On Start is disabled then you will be able to start playing animation using Start Play button on top of UI. \fs24\par
\par
\b\fs22\lang9 Animation Clip\b0 : Animation clip which was created in Record mode\lang1033 .\fs24\par
\par
\b\fs22\lang9 Controller\b0 : Runtime Animator Controller which was created in Record mode\fs24\lang1033\par
\par
\b\fs48\lang9\tab Rigid Playback\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Set To Kinematic\b0 : If recorded object has Rigid component it will be set to Kinematic at start so it won't be simulated as dynamic object again but will affect other dynamic objects in scene\lang1033 . It is also possible to activate such object at some point to make them dynamic again.\fs24\par
\par
\par
}
 