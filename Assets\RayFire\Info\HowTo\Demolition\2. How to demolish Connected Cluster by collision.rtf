{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish Connected Cluster by collision.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par
Connected cluster consists of multiple objects (Shards) with mesh as children of one root without mesh. Unity will simulate the whole structure as one solid object but it is possible to demolish it and detach it's fragments to Mesh objects and to lesser Connected Clusters.\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be shattered to multiple fragments. These fragments will be used as Shards for Connected Cluster. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Pillar\i0 ", \b position \b0 to [0,10,0], \b rotation \b0 to [0,0,40] and \b scale \b0 to [10,0.5,1]\line\par
{\pntext\f0 3.\tab}Create another \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 4.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [15,1,10]\line\par
{\pntext\f0 5.\tab}Add \b RayFire Shatter \b0 component to Pillar and set \b Type \b0 to \b Voronoi\b0 . In Voronoi properties set \b Amount \b0 to \b 100\b0 .\line\par
{\pntext\f0 6.\tab}Click on the \b Fragment \b0 button, a new gameobject \ldblquote\i Pillar_root\i0\rdblquote  with fragments as it\rquote s children will be created. Destroy original Pillar because we don\rquote t need it anymore.\line\par
{\pntext\f0 7.\tab}Add \b RayFire Rigid \b0 component to Pillar_root.\line\par
{\pntext\f0 8.\tab}Set Rigid \b Initialization \b0 to \b At Start.\b0\line\par
{\pntext\f0 9.\tab}Set \b Simulation \b0 type to \b Dynamic \b0 so the object will start to fall down immediately after Initialization.\line\par
{\pntext\f0 10.\tab}Set \b Object \b0 type to \b Connected Cluster\b0 .\line\par
{\pntext\f0 11.\tab}Set \b Demolition \b0 type to \b Runtime\b0 . This is the only demoltion type that work for Connected Cluster.\line\par
{\pntext\f0 12.\tab}\b Start \b0 Play Mode. \line\line Pillar will fall down and a group of shards around contact point will be detached and will start to simulate separately. These shards will get new Rigid component applied and will inherit all Rigid properties (except Object type) from their main Connected Cluster. But then you will see that the rest of the Connected Cluster collides with these shards and continue demolishing further detaching more shards and getting smaller.\line\par
{\pntext\f0 13.\tab}\b Turn off \b0 Play Mode. \line\par
{\pntext\f0 14.\tab}In Connected Cluster \b Limitations \b0 properties set \b Solidity \b0 to \b 0.6 \b0 to make it less fragile.\line\par
{\pntext\f0 15.\tab}\b Start \b0 Play Mode. \line\par
{\pntext\f0 16.\tab}This time Connected Cluster was demolished only once at the collision point, but the amount of detached shards is too low, now lets increase their amount.\line\par
{\pntext\f0 17.\tab}\b Turn off \b0 Play Mode. \line\par
{\pntext\f0 18.\tab}In Cluster Demolition properties set \b Ratio \b0 to \b 60\b0 . This property defines radius around collision point in percentage relative to Connected Cluster size. All shards inside this radius will be detached. In our case Cluster size is approximately 10 units, with value 60 it means that all shards in 6 units radius around the collision point will be detached.\line\par
{\pntext\f0 19.\tab}\b Start \b0 Play Mode.\fs24\lang1033\line\fs22\lang9  \fs24\lang1033\par
{\pntext\f0 20.\tab}\fs22\lang9 Now a lot of shards were detached but they all simulate separately which looks not realistic. Some of these shards can be grouped in small Connected Clusters.\fs24\lang1033\line\par
{\pntext\f0 21.\tab}\b\fs22\lang9 Turn off \b0 Play Mode. \fs24\lang1033\line\par
{\pntext\f0 22.\tab}\fs22\lang9 In Cluster Demolition properties set \b Shard Area \b0 value to \b 20\b0 . Default 100 % value means that all shards in collision radius will be detached into separate Mesh objects. Value 20 means that only 20% of sards around the collision point will be detached into separate Mesh objects and the rest of detached shards will be grouped in small Connected Clusters. Amount of such small clusters can be defined by \b Min \b0 and \b Max Amount \b0 properties.\fs24\lang1033\par
}
 