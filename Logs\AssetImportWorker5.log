Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.38f1c1 (b17906c7b2b6) revision 11630854'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 48295 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\GameSoft\Unity\Editor\2022.3.38f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
D:/GameSoft/Unity/g-pro/My project
-logFile
Logs/AssetImportWorker5.log
-srvPort
62321
Successfully changed project path to: D:/GameSoft/Unity/g-pro/My project
D:/GameSoft/Unity/g-pro/My project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [37664]  Target information:

Player connection [37664]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1016484094 [EditorId] 1016484094 [Version] 1048832 [Id] WindowsEditor(7,Top1a-Va11hallA) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [37664] Host joined multi-casting on [***********:54997]...
Player connection [37664] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 24.97 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.38f1c1 (b17906c7b2b6)
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/g-pro/My project/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 5070 Ti (ID=0x2c05)
    Vendor:   NVIDIA
    VRAM:     15907 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56312
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005588 seconds.
- Loaded All Assemblies, in  0.474 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.141 seconds
Domain Reload Profiling: 613ms
	BeginReloadAssembly (61ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (98ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (60ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (271ms)
				TypeCache.ScanAssembly (264ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (141ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (111ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (71ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.414 seconds
Refreshing native plugins compatible for Editor in 5.09 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.382 seconds
Domain Reload Profiling: 793ms
	BeginReloadAssembly (83ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (16ms)
	RebuildCommonClasses (17ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (288ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (146ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (86ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (383ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (293ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (40ms)
			ProcessInitializeOnLoadAttributes (201ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
Refreshing native plugins compatible for Editor in 8.54 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4930 Unused Serialized files (Serialized files now loaded: 0)
Unloading 99 unused Assets / (443.0 KB). Loaded Objects now: 5380.
Memory consumption went from 232.8 MB to 232.4 MB.
Total: 5.176700 ms (FindLiveObjects: 0.697000 ms CreateObjectMapping: 0.362600 ms MarkObjects: 3.679100 ms  DeleteObjects: 0.436400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
