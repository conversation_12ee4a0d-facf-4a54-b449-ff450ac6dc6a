{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fnil\fcharset2 Symbol;}{\f2\fnil\fcharset0 Calibri;}{\f3\fmodern\fprq1\fcharset204 Consolas;}{\f4\fmodern\fprq1\fcharset0 Consolas;}{\f5\fnil Calibri;}}
{\colortbl ;\red0\green0\blue255;\red120\green157\blue235;\red255\green132\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang1033 RayFire Blade\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs24\par
\fs22\lang9 RayFire Blade componenet should be used if you want to slice or demolish object with Rigid component in Runtime. Slicing supported only for \b Mesh \b0 object types while demolition initiation can be used for Connected and Nested Clusters.\fs24\lang1033\par
\par
{\fs22\lang9{\field{\*\fldinst{HYPERLINK https://www.youtube.com/watch?v=wNgXlZS0mTA }}{\fldrslt{https://www.youtube.com/watch?v=wNgXlZS0mTA\ul0\cf0}}}}\f0\fs24\par
\par
\fs22\lang9 There are several requirements for Blade component:\fs24\lang1033\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f1\'b7\tab\f0\fs22\lang9 Object which you want to slice should have \b RayFire Rigid \b0 component.\fs24\lang1033\par
\f1\'b7\tab\f0\fs22\lang9 Rigid component \b Demolition type \b0 should be set to \b Runtime\b0 .\fs24\lang1033\par
\f1\'b7\tab\b\f0\fs22\lang9 Slice By Blade \b0 property In Rigid  / Limitations properties should be \b Enabled\b0 .\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
\fs28\lang9\tab\fs24\lang1033\par
\b\fs48\lang9\tab Properties\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Action\b0\lang1033 : Using Blade you can slice object or initiate demolition by Blade's trigger collider.\fs24\par

\pard\nowidctlpar\fi-10\li720\sl276\slmult1\tx720\f1\'b7\tab\b\f0\fs22 Slice\b0 : Object will be Sliced accordingly to Slice Type plane. \fs24\par
\f1\'b7\tab\b\f0\fs22 Demolish\b0 : Object will demolished accordingly to it's Mesh or Cluster Demolition properties.\fs24\par
\par

\pard\nowidctlpar\fi-10\li10\sl276\slmult1\tx10\tx720\b\fs22\lang9 On Trigger\b0\lang1033 : Defines moment when object will be sliced or demolished.\fs24\par

\pard\nowidctlpar\fi-10\li720\sl276\slmult1\tx720\f1\'b7\tab\b\f0\fs22 Enter\b0 : Object will be sliced when Blade's trigger collider enter object's collider.\fs24\par
\f1\'b7\tab\b\f0\fs22 Exit\b0 : Object will be sliced when Blade's trigger collider exit object's collider.\fs24\par
\f1\'b7\tab\b\f0\fs22 Enter Exit\b0 : Object will be sliced when Blade's trigger collider exit object's collider and angle for slicing plane will be average angle between enter and exit. This type should be used if object with Blade will be rotated while it is inside sliced object so slicing plane at least will have average angle.\fs24\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22 Slice\b0 : Defines slicing plane which will be used to slice target object.\fs24\par

\pard\nowidctlpar\li1080\sl276\slmult1\tx1080\f1\'b7\tab\b\f0\fs22 XY\b0 : Plane will lay on X and Y axes. It's normal will point over Z axis.\fs24\par
\f1\'b7\tab\b\f0\fs22 XZ\b0 : Plane will lay on X and Z axes. It's normal will point over Y axis.\fs24\par
\f1\'b7\tab\b\f0\fs22 YZ\b0 : Plane will lay on Y and Z axes. It's normal will point over X axis.\fs24\par

\pard\nowidctlpar\sl276\slmult1\par

\pard\box\brdrdash\brdrw0 \nowidctlpar\sl276\slmult1\b\fs22\lang9 Damage\b0 : \f2\lang1033 Damage value applied to sliced object with Rigid component and enabled Damage. Object will be sliced only after Rigid's Current damage value will reach Maximum damage value.\b\f0\lang9\par

\pard\box\brdrs\brdrw0\brdrcf4 \nowidctlpar\sl276\slmult1\lang1033\par
Target\b0 : Slicing also can be initiated by Slice Target button or by public\cf2\b\f3\fs18\lang1049  \cf3 SliceTarget\cf0\b0 ()\f4\lang1033  \f0\fs22 method. In this case object with Blade doesn't have to enter or exit sliced object collider, but you need to define Target Gameobject for slice.\fs24\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22 Targets\b0 : You can define several Targets for manual slicing.\par
\par
\b\fs48\lang9\tab Force\b0\fs24\lang1033\par
\par

\pard\box\brdrdash\brdrw0 \nowidctlpar\sl276\slmult1\b\fs22\lang9 Force\b0 : \f5\lang1033 Add to sliced fragments additional velocity impulse to separate them\f2 .\b\f0\lang9\par

\pard\nowidctlpar\sl276\slmult1\par
Affect Inactive\b0 : \lang1033 Force will be applied to Inactive objects as well.\par
\fs24\par
\b\fs48\lang9\tab Filters\b0\fs24\lang1033\par
\par

\pard\box\brdrdash\brdrw0 \nowidctlpar\sl276\slmult1\b\fs22\lang9 Cooldown\b0 : \f5\lang1033 Allows to temporary disable Blade component for defined time to prevent constant slicing.\b\f0\lang9\par

\pard\nowidctlpar\sl276\slmult1\par
Tag\b0 : \lang1033 Blade will slice only objects with picked Tag. You can pick only one Tag.\fs24\par
\par
\b\fs22\lang9 Layer\b0 : \lang1033 Blade will slice only objects with defined layer. You can define several Layers.\fs24\par
\par
\b\fs48\lang9\tab Targets\par
\fs22 Target List\b0 : \lang1033 List of game objects which can be sliced using \b SliceTarget ()\b0  public method. No collision required.\fs24\par
\b\fs48\lang9\par
}
 