{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish Mesh object by shooting\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create a \b Cube\b0 , this will be the cube which will \lang1033 shoot and demolish\lang9 . \line\par
{\pntext\f0 2.\tab}Set its name to "\i Wall\i0 ", \b position \b0 to [0,2,0] and \b scale \b0 to [1, 3, 4]\line\par
{\pntext\f0 3.\tab}\b Remove \b0 it's \b Box Collider \b0 because the Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 6.\tab}\b Add RayFire Rigid \b0 component to Wall.\line\par
{\pntext\f0 7.\tab}Set Rigid \b Initialization \b0 to \b At Start \b0 and \b Demolition type \b0 to \b\f1\lang1049 R\f0\lang1033 untime\b0\lang9 .\line\par
{\pntext\f0 8.\tab}\lang1033 In \b Damage \b0 properties \b turn On Enable \b0 checkbox\lang9 . Now every shot will apply damage defined in Gun component, when Current Damage value will reach Max Damage value object will be demolished.\line\par
{\pntext\f0 9.\tab}Create \b Cylinder\b0 , this will be our gun\lang1033 .\lang9\line\par
{\pntext\f0 10.\tab}Set its name to "\i Gun\i0 ", \b position \b0 to [5,2,0], \b rotation \b0 to [0, 0, 90] and \b scale \b0 to [0.2,0.4,0.2]\line\par
{\pntext\f0 11.\tab}\b Add RayFire Gun \b0 component to Gun object and set \b Axis \b0 property to \b Y Green\b0 , \b disable Show Impact \b0 property.\line\par
{\pntext\f0 12.\tab}\lang1033 Set \b Impact Strength\b0  property to \b 3\b0 , \b Damage \b0 value to \b 60 \b0 and \b enable Flash \b0 checkbox.\lang9\line\fs24\lang1033\par
{\pntext\f0 13.\tab}\b\fs22\lang9 Start \b0 Play Mode. \line\par
{\pntext\f0 14.\tab}\b Select \b0 Gun object and click on \b Single Shot \b0 button. The same can be initiated in your code by public method Shoot()\line\line\lang1033 Wall object will get physical impact and a small flash will appear for a moment at the impact point\lang9 .\line\par
{\pntext\f0 15.\tab}\b Select \b0 Wall object, in Damage properties you will see that \b Current Damage \b0 value now is 60. This damage was applied by first shot.\line\par
{\pntext\f0 16.\tab}\b Select \b0 Gun object and click on \b Single Shot \b0 button again.\line\line This time Wall object will be demolished because Current Damage value will be 120 which is more than Max Damage 100.\line\line\par

\pard\nowidctlpar\sl276\slmult1\line\fs24\lang1033\par
}
 