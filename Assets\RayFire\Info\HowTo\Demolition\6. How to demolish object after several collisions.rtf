{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish object after several collisions\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Brick\i0 ", \b position \b0 to [0,10,0], \b rotation \b0 to [0, 0, 20] and \b scale \b0 to [2,1,1]\line\par
{\pntext\f0 3.\tab}\b Remove \b0 it's Box Collider because Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 6.\tab}Add \b RayFire Rigid \b0 component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid \b Initialization to At Start\b0\f1\lang1049 .\f0\lang9\line\par
{\pntext\f0 8.\tab}Set \b Simulation \b0 type to \b Dynamic\b0 .\line\par
{\pntext\f0 9.\tab}Set \b Object \b0 type to \b Mesh\b0 ..\line\b\par
{\pntext\f0 10.\tab}\b0 Set \b Demolition \b0 type to \b Runtime\b0 .\line\par
{\pntext\f0 11.\tab}Start Play Mode.\line\line\tab Bricks fall down and demolish at first contact. Solidity property defines whether an object will be demolished at collision or not, but in some cases you may need to \b collect \b0 collision damage and demolish objects after several collisions.\line\par
{\pntext\f0 12.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 13.\tab}In \b Limitations \b0 properties set \b Solidity \b0 to \b 10 \b0 so Brick won't be demolished by single collison.\line\par
{\pntext\f0 14.\tab}In \b Damage \b0 properties turn On \b Enable \b0 property and under \b Collision \b0 caption enable \b Collect \b0 property.\line\par
{\pntext\f0 15.\tab}Set \b Max Damage \b0 to \b 50\b0 . This value defines how much damage an object can take before will be demolished. \line\par
{\pntext\f0 16.\tab}Start Play Mode.\line\line\tab Bricks fall down without being demolished because now it has high Solidity. Notice that \b Current Damage \b0 value has increased from 0 to about 25. One collision not enough to reach max Damage value.\line\par
{\pntext\f0 17.\tab}\b Turn Off \b0 Play mode.\line\par
{\pntext\f0 18.\tab}Create a \b Sphere \b0 object.\line\par
{\pntext\f0 19.\tab}Set its \b name \b0 to \ldblquote\i Small Sphere\i0\rdblquote  \b position \b0 to [0, 12, 0] and \b scale \b0 to [0.2, 0.2, 0.2]\line\par
{\pntext\f0 20.\tab}Add \b RayFire Rigid \b0 to the Small Sphere object.\line\par
{\pntext\f0 21.\tab}Create another \b Sphere \b0 object.\line\par
{\pntext\f0 22.\tab}Set its \b name \b0 to \ldblquote\i Big Sphere\i0\rdblquote  \b position \b0 to [0.5, 13, 0] and \b scale \b0 to [0.5, 0.5, 0.5]\line\par
{\pntext\f0 23.\tab}Add \b RayFire Rigid \b0 to the Big Sphere object.\line\par
{\pntext\f0 24.\tab}\b Start \b0 Play Mode.\line\line\tab Bricks fall down without being demolished and it\rquote s current damage value now is about 25.\line\par
{\pntext\f0 25.\tab}\b Select \b0 the Big Sphere object and in the RayFire Rigid component click on \b Initialize\b0 .\line\par
{\pntext\f0 26.\tab}\b Select \b0 Brick object. Notice that after collision with Big Sphere it\rquote s \b Current Damage \b0 now about 42.\line\par
{\pntext\f0 27.\tab}\b Select \b0 the Small Sphere object and in the RayFire Rigid component click on \b Initialize\b0 .\line\line\tab Sphere falls on the Brick object and demolishes it because Brick\rquote s Current Damage property finally exceeded Max Damage property value. Playing with Multiplier property you can multiply applied collision damage. Another way to demolish an object by damage is to shoot it using a RayFire Gun component but this will be covered in another How-To.\par

\pard\nowidctlpar\sl276\slmult1\b\par
}
 