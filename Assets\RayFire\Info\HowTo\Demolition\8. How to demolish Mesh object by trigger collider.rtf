{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to initiate demolition by trigger collider\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Brick\i0 " and \b position \b0 to [0,5,0] and scale to [3,1,1]\line\par
{\pntext\f0 3.\tab}\b Remove \b0 it's Box Collider because Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "\i Ground\i0 ", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 6.\tab}Add \b RayFire Rigid \b0 component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid \b Initialization to At Start\b0 .\line\par
{\pntext\f0 8.\tab}Set \b Simulation \b0 type to \b Sleeping\b0 .\line\par
{\pntext\f0 9.\tab}Set \b Object \b0 type to \b Mesh\b0 .\line\par
{\pntext\f0 10.\tab}Set \b Demolition \b0 type to \b Runtime\b0 .\line\par
{\pntext\f0 11.\tab}Create \b empty Gameobject\b0 . This is the object which will trigger Brick demolition.\line\par
{\pntext\f0 12.\tab}Set its \b name \b0 to "\i Trigger\i0 " and \b position \b0 to [3, 5, 0]\line\par
{\pntext\f0 13.\tab}Add \b Box Collider \b0 component to Trigger and enable \b Is Trigger \b0 property\b .\b0\line\par
{\pntext\f0 14.\tab}Add \b RayFire Blade \b0 component to Trigger object.\line\par
{\pntext\f0 15.\tab}Set \b Action \b0 property to \b Demolish \b0 and \b On Trigger \b0 property to \b Enter\b0 .\line\par
{\pntext\f0 16.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 17.\tab}\b Select \b0 Trigger and start moving it by its \b X axis \b0 towards the Brick object.\line\line\tab Brick object will be demolished to fragments as soon as Trigger will collide with it.\line\par
{\pntext\f0 18.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 19.\tab}\b Select \b0 Trigger and set \b On Trigger \b0 property to \b Exit\b0 .\line\par
{\pntext\f0 20.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 21.\tab}\b Select \b0 Trigger and start moving it by its X axis towards the Brick object.\line\par

\pard\nowidctlpar\sl276\slmult1\tab\tab This time Brick object will be demolished only when Trigger object will \b exit \b0\tab it.\par
\line\tab\fs24\lang1033\par
}
 