Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.38f1c1 (b17906c7b2b6) revision 11630854'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 48295 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\GameSoft\Unity\Editor\2022.3.38f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/GameSoft/Unity/g-pro/My project
-logFile
Logs/AssetImportWorker2.log
-srvPort
62321
Successfully changed project path to: D:/GameSoft/Unity/g-pro/My project
D:/GameSoft/Unity/g-pro/My project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19172]  Target information:

Player connection [19172]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3956619497 [EditorId] 3956619497 [Version] 1048832 [Id] WindowsEditor(7,Top1a-Va11hallA) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19172] Host joined multi-casting on [***********:54997]...
Player connection [19172] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 20.47 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.38f1c1 (b17906c7b2b6)
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/g-pro/My project/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 5070 Ti (ID=0x2c05)
    Vendor:   NVIDIA
    VRAM:     15907 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56216
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004694 seconds.
- Loaded All Assemblies, in  0.189 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.142 seconds
Domain Reload Profiling: 329ms
	BeginReloadAssembly (53ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (76ms)
		LoadAssemblies (51ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (75ms)
			TypeCache.Refresh (74ms)
				TypeCache.ScanAssembly (66ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (142ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (111ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (75ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.369 seconds
Refreshing native plugins compatible for Editor in 5.50 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.385 seconds
Domain Reload Profiling: 751ms
	BeginReloadAssembly (79ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (16ms)
	RebuildCommonClasses (17ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (249ms)
		LoadAssemblies (170ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (119ms)
			TypeCache.Refresh (100ms)
				TypeCache.ScanAssembly (91ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (385ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (291ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (43ms)
			ProcessInitializeOnLoadAttributes (202ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.16 seconds
Refreshing native plugins compatible for Editor in 14.35 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4919 Unused Serialized files (Serialized files now loaded: 0)
Unloading 99 unused Assets / (443.6 KB). Loaded Objects now: 5369.
Memory consumption went from 232.5 MB to 232.1 MB.
Total: 4.913800 ms (FindLiveObjects: 0.598700 ms CreateObjectMapping: 0.139200 ms MarkObjects: 3.741100 ms  DeleteObjects: 0.432200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 63854.073897 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/2 Shatter. Types. Splinters.unity
  artifactKey: Guid(41774344b044f9149b5f2e93bc5f30d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/2 Shatter. Types. Splinters.unity using Guid(41774344b044f9149b5f2e93bc5f30d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '839e995cacc99845260b6b4d21879169') in 0.001906 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.735334 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/3 Shatter. Types. Slabs.unity
  artifactKey: Guid(55bee203d29565a49a96912a56d54317) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/3 Shatter. Types. Slabs.unity using Guid(55bee203d29565a49a96912a56d54317) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'c622c9412e4c3cd62fa56106b4d350e5') in 0.000553 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.612566 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/4 Shatter. Types. Radial.unity
  artifactKey: Guid(837b0f0b63994524291ed658477f11f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/4 Shatter. Types. Radial.unity using Guid(837b0f0b63994524291ed658477f11f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '48bad448cd6e424f8436a7a45f0adcb4') in 0.000558 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.161278 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/5 Shatter. Types. Custom.unity
  artifactKey: Guid(d6f7c001d7dba724ca0cf78b9c871796) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/5 Shatter. Types. Custom.unity using Guid(d6f7c001d7dba724ca0cf78b9c871796) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '1e6f8e487a3e8c90ca26b72d8dad83de') in 0.000636 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.491808 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/6 Shatter. Types. Slices.unity
  artifactKey: Guid(7abbc52cf181dac41a1ef7155108b9b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/6 Shatter. Types. Slices.unity using Guid(7abbc52cf181dac41a1ef7155108b9b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '2d57769678e57acde325c3b0f5ab79dc') in 0.000690 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.695364 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/7 Shatter. Types. Tets.unity
  artifactKey: Guid(769ed5f9107354a4c9c273d3cf2162c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/7 Shatter. Types. Tets.unity using Guid(769ed5f9107354a4c9c273d3cf2162c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '71d2b375602cafb9bcf5d0ef6de9c427') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.272033 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/10 Shatter. Types. Bricks.unity
  artifactKey: Guid(e44580c7f0db96146908962634475106) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/10 Shatter. Types. Bricks.unity using Guid(e44580c7f0db96146908962634475106) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'ceebe8acd3f493b15c9c382ad516e6ef') in 0.000561 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.832360 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/9 Shatter. Clusters.unity
  artifactKey: Guid(9aa0af86f9521e14c81ecda22518a68b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/9 Shatter. Clusters.unity using Guid(9aa0af86f9521e14c81ecda22518a68b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'ddd01f2cc51730f43d6d15a9fb1543cf') in 0.000565 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.951108 seconds.
  path: Assets/RayFire/Tutorial/Scenes/1 Shatter/8 Shatter. Types. Decompose.unity
  artifactKey: Guid(074936205a6145243992516a145a984f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/1 Shatter/8 Shatter. Types. Decompose.unity using Guid(074936205a6145243992516a145a984f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '5e333b382d0d2133b221fd984e51e0b9') in 0.000566 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.246506 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid
  artifactKey: Guid(483520c8a1847b04eb284135068dbce9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid using Guid(483520c8a1847b04eb284135068dbce9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'd752e217fdf10b1ffceb898dc5f995bd') in 0.000623 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.437400 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/1 Rigid. Simulation Types.unity
  artifactKey: Guid(2725ae4a7ed4ef1458d9cdda106c98ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/1 Rigid. Simulation Types.unity using Guid(2725ae4a7ed4ef1458d9cdda106c98ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'bb8dc863b4c683cb9b21ae82b0d90d64') in 0.000566 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.890983 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/2 Rigid. Object Types.unity
  artifactKey: Guid(1f7014d41bd7cc748966c0e675ef67d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/2 Rigid. Object Types.unity using Guid(1f7014d41bd7cc748966c0e675ef67d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '460d47abb55cc68e645a5dba90f189f4') in 0.000575 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.490423 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/3 Rigid. Mesh Demolition Types.unity
  artifactKey: Guid(5c2dc9388cf024a47b240f3c8e7f04f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/3 Rigid. Mesh Demolition Types.unity using Guid(5c2dc9388cf024a47b240f3c8e7f04f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '06b8b9b6ee4430a8f6c46f8edca1296b') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.529990 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/4 Rigid. Physics.unity
  artifactKey: Guid(5ea52a5ee01b9f645a75aecc32bfa031) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/4 Rigid. Physics.unity using Guid(5ea52a5ee01b9f645a75aecc32bfa031) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8c666722163ffbba35d19137ebeaaa53') in 0.000570 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.380323 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/5 Rigid. Activation.unity
  artifactKey: Guid(ddf8bcb480da2fc42b6e0c70a22ba646) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/5 Rigid. Activation.unity using Guid(ddf8bcb480da2fc42b6e0c70a22ba646) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e4f259dbbe9ff7bb05fe360cda4b3304') in 0.000541 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.583316 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/8 Rigid. Mesh Demolition. Fragments.unity
  artifactKey: Guid(85bfba699af6c2f4bbdbeb7a6e8d6a71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/8 Rigid. Mesh Demolition. Fragments.unity using Guid(85bfba699af6c2f4bbdbeb7a6e8d6a71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8923792a29f2e6fdf3735257cb339ccc') in 0.000569 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.850719 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/14 Rigid. Damage.unity
  artifactKey: Guid(9e2a6136c1190ca4a9ebbf4bf22c5998) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/14 Rigid. Damage.unity using Guid(9e2a6136c1190ca4a9ebbf4bf22c5998) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'd7cd0acede00cf7ad8125a21be83172c') in 0.000543 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.787898 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/15 Rigid. Fading.unity
  artifactKey: Guid(bea3646119c210c429c21d5fc745faa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/15 Rigid. Fading.unity using Guid(bea3646119c210c429c21d5fc745faa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '56d0f952a4c3760019558c7417d364f9') in 0.000564 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.069408 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/16 Rigid. Reset.unity
  artifactKey: Guid(2191d4075c14b0041afd4a9900ca4bd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/16 Rigid. Reset.unity using Guid(2191d4075c14b0041afd4a9900ca4bd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '4f8c622d1f1bb4931a6653503aab97ba') in 0.000553 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.386176 seconds.
  path: Assets/RayFire/Tutorial/Scenes/5 Blade
  artifactKey: Guid(98140d9b639af8241804148c047dfe0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/5 Blade using Guid(98140d9b639af8241804148c047dfe0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '77a577eed38ee014c4fe60b50ab992bb') in 0.000625 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.814768 seconds.
  path: Assets/RayFire/Tutorial/Scenes/4 Dust
  artifactKey: Guid(287d884a8deeefb4fb69ad638ef714bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/4 Dust using Guid(287d884a8deeefb4fb69ad638ef714bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '629f61e5e8a84c389c5b71ba894ef501') in 0.000571 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.564732 seconds.
  path: Assets/RayFire/Tutorial/Scenes/3 Debris/9 Debris. Pool.unity
  artifactKey: Guid(69cc999c56ea03b4f83d291541da5749) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/3 Debris/9 Debris. Pool.unity using Guid(69cc999c56ea03b4f83d291541da5749) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8346ab1b3dd4468a74a3127462744b90') in 0.000556 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.641667 seconds.
  path: Assets/RayFire/Tutorial/Scenes/3 Debris/3 Debris. Emission.unity
  artifactKey: Guid(d05acf2eafb2c7c4fae049540d9cd24c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/3 Debris/3 Debris. Emission.unity using Guid(d05acf2eafb2c7c4fae049540d9cd24c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '035f8f2ebe2c18fdce685af4a543d69a') in 0.000645 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.707118 seconds.
  path: Assets/RayFire/Tutorial/Scenes/3 Debris/1 Debris. Types.unity
  artifactKey: Guid(ef2cedcf2d46a144cb8da6823b1fd8ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/3 Debris/1 Debris. Types.unity using Guid(ef2cedcf2d46a144cb8da6823b1fd8ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'f37e7f212b573cee0035e5fa1b334486') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.758383 seconds.
  path: Assets/RayFire/Tutorial/Scenes/6 Connectivity
  artifactKey: Guid(3ce03bfc306ea944989cf7236a9432ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/6 Connectivity using Guid(3ce03bfc306ea944989cf7236a9432ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'd91de9c4f29283eaf4969903c0e5b507') in 0.000644 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.560175 seconds.
  path: Assets/RayFire/Tutorial/Scenes/6 Connectivity/6 Connectivity. Event.unity
  artifactKey: Guid(288e819af23847649b2950311b69677e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/6 Connectivity/6 Connectivity. Event.unity using Guid(288e819af23847649b2950311b69677e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'deba8a00ec58dc8ccf87d10fef06e3c6') in 0.000617 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.824764 seconds.
  path: Assets/RayFire/Tutorial/Scenes/6 Connectivity/3 Connectivity. Gun.unity
  artifactKey: Guid(a86864bd30a00384aa403df2f62e6e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/6 Connectivity/3 Connectivity. Gun.unity using Guid(a86864bd30a00384aa403df2f62e6e71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '1ac56b00dbbd5103238c7211cc4d312b') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.781095 seconds.
  path: Assets/RayFire/Tutorial/Scenes/6 Connectivity/2 Connectivity. Activator.unity
  artifactKey: Guid(6b6a55c0304048a4c9c2f6b301e789ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/6 Connectivity/2 Connectivity. Activator.unity using Guid(6b6a55c0304048a4c9c2f6b301e789ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '04f14b10246f8bdea6113dd06bf93be7') in 0.000575 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.130372 seconds.
  path: Assets/RayFire/Tutorial/Scenes/7 Restriction
  artifactKey: Guid(fc974670cac09fa4eafab552a569817d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/7 Restriction using Guid(fc974670cac09fa4eafab552a569817d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '9b8eb817504e4f7e6d163aa57e1a59eb') in 0.000564 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.799395 seconds.
  path: Assets/RayFire/Tutorial/Scenes/8 Activator
  artifactKey: Guid(ee9cab52b52caa142afcd695acc1c7c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/8 Activator using Guid(ee9cab52b52caa142afcd695acc1c7c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'cc6aac1ec8b50aefa8512e43a778ab01') in 0.000573 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.849143 seconds.
  path: Assets/RayFire/Tutorial/Scenes/8 Activator/2. Activator. Activation.unity
  artifactKey: Guid(7762da131a48a434792ad926d282ea86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/8 Activator/2. Activator. Activation.unity using Guid(7762da131a48a434792ad926d282ea86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '850ae2a022d3693cf7e2bb7b737b56db') in 0.000539 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.040647 seconds.
  path: Assets/RayFire/Tutorial/Scenes/8 Activator/1. Activator. Gizmo.unity
  artifactKey: Guid(83f5ce91ecfcb814aabed3a9a22923c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/8 Activator/1. Activator. Gizmo.unity using Guid(83f5ce91ecfcb814aabed3a9a22923c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '86f3cbb07f2d2723bb179487a03616ae') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.451782 seconds.
  path: Assets/RayFire/Tutorial/Scenes/8 Activator/3. Activator. Animation.unity
  artifactKey: Guid(da31cabd6d9fc6f4c85b23bf9f1f685a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/8 Activator/3. Activator. Animation.unity using Guid(da31cabd6d9fc6f4c85b23bf9f1f685a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '4a0666cc673e1567730c873b4a4f9cab') in 0.000550 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.760998 seconds.
  path: Assets/RayFire/Tutorial/Scenes/10 Bomb
  artifactKey: Guid(bbf1623e2d1158d4dbeac14d78c3da1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/10 Bomb using Guid(bbf1623e2d1158d4dbeac14d78c3da1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'c1cdb3bcb0c9d4418f6f705053d89807') in 0.000578 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.459699 seconds.
  path: Assets/RayFire/Tutorial/Scenes/9 RigidRoot
  artifactKey: Guid(4d7ac61f7374c604dbb429b3090196af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/9 RigidRoot using Guid(4d7ac61f7374c604dbb429b3090196af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '030ecb6b28612d7513f7c987049a0f32') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.037524 seconds.
  path: Assets/RayFire/Tutorial/Scenes/9 RigidRoot/1 RigidRoot..unity
  artifactKey: Guid(cd04590cfc68bbb4d8004bc6d3235072) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/9 RigidRoot/1 RigidRoot..unity using Guid(cd04590cfc68bbb4d8004bc6d3235072) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'baf213ba4423f68eafb95ae9e8140830') in 0.000569 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 276.911042 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/7 Rigid. Limitations.unity
  artifactKey: Guid(75e3a6bb18c0d3547891986d1a8e708b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/7 Rigid. Limitations.unity using Guid(75e3a6bb18c0d3547891986d1a8e708b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '33985ace7f80879bd461f06f4380fe75') in 0.000834 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.350570 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/9 Rigid. Mesh Demolition. Runtime Caching.unity
  artifactKey: Guid(e8891b7c6f008354997dfe70053534ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/9 Rigid. Mesh Demolition. Runtime Caching.unity using Guid(e8891b7c6f008354997dfe70053534ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '2b09818cbd2fc46de37fc87e2cbfbf97') in 0.000586 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.374940 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/11 Rigid. Connected cluster demolition.unity
  artifactKey: Guid(b7816cf177b965e43afeb36ef23b8d16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/11 Rigid. Connected cluster demolition.unity using Guid(b7816cf177b965e43afeb36ef23b8d16) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'fbf504696c915e4c399bdefcddbd7632') in 0.000541 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.772790 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/10 Rigid. Nested Cluster demolition.unity
  artifactKey: Guid(ba27746e678846d4389c2edd09ca2fec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/10 Rigid. Nested Cluster demolition.unity using Guid(ba27746e678846d4389c2edd09ca2fec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '0602e827341cfa228e48945040fde943') in 0.000583 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 12.436981 seconds.
  path: Assets/RayFire/Tutorial/Scenes/2 Rigid/13 Rigid. Material.unity
  artifactKey: Guid(569f9d0d9e396844a80ee74158a12429) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Scenes/2 Rigid/13 Rigid. Material.unity using Guid(569f9d0d9e396844a80ee74158a12429) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'f8333e9d7c3d811cdce6e8106e294f95') in 0.000544 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 502.517528 seconds.
  path: Assets/RayFire
  artifactKey: Guid(5aa0128b2875fd944a0a83f50b50cb94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire using Guid(5aa0128b2875fd944a0a83f50b50cb94) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '846d28876c2de0526d556140feec1433') in 0.000729 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 21.869818 seconds.
  path: Assets/SharedAssets/FirstPersonController
  artifactKey: Guid(a217b3fd32973134c8df3000cefc5b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/SharedAssets/FirstPersonController using Guid(a217b3fd32973134c8df3000cefc5b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '784fb92a0c94bd95b18912e8574dcaa7') in 0.000529 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 627.037693 seconds.
  path: Assets/VoronoiGlassBreaker
  artifactKey: Guid(e27c33a2cb7f94c4ebc1d3dfda0d1f08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker using Guid(e27c33a2cb7f94c4ebc1d3dfda0d1f08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'b0c217fa27b0720987db7ec5268515dd') in 0.000616 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.973277 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts
  artifactKey: Guid(975e4d5c21e059c41a1a5868a749e8ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts using Guid(975e4d5c21e059c41a1a5868a749e8ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '75a64ab71df03ee7d9d5946385bcde06') in 0.000511 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.192842 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/VoronoiGlassBreaker.cs
  artifactKey: Guid(3bf524794276eca47b469b32e0c60367) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/VoronoiGlassBreaker.cs using Guid(3bf524794276eca47b469b32e0c60367) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'a1cb19bc01d341f1afa8e563d5d9c1f0') in 0.000570 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 10.987246 seconds.
  path: Assets/New SceneTemplate.scenetemplate
  artifactKey: Guid(ef58a7d48ac196741a4c6a63f073224d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New SceneTemplate.scenetemplate using Guid(ef58a7d48ac196741a4c6a63f073224d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '4f563e17717dc721e0a46ea50717c77e') in 0.028722 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 24.626567 seconds.
  path: Assets/New Scene.unity
  artifactKey: Guid(de729eba75f68d5449fd1ec5e0027317) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Scene.unity using Guid(de729eba75f68d5449fd1ec5e0027317) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e76365007df01ee16ba65521c8e384f8') in 0.000621 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 6.745069 seconds.
  path: Assets/VoronoiGlassBreaker/Editor
  artifactKey: Guid(b614f6ed62a5a734983c8d770027906a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Editor using Guid(b614f6ed62a5a734983c8d770027906a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '55e4a4db1f3c8c80afe157b3559ef5ed') in 0.000547 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.554587 seconds.
  path: Assets/VoronoiGlassBreaker/Editor/VoronoiGlassBreakerEditor.cs
  artifactKey: Guid(bba666e759b51e840a0fe63d995787ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Editor/VoronoiGlassBreakerEditor.cs using Guid(bba666e759b51e840a0fe63d995787ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '7446a6f996c85837527cedac899b925d') in 0.000572 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 7.437129 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/GlassAudioManager.cs
  artifactKey: Guid(f526889b14062e749968eb2fc981b266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/GlassAudioManager.cs using Guid(f526889b14062e749968eb2fc981b266) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '2e564a94b896971afca774582ab9c182') in 0.001343 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.532332 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/GlassBreakEffect.cs
  artifactKey: Guid(30e3c4bc2a8c7b441ad5eff942735b7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/GlassBreakEffect.cs using Guid(30e3c4bc2a8c7b441ad5eff942735b7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '35266114b60c3146bef34f6001aeb802') in 0.000537 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 31.174777 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/VoronoiGenerator.cs
  artifactKey: Guid(a34e5ee2f7a6fda499289db59a331e91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/VoronoiGenerator.cs using Guid(a34e5ee2f7a6fda499289db59a331e91) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8fbb47a8d4b5d034f4df01adb730071e') in 0.000643 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.552859 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/GlassBreakerDemo.cs
  artifactKey: Guid(2b7e129f5d02a654eb89ad89d8754b0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/GlassBreakerDemo.cs using Guid(2b7e129f5d02a654eb89ad89d8754b0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '5cb7d3d9462e0bcef2dd120251d6a2cf') in 0.000548 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 110.284121 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/GlassBreakerDemo.cs
  artifactKey: Guid(2b7e129f5d02a654eb89ad89d8754b0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/GlassBreakerDemo.cs using Guid(2b7e129f5d02a654eb89ad89d8754b0c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'ea0c2551fb6e4fcb179da17efee5d9a0') in 0.000936 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.408 seconds
Refreshing native plugins compatible for Editor in 5.51 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.815 seconds
Domain Reload Profiling: 8221ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (87ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (154ms)
		LoadAssemblies (198ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (7816ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (276ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (190ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 6.52 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (398.8 KB). Loaded Objects now: 5399.
Memory consumption went from 193.2 MB to 192.8 MB.
Total: 3.115500 ms (FindLiveObjects: 0.365900 ms CreateObjectMapping: 0.058700 ms MarkObjects: 2.564800 ms  DeleteObjects: 0.124800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.390 seconds
Refreshing native plugins compatible for Editor in 6.52 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.906 seconds
Domain Reload Profiling: 1292ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (194ms)
		LoadAssemblies (217ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (906ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (372ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (259ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 10.72 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (398.8 KB). Loaded Objects now: 5415.
Memory consumption went from 193.2 MB to 192.8 MB.
Total: 4.921400 ms (FindLiveObjects: 0.974400 ms CreateObjectMapping: 0.241400 ms MarkObjects: 3.502500 ms  DeleteObjects: 0.201700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.369 seconds
Refreshing native plugins compatible for Editor in 6.14 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.888 seconds
Domain Reload Profiling: 1253ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (184ms)
		LoadAssemblies (201ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (889ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (336ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (229ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 12.19 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (399.1 KB). Loaded Objects now: 5431.
Memory consumption went from 193.2 MB to 192.8 MB.
Total: 6.808800 ms (FindLiveObjects: 0.789400 ms CreateObjectMapping: 0.157100 ms MarkObjects: 5.580100 ms  DeleteObjects: 0.279400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.386 seconds
Refreshing native plugins compatible for Editor in 6.87 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.864 seconds
Domain Reload Profiling: 1247ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (193ms)
		LoadAssemblies (211ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (865ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (340ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (244ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 7.98 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (399.3 KB). Loaded Objects now: 5447.
Memory consumption went from 193.2 MB to 192.9 MB.
Total: 5.187300 ms (FindLiveObjects: 0.676900 ms CreateObjectMapping: 0.133800 ms MarkObjects: 4.177300 ms  DeleteObjects: 0.197800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 556.074545 seconds.
  path: Assets/RayFire/Tutorial/Fbx/column_corner.FBX
  artifactKey: Guid(749a1382091dfba4aa89d2decb6d6e36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Fbx/column_corner.FBX using Guid(749a1382091dfba4aa89d2decb6d6e36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '88642e9de8fbfdda5ad1f942f5875ab4') in 0.277316 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 32
========================================================================
Received Import Request.
  Time since last request: 10.542568 seconds.
  path: Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.prefab
  artifactKey: Guid(12883d23aa5d4d649a98a8e2438f5c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.prefab using Guid(12883d23aa5d4d649a98a8e2438f5c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'bfa4de9c40700fc0b0828bc62a87a763') in 0.018598 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 7.978714 seconds.
  path: Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.prefab
  artifactKey: Guid(12883d23aa5d4d649a98a8e2438f5c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.prefab using Guid(12883d23aa5d4d649a98a8e2438f5c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '5901049615b36409d503c8d36f35d0b9') in 0.028989 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 10
========================================================================
Received Import Request.
  Time since last request: 6.089707 seconds.
  path: Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.mat
  artifactKey: Guid(5c9d08e427a040e4e83ba1adcfd22de8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.mat using Guid(5c9d08e427a040e4e83ba1adcfd22de8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '3e69c55efaacce74d302b2fbbbed34ba') in 0.044770 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 12.385001 seconds.
  path: Assets/VoronoiGlassBreaker/Materials
  artifactKey: Guid(a5ded7d51a027b34c8da0fe6e90ae909) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Materials using Guid(a5ded7d51a027b34c8da0fe6e90ae909) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'd872742fb805c702c8b5d25c52bd0d1c') in 0.000573 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 20.190770 seconds.
  path: Assets/VoronoiGlassBreaker/Materials/Plane.prefab
  artifactKey: Guid(9c470238859b4544a8860657b30efaeb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Materials/Plane.prefab using Guid(9c470238859b4544a8860657b30efaeb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8ee1cbc21257395b0f128817a5bc7818') in 0.012540 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 274.499328 seconds.
  path: Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.prefab
  artifactKey: Guid(12883d23aa5d4d649a98a8e2438f5c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Materials/VoronoiGlass.prefab using Guid(12883d23aa5d4d649a98a8e2438f5c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '6b57617baea058faadf3436beb2fe90c') in 0.010079 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 10
========================================================================
Received Import Request.
  Time since last request: 3.187643 seconds.
  path: Assets/New Scene.unity
  artifactKey: Guid(de729eba75f68d5449fd1ec5e0027317) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Scene.unity using Guid(de729eba75f68d5449fd1ec5e0027317) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '48f93e5aa3de33f492fafa54ed99bb9a') in 0.000580 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.416 seconds
Refreshing native plugins compatible for Editor in 6.85 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.868 seconds
Domain Reload Profiling: 1281ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (210ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (869ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (324ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (210ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 10.19 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (398.7 KB). Loaded Objects now: 5549.
Memory consumption went from 229.3 MB to 228.9 MB.
Total: 5.480200 ms (FindLiveObjects: 0.626500 ms CreateObjectMapping: 0.124100 ms MarkObjects: 4.529500 ms  DeleteObjects: 0.198800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.331 seconds
Refreshing native plugins compatible for Editor in 5.37 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.758 seconds
Domain Reload Profiling: 1086ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (171ms)
		LoadAssemblies (187ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (759ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (309ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (50ms)
			ProcessInitializeOnLoadAttributes (213ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 8.49 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (398.8 KB). Loaded Objects now: 5565.
Memory consumption went from 229.3 MB to 228.9 MB.
Total: 7.181500 ms (FindLiveObjects: 1.018800 ms CreateObjectMapping: 0.194300 ms MarkObjects: 4.577600 ms  DeleteObjects: 1.389100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.368 seconds
Refreshing native plugins compatible for Editor in 5.46 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.814 seconds
Domain Reload Profiling: 1179ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (192ms)
		LoadAssemblies (211ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (814ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (294ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (202ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 15.26 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (399.3 KB). Loaded Objects now: 5581.
Memory consumption went from 229.3 MB to 228.9 MB.
Total: 5.984200 ms (FindLiveObjects: 0.927900 ms CreateObjectMapping: 0.227200 ms MarkObjects: 4.606500 ms  DeleteObjects: 0.221400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.392 seconds
Refreshing native plugins compatible for Editor in 9.40 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.867 seconds
Domain Reload Profiling: 1256ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (2ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (205ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (16ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (868ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (318ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (217ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 16.98 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4714 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (399.2 KB). Loaded Objects now: 5597.
Memory consumption went from 229.3 MB to 228.9 MB.
Total: 4.991300 ms (FindLiveObjects: 0.624200 ms CreateObjectMapping: 0.123600 ms MarkObjects: 4.052200 ms  DeleteObjects: 0.189700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 799.841863 seconds.
  path: Assets/RayFire/Scripts
  artifactKey: Guid(688c1197d213fc94b97523e89815b1ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Scripts using Guid(688c1197d213fc94b97523e89815b1ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '7471e1e6bbd380969de176b57e7dbf01') in 0.002358 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.062204 seconds.
  path: Assets/RayFire/Scripts/RayFireAssembly.asmdef
  artifactKey: Guid(c677688ef3042284595272795d5b252d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Scripts/RayFireAssembly.asmdef using Guid(c677688ef3042284595272795d5b252d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '24dd361b232348c23b4bef054d1d4de0') in 0.000564 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 33.153776 seconds.
  path: Assets/Scenes/Cockpit/Art/Props/Meshes/CockpitGlassFlat_Mesh.fbx
  artifactKey: Guid(5b912376a362146dda9fbd2fcdacedb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Cockpit/Art/Props/Meshes/CockpitGlassFlat_Mesh.fbx using Guid(5b912376a362146dda9fbd2fcdacedb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '0767b3909d14d2e7cae23a5432913053') in 0.286350 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 74
========================================================================
Received Import Request.
  Time since last request: 0.218649 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/DoorGlass_100x200_02_Prefab.prefab
  artifactKey: Guid(bd9e0ee4457fda4449cf43d2b827fbc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/DoorGlass_100x200_02_Prefab.prefab using Guid(bd9e0ee4457fda4449cf43d2b827fbc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '949def548724e6c9657a511901008718') in 0.087409 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 53
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Meshes/DoorGlass_100x200_02_Mesh.fbx
  artifactKey: Guid(9a3b4474612d22b4fba1fadae1745a96) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Meshes/DoorGlass_100x200_02_Mesh.fbx using Guid(9a3b4474612d22b4fba1fadae1745a96) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '3f1447e7dd05ecb9597804835b1ff940') in 0.056654 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 32
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Scenes/Cockpit/Art/Props/Textures/Glass_T_Noise.png
  artifactKey: Guid(cbeaaa58b351343a1acd97edafdb1ba2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Cockpit/Art/Props/Textures/Glass_T_Noise.png using Guid(cbeaaa58b351343a1acd97edafdb1ba2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'bc6a831ce22a35bcd7c33cc772a716db') in 0.025612 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/DoorGlass_100x200_01_Prefab_Flipped.prefab
  artifactKey: Guid(27e140a629ea9d94e8c14bd198718b56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/DoorGlass_100x200_01_Prefab_Flipped.prefab using Guid(27e140a629ea9d94e8c14bd198718b56) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e3526f71dfa0f34c63a330307688530f') in 0.025954 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 73
========================================================================
Received Import Request.
  Time since last request: 32.578029 seconds.
  path: Assets/Scenes/Cockpit/Art/Environment/Prefabs/Asteroid_02_Prefab.prefab
  artifactKey: Guid(33343cf1a716649fc9b6d1e06569c6d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Cockpit/Art/Environment/Prefabs/Asteroid_02_Prefab.prefab using Guid(33343cf1a716649fc9b6d1e06569c6d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'a148f3d020693ce6beb6fafd65e7f5d1') in 0.049447 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 24
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Scenes/Garden/Art/Vegetation/Prefabs/Bamboo_Seedling.prefab
  artifactKey: Guid(ce260bf77f626ef4c849d5350b8d125a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Vegetation/Prefabs/Bamboo_Seedling.prefab using Guid(ce260bf77f626ef4c849d5350b8d125a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '8d663ff612900dace966395bdac4c8a8') in 0.128214 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 47
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_20x100_01_Prefab.prefab
  artifactKey: Guid(fa21c78222ed22140afacbdac3c0f43a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_20x100_01_Prefab.prefab using Guid(fa21c78222ed22140afacbdac3c0f43a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '7e7b41eb118ccc148a75fe5cee1f81e5') in 0.025677 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 35
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Meshes/BeamWooden_20x100_01_Mesh.fbx
  artifactKey: Guid(bcf320292eaa90647a4280cc2c1181f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Meshes/BeamWooden_20x100_01_Mesh.fbx using Guid(bcf320292eaa90647a4280cc2c1181f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'fc5d54b3b9d7d8aa17915f48b09a8ddd') in 0.040106 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RayFire/Tutorial/Fbx/bend.FBX
  artifactKey: Guid(16c2ff531b4eba442a45a7dfd32c66d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RayFire/Tutorial/Fbx/bend.FBX using Guid(16c2ff531b4eba442a45a7dfd32c66d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '985011b682cf7aa546bfdbc7348dde5e') in 0.033314 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Scenes/Garden/Art/Vegetation/Prefabs/Bamboo_Group.prefab
  artifactKey: Guid(60e45149c88bd7f4c970f5cfd41ee742) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Vegetation/Prefabs/Bamboo_Group.prefab using Guid(60e45149c88bd7f4c970f5cfd41ee742) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '99f95e090ee6540f5ae1c1130065c143') in 0.091578 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 52
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_20x330_01_Prefab.prefab
  artifactKey: Guid(71418bea553194f4d8c3ec9179bbc437) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_20x330_01_Prefab.prefab using Guid(71418bea553194f4d8c3ec9179bbc437) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '232b3034c2835a8c9419c5e58bc4d7fc') in 0.020258 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 45
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_10X120_01_Prefab.prefab
  artifactKey: Guid(94832ae50096eb146b35190600473f6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_10X120_01_Prefab.prefab using Guid(94832ae50096eb146b35190600473f6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '91fa4aa4c3062b3573b2c2e7391a9ed0') in 0.020776 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 35
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Meshes/BeamWooden_13x16x215_01_Mesh.fbx
  artifactKey: Guid(a5a350949a4533a4ab1da1f0289e45b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Meshes/BeamWooden_13x16x215_01_Mesh.fbx using Guid(a5a350949a4533a4ab1da1f0289e45b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'eb8d34858058f15cf576ad6d89a30aed') in 0.039904 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 20
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error: operation aborted. errorcode: 995, details: 由于线程退出或应用程序请求，已中止 I/O 操作。
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0