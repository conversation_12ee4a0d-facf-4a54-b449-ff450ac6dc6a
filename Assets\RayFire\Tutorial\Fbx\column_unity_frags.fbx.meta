fileFormatVersion: 2
guid: 324bc992ed03a404c98b36a3d7f15fd6
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: //RootNode
    100002: column_sh_1
    100004: column_sh_10
    100006: column_sh_11
    100008: column_sh_12
    100010: column_sh_13
    100012: column_sh_14
    100014: column_sh_15
    100016: column_sh_16
    100018: column_sh_17
    100020: column_sh_18
    100022: column_sh_19
    100024: column_sh_2
    100026: column_sh_20
    100028: column_sh_21
    100030: column_sh_22
    100032: column_sh_23
    100034: column_sh_24
    100036: column_sh_25
    100038: column_sh_26
    100040: column_sh_27
    100042: column_sh_28
    100044: column_sh_29
    100046: column_sh_3
    100048: column_sh_30
    100050: column_sh_31
    100052: column_sh_32
    100054: column_sh_33
    100056: column_sh_34
    100058: column_sh_35
    100060: column_sh_36
    100062: column_sh_37
    100064: column_sh_38
    100066: column_sh_39
    100068: column_sh_4
    100070: column_sh_40
    100072: column_sh_41
    100074: column_sh_42
    100076: column_sh_43
    100078: column_sh_44
    100080: column_sh_45
    100082: column_sh_46
    100084: column_sh_47
    100086: column_sh_48
    100088: column_sh_49
    100090: column_sh_5
    100092: column_sh_50
    100094: column_sh_51
    100096: column_sh_52
    100098: column_sh_53
    100100: column_sh_54
    100102: column_sh_55
    100104: column_sh_56
    100106: column_sh_57
    100108: column_sh_58
    100110: column_sh_59
    100112: column_sh_6
    100114: column_sh_60
    100116: column_sh_61
    100118: column_sh_62
    100120: column_sh_63
    100122: column_sh_64
    100124: column_sh_65
    100126: column_sh_66
    100128: column_sh_67
    100130: column_sh_68
    100132: column_sh_69
    100134: column_sh_7
    100136: column_sh_70
    100138: column_sh_71
    100140: column_sh_72
    100142: column_sh_73
    100144: column_sh_74
    100146: column_sh_75
    100148: column_sh_76
    100150: column_sh_77
    100152: column_sh_78
    100154: column_sh_79
    100156: column_sh_8
    100158: column_sh_80
    100160: column_sh_81
    100162: column_sh_82
    100164: column_sh_83
    100166: column_sh_84
    100168: column_sh_85
    100170: column_sh_86
    100172: column_sh_87
    100174: column_sh_88
    100176: column_sh_89
    100178: column_sh_9
    100180: column_sh_90
    100182: column_sh_91
    400000: //RootNode
    400002: column_sh_1
    400004: column_sh_10
    400006: column_sh_11
    400008: column_sh_12
    400010: column_sh_13
    400012: column_sh_14
    400014: column_sh_15
    400016: column_sh_16
    400018: column_sh_17
    400020: column_sh_18
    400022: column_sh_19
    400024: column_sh_2
    400026: column_sh_20
    400028: column_sh_21
    400030: column_sh_22
    400032: column_sh_23
    400034: column_sh_24
    400036: column_sh_25
    400038: column_sh_26
    400040: column_sh_27
    400042: column_sh_28
    400044: column_sh_29
    400046: column_sh_3
    400048: column_sh_30
    400050: column_sh_31
    400052: column_sh_32
    400054: column_sh_33
    400056: column_sh_34
    400058: column_sh_35
    400060: column_sh_36
    400062: column_sh_37
    400064: column_sh_38
    400066: column_sh_39
    400068: column_sh_4
    400070: column_sh_40
    400072: column_sh_41
    400074: column_sh_42
    400076: column_sh_43
    400078: column_sh_44
    400080: column_sh_45
    400082: column_sh_46
    400084: column_sh_47
    400086: column_sh_48
    400088: column_sh_49
    400090: column_sh_5
    400092: column_sh_50
    400094: column_sh_51
    400096: column_sh_52
    400098: column_sh_53
    400100: column_sh_54
    400102: column_sh_55
    400104: column_sh_56
    400106: column_sh_57
    400108: column_sh_58
    400110: column_sh_59
    400112: column_sh_6
    400114: column_sh_60
    400116: column_sh_61
    400118: column_sh_62
    400120: column_sh_63
    400122: column_sh_64
    400124: column_sh_65
    400126: column_sh_66
    400128: column_sh_67
    400130: column_sh_68
    400132: column_sh_69
    400134: column_sh_7
    400136: column_sh_70
    400138: column_sh_71
    400140: column_sh_72
    400142: column_sh_73
    400144: column_sh_74
    400146: column_sh_75
    400148: column_sh_76
    400150: column_sh_77
    400152: column_sh_78
    400154: column_sh_79
    400156: column_sh_8
    400158: column_sh_80
    400160: column_sh_81
    400162: column_sh_82
    400164: column_sh_83
    400166: column_sh_84
    400168: column_sh_85
    400170: column_sh_86
    400172: column_sh_87
    400174: column_sh_88
    400176: column_sh_89
    400178: column_sh_9
    400180: column_sh_90
    400182: column_sh_91
    2100000: Default-Material
    2300000: column_sh_1
    2300002: column_sh_10
    2300004: column_sh_11
    2300006: column_sh_12
    2300008: column_sh_13
    2300010: column_sh_14
    2300012: column_sh_15
    2300014: column_sh_16
    2300016: column_sh_17
    2300018: column_sh_18
    2300020: column_sh_19
    2300022: column_sh_2
    2300024: column_sh_20
    2300026: column_sh_21
    2300028: column_sh_22
    2300030: column_sh_23
    2300032: column_sh_24
    2300034: column_sh_25
    2300036: column_sh_26
    2300038: column_sh_27
    2300040: column_sh_28
    2300042: column_sh_29
    2300044: column_sh_3
    2300046: column_sh_30
    2300048: column_sh_31
    2300050: column_sh_32
    2300052: column_sh_33
    2300054: column_sh_34
    2300056: column_sh_35
    2300058: column_sh_36
    2300060: column_sh_37
    2300062: column_sh_38
    2300064: column_sh_39
    2300066: column_sh_4
    2300068: column_sh_40
    2300070: column_sh_41
    2300072: column_sh_42
    2300074: column_sh_43
    2300076: column_sh_44
    2300078: column_sh_45
    2300080: column_sh_46
    2300082: column_sh_47
    2300084: column_sh_48
    2300086: column_sh_49
    2300088: column_sh_5
    2300090: column_sh_50
    2300092: column_sh_51
    2300094: column_sh_52
    2300096: column_sh_53
    2300098: column_sh_54
    2300100: column_sh_55
    2300102: column_sh_56
    2300104: column_sh_57
    2300106: column_sh_58
    2300108: column_sh_59
    2300110: column_sh_6
    2300112: column_sh_60
    2300114: column_sh_61
    2300116: column_sh_62
    2300118: column_sh_63
    2300120: column_sh_64
    2300122: column_sh_65
    2300124: column_sh_66
    2300126: column_sh_67
    2300128: column_sh_68
    2300130: column_sh_69
    2300132: column_sh_7
    2300134: column_sh_70
    2300136: column_sh_71
    2300138: column_sh_72
    2300140: column_sh_73
    2300142: column_sh_74
    2300144: column_sh_75
    2300146: column_sh_76
    2300148: column_sh_77
    2300150: column_sh_78
    2300152: column_sh_79
    2300154: column_sh_8
    2300156: column_sh_80
    2300158: column_sh_81
    2300160: column_sh_82
    2300162: column_sh_83
    2300164: column_sh_84
    2300166: column_sh_85
    2300168: column_sh_86
    2300170: column_sh_87
    2300172: column_sh_88
    2300174: column_sh_89
    2300176: column_sh_9
    2300178: column_sh_90
    2300180: column_sh_91
    3300000: column_sh_1
    3300002: column_sh_10
    3300004: column_sh_11
    3300006: column_sh_12
    3300008: column_sh_13
    3300010: column_sh_14
    3300012: column_sh_15
    3300014: column_sh_16
    3300016: column_sh_17
    3300018: column_sh_18
    3300020: column_sh_19
    3300022: column_sh_2
    3300024: column_sh_20
    3300026: column_sh_21
    3300028: column_sh_22
    3300030: column_sh_23
    3300032: column_sh_24
    3300034: column_sh_25
    3300036: column_sh_26
    3300038: column_sh_27
    3300040: column_sh_28
    3300042: column_sh_29
    3300044: column_sh_3
    3300046: column_sh_30
    3300048: column_sh_31
    3300050: column_sh_32
    3300052: column_sh_33
    3300054: column_sh_34
    3300056: column_sh_35
    3300058: column_sh_36
    3300060: column_sh_37
    3300062: column_sh_38
    3300064: column_sh_39
    3300066: column_sh_4
    3300068: column_sh_40
    3300070: column_sh_41
    3300072: column_sh_42
    3300074: column_sh_43
    3300076: column_sh_44
    3300078: column_sh_45
    3300080: column_sh_46
    3300082: column_sh_47
    3300084: column_sh_48
    3300086: column_sh_49
    3300088: column_sh_5
    3300090: column_sh_50
    3300092: column_sh_51
    3300094: column_sh_52
    3300096: column_sh_53
    3300098: column_sh_54
    3300100: column_sh_55
    3300102: column_sh_56
    3300104: column_sh_57
    3300106: column_sh_58
    3300108: column_sh_59
    3300110: column_sh_6
    3300112: column_sh_60
    3300114: column_sh_61
    3300116: column_sh_62
    3300118: column_sh_63
    3300120: column_sh_64
    3300122: column_sh_65
    3300124: column_sh_66
    3300126: column_sh_67
    3300128: column_sh_68
    3300130: column_sh_69
    3300132: column_sh_7
    3300134: column_sh_70
    3300136: column_sh_71
    3300138: column_sh_72
    3300140: column_sh_73
    3300142: column_sh_74
    3300144: column_sh_75
    3300146: column_sh_76
    3300148: column_sh_77
    3300150: column_sh_78
    3300152: column_sh_79
    3300154: column_sh_8
    3300156: column_sh_80
    3300158: column_sh_81
    3300160: column_sh_82
    3300162: column_sh_83
    3300164: column_sh_84
    3300166: column_sh_85
    3300168: column_sh_86
    3300170: column_sh_87
    3300172: column_sh_88
    3300174: column_sh_89
    3300176: column_sh_9
    3300178: column_sh_90
    3300180: column_sh_91
    4300000: column_sh_1
    4300002: column_sh_2
    4300004: column_sh_3
    4300006: column_sh_4
    4300008: column_sh_5
    4300010: column_sh_6
    4300012: column_sh_7
    4300014: column_sh_8
    4300016: column_sh_9
    4300018: column_sh_10
    4300020: column_sh_11
    4300022: column_sh_12
    4300024: column_sh_13
    4300026: column_sh_14
    4300028: column_sh_15
    4300030: column_sh_16
    4300032: column_sh_17
    4300034: column_sh_18
    4300036: column_sh_19
    4300038: column_sh_20
    4300040: column_sh_21
    4300042: column_sh_22
    4300044: column_sh_23
    4300046: column_sh_24
    4300048: column_sh_25
    4300050: column_sh_26
    4300052: column_sh_27
    4300054: column_sh_28
    4300056: column_sh_29
    4300058: column_sh_30
    4300060: column_sh_31
    4300062: column_sh_32
    4300064: column_sh_33
    4300066: column_sh_34
    4300068: column_sh_35
    4300070: column_sh_36
    4300072: column_sh_37
    4300074: column_sh_38
    4300076: column_sh_39
    4300078: column_sh_40
    4300080: column_sh_41
    4300082: column_sh_42
    4300084: column_sh_43
    4300086: column_sh_44
    4300088: column_sh_45
    4300090: column_sh_46
    4300092: column_sh_47
    4300094: column_sh_48
    4300096: column_sh_49
    4300098: column_sh_50
    4300100: column_sh_51
    4300102: column_sh_52
    4300104: column_sh_53
    4300106: column_sh_54
    4300108: column_sh_55
    4300110: column_sh_56
    4300112: column_sh_57
    4300114: column_sh_58
    4300116: column_sh_59
    4300118: column_sh_60
    4300120: column_sh_61
    4300122: column_sh_62
    4300124: column_sh_63
    4300126: column_sh_64
    4300128: column_sh_65
    4300130: column_sh_66
    4300132: column_sh_67
    4300134: column_sh_68
    4300136: column_sh_69
    4300138: column_sh_70
    4300140: column_sh_71
    4300142: column_sh_72
    4300144: column_sh_73
    4300146: column_sh_74
    4300148: column_sh_75
    4300150: column_sh_76
    4300152: column_sh_77
    4300154: column_sh_78
    4300156: column_sh_79
    4300158: column_sh_80
    4300160: column_sh_81
    4300162: column_sh_82
    4300164: column_sh_83
    4300166: column_sh_84
    4300168: column_sh_85
    4300170: column_sh_86
    4300172: column_sh_87
    4300174: column_sh_88
    4300176: column_sh_89
    4300178: column_sh_90
    4300180: column_sh_91
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Default-Material
    second: {fileID: 2100000, guid: aea6a14ad54e0b34191abbc84ef4ea7d, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
