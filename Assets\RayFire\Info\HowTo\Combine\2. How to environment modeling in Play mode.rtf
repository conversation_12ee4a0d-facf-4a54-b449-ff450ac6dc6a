{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to environment modeling in Play mode.\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\par
Combine component allows you to combine multiple meshes into one mesh. This can be useful for enviroment modeling if you want to spread a lot of objects over scene and then bake them into one static mesh. \par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0 ,0, 0] and \b scale \b0 to [15, 1, 10]\line\par
{\pntext\f0 3.\tab}Create \b\lang1033 Cube\b0\lang9  and set its \b position \b0 to [0, 1, 0]\line\par
{\pntext\f0 4.\tab}Create \b Material \b0 asset, set it sname to "\i Red\i0 ", set its Albedo color to \b Red \b0 and apply to \b Cube\b0 .\line\par
{\pntext\f0 5.\tab}Create \b Sphere \b0 and set its \b position \b0 to [0, 5, 0]\line\par
{\pntext\f0 6.\tab}Create \b Material \b0 asset, set it sname to "\i Green\i0 ", set its Albedo color to \b Green \b0 and apply to \b Sphere \b0 .\line\line You can create more different objects with different materials, but for this \f1\lang1049 t\f0\lang1033 utorial\lang9  it will be enough just these two objects.\line\par
{\pntext\f0 7.\tab}Create \b empty \b0 gameobject, set its name to "\i Combine\i0 " and \b position \b0 to  [0, 0.5, 0]\line\par
{\pntext\f0 8.\tab}Add \b RayFire Combine\b0  component to Combine object.\line\par
{\pntext\f0 9.\tab}\b Select \b0 Cube and Sphere and in Hierarchy set them as \b children \b0 for Combine object.\line\par
{\pntext\f0 10.\tab}Add RayFire Rigid component to Cube and Sphere.\line\par
{\pntext\f0 11.\tab}In Rigid component set Initialization to At Start and Demolition Type to Runtime, in Limitations properties set Depth to 2.\line\par
{\pntext\f0 12.\tab}Start Play Mode.\line\line Sphere will fall to the Cube and both of them will be demolished to fragments. Some fragments will be demolished again. At this stage you can select some fragments and move them around scene as you want.\line\line Notice that original Cube and Sphere were destroyed and all fragments now children of \i RayFireMan / Storage_Fragments \i0 object. In order to bake them into one mesh you need to set them as children of Combine object.\line\par
{\pntext\f0 13.\tab}Select Storage_Fragments object which is child of RayFire Man object and set it as child for Combine object.\line\par
{\pntext\f0 14.\tab}Select Combine object and click on Combine button.\line\line MeshFilter with combined mesh and MeshRenderer components will be added to Combine object.\line\line This time we need to save combined mesh in Prefab in order to restore it after Play mode will be turned off. But first we need to export combined mesh into asset so it will be saved in prefab.\line\par
{\pntext\f0 15.\tab}Select Storage_Fragments object and destroy it with all fragments.\line\par
{\pntext\f0 16.\tab}Select Combine object and in Combine component click on Export Mesh button.\line\par
{\pntext\f0 17.\tab}Select Combine object in Hierarchy and drag and drop it in Project window to create prefab.\line\par
{\pntext\f0 18.\tab}Turn off Play Mode.\line\par
{\pntext\f0 19.\tab}Select Combine prefab in Project window and drag and drop it into scene. \line\par

\pard\nowidctlpar\sl276\slmult1 In this way you can use Combine component to combine demolished and simulated fragments into one mesh object.\par
\par
\par
\par
\par
}
 