{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to shoot and Demolish prefragmented Connected Cluster structure\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0, 0, 0] and \b scale \b0 to [10, 1, 10]\line\par
{\pntext\f0 3.\tab}Create \b Cylinder\b0 , this will be a pillar we will prefragment and shoot. \line\par
{\pntext\f0 4.\tab}Set its \b name \b0 to "\i Pillar\i0 ", \b position \b0 to [0, 4.5, 0] and \b scale \b0 to [1, 4, 1]\line\par
{\pntext\f0 5.\tab}Add \b RayFire Shatter \b0 to the Pillar object, in \b Voronoi \b0 properties set \b Amount \b0 to 1\b 00\b0  and click the \b Fragment \b0 button. New object Pillar_root will be created.\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 or \b Deactivate \b0 Pillar object, we won\rquote t need it anymore.\line\par
{\pntext\f0 7.\tab}Add \b RayFire Rigid \b0 to the Pillar_root object and set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 8.\tab}Set \b Object type \b0 to \b Connected Cluster\b0 . In this case there will be only one RigidBody component on the root. \line\par
{\pntext\f0 9.\tab}Set \b Simulation type \b0 to \b Dynamic\b0  and \b Demolition Type \b0 to \b Runtime\b0 .\line\par
{\pntext\f0 10.\tab}Create another \b Cylinder \b0 object, this will be our gun barrel.\line\par

\pard 
{\pntext\f0 11.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\ri-72\sl276\slmult1\tx8236\tx8378 Set its \b name \b0 to "\i Gun\i0 ", \b position \b0 to [4, 5, 0], \b rotation \b0 to [0, 0 ,90] and \b scale \b0 to [0.1, 0.2, 0.1] \line\par

\pard 
{\pntext\f0 12.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Add \b Rayfire Gun \b0 component and set \b Axis \b0 property to \b Y Green\b0 , set \b Impact Radius \b0 to \b 0.5 \b0 and \b disable Show \b0 property. Set \b Impact Strength \b0 property to \b 10.\b0\line\par
{\pntext\f0 13.\tab}Disable \b Demolish Cluster \b0 property.\line\par
{\pntext\f0 14.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click \b0 on \b Single Shot \b0 button. \line\line Pillar \f1\lang1049 w\f0\lang1033 ill be pushed by shot but will not be demolished\lang9 , but it will be demolished at collision with ground.\line\par
{\pntext\f0 15.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 16.\tab}Select Gun and enable Demolish Cluster property.\line\par
{\pntext\f0 17.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click \b0 on \b Single Shot \b0 button. \line\line This time Pillar cluster will be demolished at impact point, all fragments insider Impact Radius range will be detached and will continue simulation with their own RigidBody component, the rest of the fragments will stay in two Connected Clusters at the top and the bottom.\line\par
{\pntext\f0 18.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 19.\tab}\b Select \b0 Pillar_root object and in \b Rigid \b0 component set \b Simulation type \b0 to \b Kinematic\b0 .\line\par
{\pntext\f0 20.\tab}\b Select \b0 Gun object and set its \b position \b0 to [4, \b 3\b0 , 0] so it will be slightly lower than middle of the pillar.\line\par
{\pntext\f0 21.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click \b0 on \b Single Shot \b0 button. \line\line Notice that this time upper group of fragments was turned to Kinematic Connected cluster and froze in the air while bottom cluster was turned to dynamic and can be pushed. \line\line This happens because \b by default \b0 when Cluster demolishes and loose connection among all its fragments, it detaches lesser group into new cluster, so bigger group continue simulation as Kinematic and lesser groups turn to dynamic because it has no any connections with main cluster anymore. This behavior can be changed with Rayfire Unyielding component.\line\par
{\pntext\f0 22.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 23.\tab}\b Select \b0 Pillar_root object and add Rayfire Unyielding component and set its Gizmo Center to [0, -4, 0] so its gizmo will overlap fragments at the bottom of pillar.\line\par
{\pntext\f0 24.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click \b0 on \b Single Shot \b0 button. \line\line This time bottom cluster will be Kinematic and will not be pushed by shot and collision with other fragments while upper cluster will turn to Dynamic and will fall to the ground. \line\line In the same way you can add more Unyielding components to define groups of fragments that should stay kinematic when Connected Cluster will be demolished.\line\par
{\pntext\f0 25.\tab}\b Turn Off \b0 Play Mode. \line\line In previous examples you demolished Connected Cluster with single shot using Demolish Cluster property in Gun component, but in some cases you may need to make several shots with different force, for instance, pillar may need 5 shots from pistol and two shots from machine gun. In this case you should use Damage feature which allows to apply different damage values before object will reach maximum damage it may take and only then it will be demolished.. \line\par
{\pntext\f0 26.\tab}\b Select \b0 Gun object and \b disable \b0 Demolish Cluster property in Gun component and set \b Damage \b0 value to \b 60\b0 .\line\par
{\pntext\f0 27.\tab}\b Select \b0 Pillar\lang1033 _root\lang9  object and enable Damage feature in Damage properties, notice that default \b Max Damage \b0 value is \b 100\b0 .\line\par
{\pntext\f0 28.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click just one time \b0 on \b Single Shot \b0 button. \line\line Notice that nothing will happen after single shot. While in Play mode \b select \b0 Pillar_root object and check \b Current Damage \b0 property, it will be \b 60\b0 , the damage value which was defined in Gun component.\line\par
{\pntext\f0 29.\tab}\b Select \b0 Gun object and \b click \b0 on \b Single Shot \b0 button again.\line\line This time Pillar cluster will be demolished because Current Damage got another 60 damage. \line\line Keep in mind that is it possible to apply damage to object using public method ApplyDamage () in Rigid component.\line\line\line\par

\pard\nowidctlpar\sl276\slmult1\par
\line\par
\par
\b\par
}
 