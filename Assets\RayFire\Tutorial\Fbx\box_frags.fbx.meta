fileFormatVersion: 2
guid: dc30fa8b94e54d641927b33500a567b1
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: //RootNode
    100002: Box_pf_sh_1
    100004: Box_pf_sh_10
    100006: Box_pf_sh_100
    100008: Box_pf_sh_11
    100010: Box_pf_sh_12
    100012: Box_pf_sh_13
    100014: Box_pf_sh_14
    100016: Box_pf_sh_15
    100018: Box_pf_sh_16
    100020: Box_pf_sh_17
    100022: Box_pf_sh_18
    100024: Box_pf_sh_19
    100026: Box_pf_sh_2
    100028: Box_pf_sh_20
    100030: Box_pf_sh_21
    100032: Box_pf_sh_22
    100034: Box_pf_sh_23
    100036: Box_pf_sh_24
    100038: Box_pf_sh_25
    100040: Box_pf_sh_26
    100042: Box_pf_sh_27
    100044: Box_pf_sh_28
    100046: Box_pf_sh_29
    100048: Box_pf_sh_3
    100050: Box_pf_sh_30
    100052: Box_pf_sh_31
    100054: Box_pf_sh_32
    100056: Box_pf_sh_33
    100058: Box_pf_sh_34
    100060: Box_pf_sh_35
    100062: Box_pf_sh_36
    100064: Box_pf_sh_37
    100066: Box_pf_sh_38
    100068: Box_pf_sh_39
    100070: Box_pf_sh_4
    100072: Box_pf_sh_40
    100074: Box_pf_sh_41
    100076: Box_pf_sh_42
    100078: Box_pf_sh_43
    100080: Box_pf_sh_44
    100082: Box_pf_sh_45
    100084: Box_pf_sh_46
    100086: Box_pf_sh_47
    100088: Box_pf_sh_48
    100090: Box_pf_sh_49
    100092: Box_pf_sh_5
    100094: Box_pf_sh_50
    100096: Box_pf_sh_51
    100098: Box_pf_sh_52
    100100: Box_pf_sh_53
    100102: Box_pf_sh_54
    100104: Box_pf_sh_55
    100106: Box_pf_sh_56
    100108: Box_pf_sh_57
    100110: Box_pf_sh_58
    100112: Box_pf_sh_59
    100114: Box_pf_sh_6
    100116: Box_pf_sh_60
    100118: Box_pf_sh_61
    100120: Box_pf_sh_62
    100122: Box_pf_sh_63
    100124: Box_pf_sh_64
    100126: Box_pf_sh_65
    100128: Box_pf_sh_66
    100130: Box_pf_sh_67
    100132: Box_pf_sh_68
    100134: Box_pf_sh_69
    100136: Box_pf_sh_7
    100138: Box_pf_sh_70
    100140: Box_pf_sh_71
    100142: Box_pf_sh_72
    100144: Box_pf_sh_73
    100146: Box_pf_sh_74
    100148: Box_pf_sh_75
    100150: Box_pf_sh_76
    100152: Box_pf_sh_77
    100154: Box_pf_sh_78
    100156: Box_pf_sh_79
    100158: Box_pf_sh_8
    100160: Box_pf_sh_80
    100162: Box_pf_sh_81
    100164: Box_pf_sh_82
    100166: Box_pf_sh_83
    100168: Box_pf_sh_84
    100170: Box_pf_sh_85
    100172: Box_pf_sh_86
    100174: Box_pf_sh_87
    100176: Box_pf_sh_88
    100178: Box_pf_sh_89
    100180: Box_pf_sh_9
    100182: Box_pf_sh_90
    100184: Box_pf_sh_91
    100186: Box_pf_sh_92
    100188: Box_pf_sh_93
    100190: Box_pf_sh_94
    100192: Box_pf_sh_95
    100194: Box_pf_sh_96
    100196: Box_pf_sh_97
    100198: Box_pf_sh_98
    100200: Box_pf_sh_99
    400000: //RootNode
    400002: Box_pf_sh_1
    400004: Box_pf_sh_10
    400006: Box_pf_sh_100
    400008: Box_pf_sh_11
    400010: Box_pf_sh_12
    400012: Box_pf_sh_13
    400014: Box_pf_sh_14
    400016: Box_pf_sh_15
    400018: Box_pf_sh_16
    400020: Box_pf_sh_17
    400022: Box_pf_sh_18
    400024: Box_pf_sh_19
    400026: Box_pf_sh_2
    400028: Box_pf_sh_20
    400030: Box_pf_sh_21
    400032: Box_pf_sh_22
    400034: Box_pf_sh_23
    400036: Box_pf_sh_24
    400038: Box_pf_sh_25
    400040: Box_pf_sh_26
    400042: Box_pf_sh_27
    400044: Box_pf_sh_28
    400046: Box_pf_sh_29
    400048: Box_pf_sh_3
    400050: Box_pf_sh_30
    400052: Box_pf_sh_31
    400054: Box_pf_sh_32
    400056: Box_pf_sh_33
    400058: Box_pf_sh_34
    400060: Box_pf_sh_35
    400062: Box_pf_sh_36
    400064: Box_pf_sh_37
    400066: Box_pf_sh_38
    400068: Box_pf_sh_39
    400070: Box_pf_sh_4
    400072: Box_pf_sh_40
    400074: Box_pf_sh_41
    400076: Box_pf_sh_42
    400078: Box_pf_sh_43
    400080: Box_pf_sh_44
    400082: Box_pf_sh_45
    400084: Box_pf_sh_46
    400086: Box_pf_sh_47
    400088: Box_pf_sh_48
    400090: Box_pf_sh_49
    400092: Box_pf_sh_5
    400094: Box_pf_sh_50
    400096: Box_pf_sh_51
    400098: Box_pf_sh_52
    400100: Box_pf_sh_53
    400102: Box_pf_sh_54
    400104: Box_pf_sh_55
    400106: Box_pf_sh_56
    400108: Box_pf_sh_57
    400110: Box_pf_sh_58
    400112: Box_pf_sh_59
    400114: Box_pf_sh_6
    400116: Box_pf_sh_60
    400118: Box_pf_sh_61
    400120: Box_pf_sh_62
    400122: Box_pf_sh_63
    400124: Box_pf_sh_64
    400126: Box_pf_sh_65
    400128: Box_pf_sh_66
    400130: Box_pf_sh_67
    400132: Box_pf_sh_68
    400134: Box_pf_sh_69
    400136: Box_pf_sh_7
    400138: Box_pf_sh_70
    400140: Box_pf_sh_71
    400142: Box_pf_sh_72
    400144: Box_pf_sh_73
    400146: Box_pf_sh_74
    400148: Box_pf_sh_75
    400150: Box_pf_sh_76
    400152: Box_pf_sh_77
    400154: Box_pf_sh_78
    400156: Box_pf_sh_79
    400158: Box_pf_sh_8
    400160: Box_pf_sh_80
    400162: Box_pf_sh_81
    400164: Box_pf_sh_82
    400166: Box_pf_sh_83
    400168: Box_pf_sh_84
    400170: Box_pf_sh_85
    400172: Box_pf_sh_86
    400174: Box_pf_sh_87
    400176: Box_pf_sh_88
    400178: Box_pf_sh_89
    400180: Box_pf_sh_9
    400182: Box_pf_sh_90
    400184: Box_pf_sh_91
    400186: Box_pf_sh_92
    400188: Box_pf_sh_93
    400190: Box_pf_sh_94
    400192: Box_pf_sh_95
    400194: Box_pf_sh_96
    400196: Box_pf_sh_97
    400198: Box_pf_sh_98
    400200: Box_pf_sh_99
    2100000: concrete_exterior
    2300000: Box_pf_sh_1
    2300002: Box_pf_sh_10
    2300004: Box_pf_sh_100
    2300006: Box_pf_sh_11
    2300008: Box_pf_sh_12
    2300010: Box_pf_sh_13
    2300012: Box_pf_sh_14
    2300014: Box_pf_sh_15
    2300016: Box_pf_sh_16
    2300018: Box_pf_sh_17
    2300020: Box_pf_sh_18
    2300022: Box_pf_sh_19
    2300024: Box_pf_sh_2
    2300026: Box_pf_sh_20
    2300028: Box_pf_sh_21
    2300030: Box_pf_sh_22
    2300032: Box_pf_sh_23
    2300034: Box_pf_sh_24
    2300036: Box_pf_sh_25
    2300038: Box_pf_sh_26
    2300040: Box_pf_sh_27
    2300042: Box_pf_sh_28
    2300044: Box_pf_sh_29
    2300046: Box_pf_sh_3
    2300048: Box_pf_sh_30
    2300050: Box_pf_sh_31
    2300052: Box_pf_sh_32
    2300054: Box_pf_sh_33
    2300056: Box_pf_sh_34
    2300058: Box_pf_sh_35
    2300060: Box_pf_sh_36
    2300062: Box_pf_sh_37
    2300064: Box_pf_sh_38
    2300066: Box_pf_sh_39
    2300068: Box_pf_sh_4
    2300070: Box_pf_sh_40
    2300072: Box_pf_sh_41
    2300074: Box_pf_sh_42
    2300076: Box_pf_sh_43
    2300078: Box_pf_sh_44
    2300080: Box_pf_sh_45
    2300082: Box_pf_sh_46
    2300084: Box_pf_sh_47
    2300086: Box_pf_sh_48
    2300088: Box_pf_sh_49
    2300090: Box_pf_sh_5
    2300092: Box_pf_sh_50
    2300094: Box_pf_sh_51
    2300096: Box_pf_sh_52
    2300098: Box_pf_sh_53
    2300100: Box_pf_sh_54
    2300102: Box_pf_sh_55
    2300104: Box_pf_sh_56
    2300106: Box_pf_sh_57
    2300108: Box_pf_sh_58
    2300110: Box_pf_sh_59
    2300112: Box_pf_sh_6
    2300114: Box_pf_sh_60
    2300116: Box_pf_sh_61
    2300118: Box_pf_sh_62
    2300120: Box_pf_sh_63
    2300122: Box_pf_sh_64
    2300124: Box_pf_sh_65
    2300126: Box_pf_sh_66
    2300128: Box_pf_sh_67
    2300130: Box_pf_sh_68
    2300132: Box_pf_sh_69
    2300134: Box_pf_sh_7
    2300136: Box_pf_sh_70
    2300138: Box_pf_sh_71
    2300140: Box_pf_sh_72
    2300142: Box_pf_sh_73
    2300144: Box_pf_sh_74
    2300146: Box_pf_sh_75
    2300148: Box_pf_sh_76
    2300150: Box_pf_sh_77
    2300152: Box_pf_sh_78
    2300154: Box_pf_sh_79
    2300156: Box_pf_sh_8
    2300158: Box_pf_sh_80
    2300160: Box_pf_sh_81
    2300162: Box_pf_sh_82
    2300164: Box_pf_sh_83
    2300166: Box_pf_sh_84
    2300168: Box_pf_sh_85
    2300170: Box_pf_sh_86
    2300172: Box_pf_sh_87
    2300174: Box_pf_sh_88
    2300176: Box_pf_sh_89
    2300178: Box_pf_sh_9
    2300180: Box_pf_sh_90
    2300182: Box_pf_sh_91
    2300184: Box_pf_sh_92
    2300186: Box_pf_sh_93
    2300188: Box_pf_sh_94
    2300190: Box_pf_sh_95
    2300192: Box_pf_sh_96
    2300194: Box_pf_sh_97
    2300196: Box_pf_sh_98
    2300198: Box_pf_sh_99
    3300000: Box_pf_sh_1
    3300002: Box_pf_sh_10
    3300004: Box_pf_sh_100
    3300006: Box_pf_sh_11
    3300008: Box_pf_sh_12
    3300010: Box_pf_sh_13
    3300012: Box_pf_sh_14
    3300014: Box_pf_sh_15
    3300016: Box_pf_sh_16
    3300018: Box_pf_sh_17
    3300020: Box_pf_sh_18
    3300022: Box_pf_sh_19
    3300024: Box_pf_sh_2
    3300026: Box_pf_sh_20
    3300028: Box_pf_sh_21
    3300030: Box_pf_sh_22
    3300032: Box_pf_sh_23
    3300034: Box_pf_sh_24
    3300036: Box_pf_sh_25
    3300038: Box_pf_sh_26
    3300040: Box_pf_sh_27
    3300042: Box_pf_sh_28
    3300044: Box_pf_sh_29
    3300046: Box_pf_sh_3
    3300048: Box_pf_sh_30
    3300050: Box_pf_sh_31
    3300052: Box_pf_sh_32
    3300054: Box_pf_sh_33
    3300056: Box_pf_sh_34
    3300058: Box_pf_sh_35
    3300060: Box_pf_sh_36
    3300062: Box_pf_sh_37
    3300064: Box_pf_sh_38
    3300066: Box_pf_sh_39
    3300068: Box_pf_sh_4
    3300070: Box_pf_sh_40
    3300072: Box_pf_sh_41
    3300074: Box_pf_sh_42
    3300076: Box_pf_sh_43
    3300078: Box_pf_sh_44
    3300080: Box_pf_sh_45
    3300082: Box_pf_sh_46
    3300084: Box_pf_sh_47
    3300086: Box_pf_sh_48
    3300088: Box_pf_sh_49
    3300090: Box_pf_sh_5
    3300092: Box_pf_sh_50
    3300094: Box_pf_sh_51
    3300096: Box_pf_sh_52
    3300098: Box_pf_sh_53
    3300100: Box_pf_sh_54
    3300102: Box_pf_sh_55
    3300104: Box_pf_sh_56
    3300106: Box_pf_sh_57
    3300108: Box_pf_sh_58
    3300110: Box_pf_sh_59
    3300112: Box_pf_sh_6
    3300114: Box_pf_sh_60
    3300116: Box_pf_sh_61
    3300118: Box_pf_sh_62
    3300120: Box_pf_sh_63
    3300122: Box_pf_sh_64
    3300124: Box_pf_sh_65
    3300126: Box_pf_sh_66
    3300128: Box_pf_sh_67
    3300130: Box_pf_sh_68
    3300132: Box_pf_sh_69
    3300134: Box_pf_sh_7
    3300136: Box_pf_sh_70
    3300138: Box_pf_sh_71
    3300140: Box_pf_sh_72
    3300142: Box_pf_sh_73
    3300144: Box_pf_sh_74
    3300146: Box_pf_sh_75
    3300148: Box_pf_sh_76
    3300150: Box_pf_sh_77
    3300152: Box_pf_sh_78
    3300154: Box_pf_sh_79
    3300156: Box_pf_sh_8
    3300158: Box_pf_sh_80
    3300160: Box_pf_sh_81
    3300162: Box_pf_sh_82
    3300164: Box_pf_sh_83
    3300166: Box_pf_sh_84
    3300168: Box_pf_sh_85
    3300170: Box_pf_sh_86
    3300172: Box_pf_sh_87
    3300174: Box_pf_sh_88
    3300176: Box_pf_sh_89
    3300178: Box_pf_sh_9
    3300180: Box_pf_sh_90
    3300182: Box_pf_sh_91
    3300184: Box_pf_sh_92
    3300186: Box_pf_sh_93
    3300188: Box_pf_sh_94
    3300190: Box_pf_sh_95
    3300192: Box_pf_sh_96
    3300194: Box_pf_sh_97
    3300196: Box_pf_sh_98
    3300198: Box_pf_sh_99
    4300000: Box_pf_sh_1
    4300002: Box_pf_sh_2
    4300004: Box_pf_sh_3
    4300006: Box_pf_sh_4
    4300008: Box_pf_sh_5
    4300010: Box_pf_sh_6
    4300012: Box_pf_sh_7
    4300014: Box_pf_sh_8
    4300016: Box_pf_sh_9
    4300018: Box_pf_sh_10
    4300020: Box_pf_sh_11
    4300022: Box_pf_sh_12
    4300024: Box_pf_sh_13
    4300026: Box_pf_sh_14
    4300028: Box_pf_sh_15
    4300030: Box_pf_sh_16
    4300032: Box_pf_sh_17
    4300034: Box_pf_sh_18
    4300036: Box_pf_sh_19
    4300038: Box_pf_sh_20
    4300040: Box_pf_sh_21
    4300042: Box_pf_sh_22
    4300044: Box_pf_sh_23
    4300046: Box_pf_sh_24
    4300048: Box_pf_sh_25
    4300050: Box_pf_sh_26
    4300052: Box_pf_sh_27
    4300054: Box_pf_sh_28
    4300056: Box_pf_sh_29
    4300058: Box_pf_sh_30
    4300060: Box_pf_sh_31
    4300062: Box_pf_sh_32
    4300064: Box_pf_sh_33
    4300066: Box_pf_sh_34
    4300068: Box_pf_sh_35
    4300070: Box_pf_sh_36
    4300072: Box_pf_sh_37
    4300074: Box_pf_sh_38
    4300076: Box_pf_sh_39
    4300078: Box_pf_sh_40
    4300080: Box_pf_sh_41
    4300082: Box_pf_sh_42
    4300084: Box_pf_sh_43
    4300086: Box_pf_sh_44
    4300088: Box_pf_sh_45
    4300090: Box_pf_sh_46
    4300092: Box_pf_sh_47
    4300094: Box_pf_sh_48
    4300096: Box_pf_sh_49
    4300098: Box_pf_sh_50
    4300100: Box_pf_sh_51
    4300102: Box_pf_sh_52
    4300104: Box_pf_sh_53
    4300106: Box_pf_sh_54
    4300108: Box_pf_sh_55
    4300110: Box_pf_sh_56
    4300112: Box_pf_sh_57
    4300114: Box_pf_sh_58
    4300116: Box_pf_sh_59
    4300118: Box_pf_sh_60
    4300120: Box_pf_sh_61
    4300122: Box_pf_sh_62
    4300124: Box_pf_sh_63
    4300126: Box_pf_sh_64
    4300128: Box_pf_sh_65
    4300130: Box_pf_sh_66
    4300132: Box_pf_sh_67
    4300134: Box_pf_sh_68
    4300136: Box_pf_sh_69
    4300138: Box_pf_sh_70
    4300140: Box_pf_sh_71
    4300142: Box_pf_sh_72
    4300144: Box_pf_sh_73
    4300146: Box_pf_sh_74
    4300148: Box_pf_sh_75
    4300150: Box_pf_sh_76
    4300152: Box_pf_sh_77
    4300154: Box_pf_sh_78
    4300156: Box_pf_sh_79
    4300158: Box_pf_sh_80
    4300160: Box_pf_sh_81
    4300162: Box_pf_sh_82
    4300164: Box_pf_sh_83
    4300166: Box_pf_sh_84
    4300168: Box_pf_sh_85
    4300170: Box_pf_sh_86
    4300172: Box_pf_sh_87
    4300174: Box_pf_sh_88
    4300176: Box_pf_sh_89
    4300178: Box_pf_sh_90
    4300180: Box_pf_sh_91
    4300182: Box_pf_sh_92
    4300184: Box_pf_sh_93
    4300186: Box_pf_sh_94
    4300188: Box_pf_sh_95
    4300190: Box_pf_sh_96
    4300192: Box_pf_sh_97
    4300194: Box_pf_sh_98
    4300196: Box_pf_sh_99
    4300198: Box_pf_sh_100
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: concrete_exterior
    second: {fileID: 2100000, guid: aea6a14ad54e0b34191abbc84ef4ea7d, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
