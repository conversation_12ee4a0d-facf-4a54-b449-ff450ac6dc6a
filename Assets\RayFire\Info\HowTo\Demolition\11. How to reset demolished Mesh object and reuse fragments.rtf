{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to reset demolished object and reuse fragments.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create a Cube, this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its name to "<PERSON>", position to [0,7,0], rotation to [30, 0, 50] and scale to [2,1,1]\line\par
{\pntext\f0 3.\tab}Remove it's Box Collider because the Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "Ground", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 6.\tab}Add RayFire Rigid component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid Initialization to At Start and Demolition type to \f1\lang1049 R\f0\lang1033 untime\lang9 .\line\par
{\pntext\f0 8.\tab}Start Play Mode. \line\line Brick will fall down and will be demolished by collision.\line As you can see in Hierarchy original Brick object was deactivated at demolition and replaced by fragments, then after a second it was destroyed. This is default behavior which can be changed in Rigid Reset properties.\line\par
{\pntext\f0 9.\tab}Turn Off Play Mode.\line\par
{\pntext\f0 10.\tab}Select Brick and in Reset properties set Demolition Action to Deactivate To Reset.\line\par
{\pntext\f0 11.\tab}Start Play Mode. \line\line Brick will fall down and will be demolished by collision. This time Brick will be demolished and deactivated, but it will stay in scene.\line\par
{\pntext\f0 12.\tab}Select Brick\f1\lang1049  \f0\lang1033 in Hierarchy\lang9  and click on Reset Rigid button on top. The same can be done by using public \lang1033 ResetRigid() method.\lang9\line\line All fragments will be destroyed and Brick will be activated at it's initialization position. It will fall down and will be demolished again. \line Now let's increase amount of fragments to get some noticeable fps drop.\line\par
{\pntext\f0 13.\tab}Turn Off Play Mode.\line\fs24\lang1033\par
{\pntext\f0 14.\tab}\fs22\lang9 Select Brick and in Mesh demolition set Amount to 150.\fs24\lang1033\line\par
{\pntext\f0 15.\tab}\fs22\lang9 Start Play Mode. \line\line Brick will fall down and will be demolished with slight lag. \line\par
{\pntext\f0 16.\tab}Select Brick\f1\lang1049  \f0\lang1033 in Hierarchy\lang9  and click on Reset Rigid button.\line\line Brick will start to fall down again but this time there won't be any lag at demolition because Mesh Reuse in enabled in Reset properties by default. Since all fragments meshes were generated at first demolition there is no reason to spend resources to do this again. Rigid will only need to create gameobjects with all necessary components again. \line But we can reuse fragments as well.\line\par
{\pntext\f0 17.\tab}Turn Off Play Mode.\line\par
{\pntext\f0 18.\tab}Select Brick and in Rigid Reset properties change Reuse Fragments from Destroy to Reuse.\fs24\lang1033\line\fs22\lang9\par
{\pntext\f0 19.\tab}In Mesh Demolition set Amount to 300.\line\par
{\pntext\f0 20.\tab}Start Play Mode. \line\line This time lag time will be longer.\line\par
{\pntext\f0 21.\tab}Select Brick\f1\lang1049  \f0\lang1033 in Hierarchy\lang9  and click on Reset Rigid button.\line\line When Brick will be demolished again even thought there are 300 fragments there won't be any lag and demolition will be smooth because fragments from first demolition will be reused.\par

\pard\nowidctlpar\sl276\slmult1\par
\tab In order to avoid such lag at first demolition you can use Awake Precache or Awake \tab Prefragment demolition types to generate all meshes or fragments in Awake.\par
\par
\tab Also you can avoid first demolition lag by using Runtime Caching feature which \tab allows to spread fragment generation over multiple frames.\par
\par
\line\fs24\lang1033\par
}
 