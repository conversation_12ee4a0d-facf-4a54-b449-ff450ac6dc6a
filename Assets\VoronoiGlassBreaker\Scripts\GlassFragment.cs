using UnityEngine;

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Represents a single glass fragment with physics and lifecycle management
    /// </summary>
    // [RequireComponent(typeof(MeshRenderer), typeof(MeshFilter), typeof(Rigidbody), typeof(MeshCollider))]
    public class GlassFragment : MonoBehaviour
    {
        [Header("Fragment Settings")]
        public float lifetime = 10f;
        public float fadeOutDuration = 2f;
        public bool useGravity = true;
        public float mass = 0.1f;
        public float drag = 0.5f;
        public float angularDrag = 0.5f;
        
        [Header("Physics")]
        public PhysicMaterial physicsMaterial;
        public float bounceForce = 0.3f;
        public float breakThreshold = 5f;
        
        [Header("Visual")]
        public AnimationCurve fadeOutCurve = AnimationCurve.EaseInOut(0f, 1f, 1f, 0f);
        
        private MeshRenderer meshRenderer;
        private MeshFilter meshFilter;
        private Rigidbody rb;
        private MeshCollider meshCollider;
        private Material fragmentMaterial;
        private Color originalColor;
        private float creationTime;
        private bool isFading = false;
        private VoronoiCell sourceCell;
        
        // Events
        public System.Action<GlassFragment> OnFragmentDestroyed;
        
        public VoronoiCell SourceCell => sourceCell;
        public bool IsFading => isFading;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            creationTime = Time.time;
            SetupPhysics();
        }
        
        private void Update()
        {
            UpdateLifetime();
        }
        
        /// <summary>
        /// Initialize fragment with mesh and source cell data
        /// </summary>
        public void Initialize(Mesh fragmentMesh, Material material, VoronoiCell cell, Vector3 impactPoint, float impactForce)
        {
            sourceCell = cell;
            
            // Set mesh
            meshFilter.mesh = fragmentMesh;
            meshCollider.sharedMesh = fragmentMesh;
            
            // Set material
            fragmentMaterial = new Material(material);
            originalColor = fragmentMaterial.color;
            meshRenderer.material = fragmentMaterial;
            
            // Apply impact force
            ApplyImpactForce(impactPoint, impactForce);
        }
        
        private void InitializeComponents()
        {
            // Get or add required components
            meshRenderer = GetComponent<MeshRenderer>();
            if (meshRenderer == null)
                meshRenderer = gameObject.AddComponent<MeshRenderer>();

            meshFilter = GetComponent<MeshFilter>();
            if (meshFilter == null)
                meshFilter = gameObject.AddComponent<MeshFilter>();

            rb = GetComponent<Rigidbody>();
            if (rb == null)
                rb = gameObject.AddComponent<Rigidbody>();

            meshCollider = GetComponent<MeshCollider>();
            if (meshCollider == null)
                meshCollider = gameObject.AddComponent<MeshCollider>();

            // Ensure mesh collider is convex for physics
            meshCollider.convex = true;

            if (physicsMaterial != null)
            {
                meshCollider.material = physicsMaterial;
            }
        }
        
        private void SetupPhysics()
        {
            rb.mass = mass;
            rb.drag = drag;
            rb.angularDrag = angularDrag;
            rb.useGravity = useGravity;
            
            // Add random rotation for more realistic movement
            rb.angularVelocity = Random.insideUnitSphere * 2f;
        }
        
        private void ApplyImpactForce(Vector3 impactPoint, float impactForce)
        {
            Vector3 forceDirection = (transform.position - impactPoint).normalized;
            if (forceDirection.magnitude < 0.1f)
            {
                forceDirection = Random.onUnitSphere;
            }
            
            // Apply force with some randomness
            Vector3 force = forceDirection * impactForce * Random.Range(0.5f, 1.5f);
            Vector3 torque = Random.insideUnitSphere * impactForce * 0.5f;
            
            rb.AddForce(force, ForceMode.Impulse);
            rb.AddTorque(torque, ForceMode.Impulse);
        }
        
        private void UpdateLifetime()
        {
            float age = Time.time - creationTime;
            
            if (age >= lifetime && !isFading)
            {
                StartFadeOut();
            }
            
            if (isFading)
            {
                UpdateFadeOut();
            }
        }
        
        private void StartFadeOut()
        {
            isFading = true;
        }
        
        private void UpdateFadeOut()
        {
            float age = Time.time - creationTime;
            float fadeProgress = (age - lifetime) / fadeOutDuration;
            
            if (fadeProgress >= 1f)
            {
                DestroyFragment();
                return;
            }
            
            // Update material alpha
            float alpha = fadeOutCurve.Evaluate(1f - fadeProgress);
            Color color = originalColor;
            color.a = alpha;
            fragmentMaterial.color = color;
            
            // Optionally scale down the fragment
            float scale = Mathf.Lerp(1f, 0.8f, fadeProgress);
            transform.localScale = Vector3.one * scale;
        }
        
        private void OnCollisionEnter(Collision collision)
        {
            // Check if impact is strong enough to break further
            float impactMagnitude = collision.relativeVelocity.magnitude;
            
            if (impactMagnitude > breakThreshold)
            {
                // Could implement further fragmentation here
                CreateImpactEffect(collision.contacts[0].point);
            }
            
            // Add bounce effect
            if (collision.rigidbody != null)
            {
                Vector3 bounceDirection = Vector3.Reflect(rb.velocity.normalized, collision.contacts[0].normal);
                rb.AddForce(bounceDirection * bounceForce, ForceMode.Impulse);
            }
        }
        
        private void CreateImpactEffect(Vector3 impactPoint)
        {
            // Create small particle effect or sound
            // This could be expanded to create smaller fragments
        }
        
        /// <summary>
        /// Force destroy this fragment
        /// </summary>
        public void DestroyFragment()
        {
            OnFragmentDestroyed?.Invoke(this);
            
            // Clean up material
            if (fragmentMaterial != null)
            {
                DestroyImmediate(fragmentMaterial);
            }
            
            Destroy(gameObject);
        }
        
        /// <summary>
        /// Set the lifetime of this fragment
        /// </summary>
        public void SetLifetime(float newLifetime)
        {
            lifetime = newLifetime;
        }
        
        /// <summary>
        /// Add additional force to this fragment
        /// </summary>
        public void AddForce(Vector3 force, ForceMode mode = ForceMode.Force)
        {
            rb.AddForce(force, mode);
        }
        
        /// <summary>
        /// Add additional torque to this fragment
        /// </summary>
        public void AddTorque(Vector3 torque, ForceMode mode = ForceMode.Force)
        {
            rb.AddTorque(torque, mode);
        }
        
        /// <summary>
        /// Get the current velocity of this fragment
        /// </summary>
        public Vector3 GetVelocity()
        {
            return rb.velocity;
        }
        
        /// <summary>
        /// Get the current angular velocity of this fragment
        /// </summary>
        public Vector3 GetAngularVelocity()
        {
            return rb.angularVelocity;
        }
        
        /// <summary>
        /// Check if this fragment is moving
        /// </summary>
        public bool IsMoving(float threshold = 0.1f)
        {
            return rb.velocity.magnitude > threshold || rb.angularVelocity.magnitude > threshold;
        }
        
        /// <summary>
        /// Stop the fragment's movement
        /// </summary>
        public void Stop()
        {
            rb.velocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }
        
        private void OnDestroy()
        {
            // Clean up material if it still exists
            if (fragmentMaterial != null)
            {
                DestroyImmediate(fragmentMaterial);
            }
        }
    }
}
