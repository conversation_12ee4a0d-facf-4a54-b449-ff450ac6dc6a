{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\ul\b\f0\fs52\lang9 How to slice Unyielding Mesh object\ulnone\b0\fs22\par
\par
Sometimes you may need to slice object which is logically connected to something else, like wall or ceiling. In this sliced part that is not connected should be Dynamic and fall to the ground, while second half shouls stay intact. To do this you should use Unyielding component.\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , set its \b name \b0 to "\i Ceiling\i0 ", \b position \b0 to [0,15,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 2.\tab}Create \b Cylinder\b0 , set its \b position \b0 to [0,9.5,0] and \b scale \b0 to [1,5,1]\line\par
{\pntext\f0 3.\tab}\b Destroy \b0 it\rquote s default Capsule collider.\line\par
{\pntext\f0 4.\tab}Create \b Quad\b0 , this will be our slicing object, set its \b name \b0 to "\i Blade\i0 ", \b position \b0 to [0,10,2], \b rotation \b0 to [90,0,0] and \b scale \b0 to [6,1,1]\line\par
{\pntext\f0 5.\tab}\b Destroy \b0 it\rquote s default Mesh collider.\line\par
{\pntext\f0 6.\tab}Add \b RayFire Blade \b0 component to the Blade object.\line\par
{\pntext\f0 7.\tab}Set \b Action \b0 to \b Slice\b0 , \b On Trigger \b0 property to \b Exit\b0  and \b Slice \lang1033 Plane\lang9  \b0 property to XY.\line\par
{\pntext\f0 8.\tab}Add \b RayFire Rigid \b0 component to \b Cylinder\b0 , set \b Initialization \b0 to \b At Start\b0 , \b Simulation Type \b0 to \b Kinematik \b0 and \b Demolition Type \b0 to \b Runtime\b0 .\line\par
{\pntext\f0 9.\tab}In \b Limitations \b0 properties enable \b Slice By Blade\b0 , otherwise it won\rquote t be possible to slice this object.\line\par
{\pntext\f0 10.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 11.\tab}Enable \b Move Tool\b0 , \b select \b0 Blade object and set \b Tool Handle \b0 in \b Local mode\b0 .\line\par
{\pntext\f0 12.\tab}\b Move \b0 the Blade object by it\rquote s \b Green axis \b0 toward Icicle and pass it through.\line\line Cylinder will be sliced. Both parts will be Dynamic and start to fall down.\line This happens because sliced fragments get \b Fragment Sim Type \b0 property default value which is \b Dynamic \b0 and can be changed in \b Mesh Demolition \b0 properties.\line But if yhis property will be set to Kinematik then both parts will be Kinematik and will not fall down.\line\par
{\pntext\f0 13.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 14.\tab}\b Select Cylinder \b0 object and add \b Rayfire Unyielding \b0 component. \line\par
{\pntext\f0 15.\tab}Set \b Size \b0 to [1,0.2,1] and \b Center \b0 to [0,1,0] so Unyielding gizmo will overlapp upper part of Icicle.\line\par
{\pntext\f0 16.\tab}In Unyielding component set \b Simulation Type \b0 to \b Kinematik\b0\line\par
{\pntext\f0 17.\tab}\b Start \b0 Play Mode, and \b move \b0 Blade object through Pillar again. \line\line Cylinder will be sliced and the bottom piece will start to fall down, while upper part will stay connected to ceiling.\par

\pard\nowidctlpar\sl276\slmult1\par
}
 