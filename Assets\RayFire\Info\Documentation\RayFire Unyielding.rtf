{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fnil\fcharset2 Symbol;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 RayFire Unyielding\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
\fs22\lang9  \fs24\lang1033\par
\fs22\lang9 RayFire Unyielding  should be used with Inactive/Kinematic Connected Clusters and Connectivity structure. When you demolish Connected Cluster or activate Fragments in Connectivity structure you need to have at lest one Unyielding Fragment so other not yet activated Fragments will be able to check if they are connected through other Fragments with at least one Unyielding Fragments and if they do not then they will be activated as well.\par
\par
Connectivity component should be used on root with all Rigid fragments as it's children, so it is possible to det manually each Rigid Unyielding and Activatable property, although it will be pretty hard to select them every time to change these properties in case you want to increase or decrease amount of Unyielding fragments. For Connected Cluster you can not do this on per Shard(Fragment) level because Connected Cluster Shards has no any components.\par
\par
This is where you need to use Unyielding component, it allows to define Unyielding group of Fragments or Shards by it's gizmo which can be easily repositioned or reshaped. In this wat You can quickly try different setups to find out which fragments should be unyielding to provide best simulation result.\par
\par
Unyielding component should be added only to object with Rigid componenet ith Conneceted Cluster object type or to object with Connectivity component, only these two components will initiate it's gizmo overlapping.\par
\b\fs48\par
\tab Properties\b0\fs24\lang1033\par
\par
\b\fs22\lang9\par
Unyielding\b0 : \lang1033 Set property for overlapped fragments. If this property Disabled then componenet will disable Unyielding property for all overlapped fragments. In this way you can Enable Unyielding prioperty for all Rigid objects and use Unyielding component to define group of fragments with Disabled Unyielding property.\fs24\par
\b\fs22\lang9\par
Activatable\b0 : By Default it is impossible to activate Unyielding fragments unless you won't Enable Activatable property in Activation properties. \lang1033 If this property Disabled then componenet will disable Activatable property for all overlapped fragments. In this way you can Enable Activatable prioperty for all Rigid objects and use Unyielding component to define group of fragments with Disabled Activatable property.\par
\par
\b\lang9 Simulation type\b0 : \lang1033 Allows to set specific Simulation type for overlapped objects. Useful when used with Mesh Root Rigid or Rigid Root components.\fs24\par

\pard\nowidctlpar\li720\sl276\slmult1\tx720\f1\'b7\tab\b\f0\fs22\lang9 Originnal\b0 : \lang1033 Do not change already defined simulation type.\fs24\par
\f1\'b7\tab\b\f0\fs22\lang9 Inactive\b0 : \lang1033 Set Inactive simulation type for overlapped objects.\par
\f1\fs24\'b7\tab\b\f0\fs22\lang9 Kinematic\b0 : \lang1033 Set Kinematic simulation type for overlapped objects.\par

\pard\nowidctlpar\sl276\slmult1\b\lang9\par
\par
\fs48\tab Gizmo\b0\fs24\lang1033\par
\b\fs22\lang9\par
\par
Show Gizmo\b0 : \lang1033 Show Box gizmo in viewport.\fs24\par
\par
\b\fs22\lang9 Show Center\b0 : \lang1033 Show gizmo's center with Move Tool. It is better to set Hand tool to move gizmo so you won't move object accidentally.\fs24\par
\par
\b\fs22\lang9 Reset\b0 : \lang1033 Reset gizmo position to it's default position..\fs24\par
\par
}
 