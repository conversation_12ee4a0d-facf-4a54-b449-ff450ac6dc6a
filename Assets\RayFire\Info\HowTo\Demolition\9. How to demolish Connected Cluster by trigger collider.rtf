{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to initiate Connected Cluster demolition by trigger collider\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create a \b Cube\b0 , set its \b name \b0 to "\i Pillar\i0 ", \b position \b0 to [0,3,0] and \b scale \b0 to [1, 5, 1]\line\par
{\pntext\f0 2.\tab}Create another \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 3.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [10,1,10]\line\par
{\pntext\f0 4.\tab}Add \b RayFire Shatter \b0 component to \b Pillar\b0 .\line\par
{\pntext\f0 5.\tab}Set Voronoi \b Amount \b0 property to \b 200 \b0 and click on \b Fragment \b0 button.\line\par
{\pntext\f0 6.\tab}\lang1033 Destroy \b Pillar \b0 object.\lang9\line\par
{\pntext\f0 7.\tab}Select \b Pillar_root \b0 object and add \b RayFire Rigid \b0 component.\line\par
{\pntext\f0 8.\tab}Set \b Initialization \b0 to \b At Start\b0 , \b Object Type \b0 to \b Connected Cluster \b0 and \b Demolition Type \b0 to \b Runtime\b0 .\line\par
{\pntext\f0 9.\tab}In \b Limitations \b0 properties disable \b By Collision \b0 property, in this case Connected Cluster and all its children Connected Clusters created during demolition will not be demolished by collision with ground and each other.\line\par
{\pntext\f0 10.\tab}Enable \b Show Connection \b0 toggle button on top of Rigid component to see Connected Cluster connections and connections its children Connected Clusters when it will be demolished.\line\par
{\pntext\f0 11.\tab}Create new empty \b gameobject\b0 , set is \b name \b0 to "\i Activator\i0 " and \b position \b0 to [2,3,0] \line\par
{\pntext\f0 12.\tab}\lang1033 Add \b RayFire Activator \b0 component to Activator object.\lang9\line\fs24\lang1033\par
{\pntext\f0 13.\tab}\fs22\lang9 Set \b Gizmo Radius \b0 property to \b 1\b0 .\line\par
{\pntext\f0 14.\tab}Enable \b Demolish Cluster \b0 checkbox. If this property will be disabled then activator will affect whole Connected Cluster, but not its Shards and Connected Cluster will not be demolished.\line\par
{\pntext\f0 15.\tab}Enable \b Show Animation \b0 checkbox and click on \b Add \b0 button to add first position in Position List. \line\par
{\pntext\f0 16.\tab}\b Move \b0 Activator to \b position \b0 [-5, 3,0] and click on \b Add \b0 button again. Now you can move Activator object to any other place or leave it there.\line\par
{\pntext\f0 17.\tab}\b Start \b0 Play Mode.\line\par
{\pntext\f0 18.\tab}Select \b Activator \b0 object and click on \b Playback Start \b0 button at the bottom. The same function can be initiated using public method \b TriggerAnimation()\b0\line\line Activator object will start to move from its first defined position to second. While it will move through Pillar cluster you will see how it breaks blue connections and separates Pillar cluster to two smaller Connected Clusters. \par

\pard\nowidctlpar\sl276\slmult1\tab\par
\tab Keep in mind that you don't have to use built-in Activator animation properties, this \tab is just a way to quickly create animation. In most of the cases, you can add Activator \tab to object driven by your game character or by your code.\par
\par
\line\line\par
\line\fs24\lang1033\par
}
 