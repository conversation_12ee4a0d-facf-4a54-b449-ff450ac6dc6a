%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 2f2c9f7dc906b8c4ab770109fbbd936b,
    type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &203136134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 203136135}
  - component: {fileID: 203136138}
  - component: {fileID: 203136137}
  - component: {fileID: 203136136}
  m_Layer: 0
  m_Name: Radial_Rad2_Ring5_Ray10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &203136135
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203136134}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.299999, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &203136136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203136134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 5
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 10
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 1.9698725, y: 2, z: -3.75}
    - {x: 2.8358977, y: 2, z: -3.75}
    - {x: 3.7019231, y: 2, z: -3.75}
    - {x: 4.5679483, y: 2, z: -3.75}
    - {x: 5.433974, y: 2, z: -3.75}
    - {x: 6.299999, y: 2, z: -3.75}
    - {x: 7.1660247, y: 2, z: -3.75}
    - {x: 8.03205, y: 2, z: -3.75}
    - {x: 8.898075, y: 2, z: -3.75}
    - {x: 9.764101, y: 2, z: -3.75}
    - {x: 2.4028852, y: 2, z: -3}
    - {x: 3.2689104, y: 2, z: -3}
    - {x: 4.134936, y: 2, z: -3}
    - {x: 5.0009613, y: 2, z: -3}
    - {x: 5.8669868, y: 2, z: -3}
    - {x: 6.7330117, y: 2, z: -3}
    - {x: 7.599037, y: 2, z: -3}
    - {x: 8.465063, y: 2, z: -3}
    - {x: 9.331088, y: 2, z: -3}
    - {x: 10.197113, y: 2, z: -3}
    - {x: 1.9698725, y: 2, z: -2.25}
    - {x: 2.8358977, y: 2, z: -2.25}
    - {x: 3.7019231, y: 2, z: -2.25}
    - {x: 4.5679483, y: 2, z: -2.25}
    - {x: 5.433974, y: 2, z: -2.25}
    - {x: 6.299999, y: 2, z: -2.25}
    - {x: 7.1660247, y: 2, z: -2.25}
    - {x: 8.03205, y: 2, z: -2.25}
    - {x: 8.898075, y: 2, z: -2.25}
    - {x: 9.764101, y: 2, z: -2.25}
    - {x: 2.4028852, y: 2, z: -1.5}
    - {x: 3.2689104, y: 2, z: -1.5}
    - {x: 4.134936, y: 2, z: -1.5}
    - {x: 5.0009613, y: 2, z: -1.5}
    - {x: 5.8669868, y: 2, z: -1.5}
    - {x: 6.7330117, y: 2, z: -1.5}
    - {x: 7.599037, y: 2, z: -1.5}
    - {x: 8.465063, y: 2, z: -1.5}
    - {x: 9.331088, y: 2, z: -1.5}
    - {x: 10.197113, y: 2, z: -1.5}
    - {x: 1.9698725, y: 2, z: -0.75}
    - {x: 2.8358977, y: 2, z: -0.75}
    - {x: 3.7019231, y: 2, z: -0.75}
    - {x: 4.5679483, y: 2, z: -0.75}
    - {x: 5.433974, y: 2, z: -0.75}
    - {x: 6.299999, y: 2, z: -0.75}
    - {x: 7.1660247, y: 2, z: -0.75}
    - {x: 8.03205, y: 2, z: -0.75}
    - {x: 8.898075, y: 2, z: -0.75}
    - {x: 9.764101, y: 2, z: -0.75}
    - {x: 2.4028852, y: 2, z: 0}
    - {x: 3.2689104, y: 2, z: 0}
    - {x: 4.134936, y: 2, z: 0}
    - {x: 5.0009613, y: 2, z: 0}
    - {x: 5.8669868, y: 2, z: 0}
    - {x: 6.7330117, y: 2, z: 0}
    - {x: 7.599037, y: 2, z: 0}
    - {x: 8.465063, y: 2, z: 0}
    - {x: 9.331088, y: 2, z: 0}
    - {x: 10.197113, y: 2, z: 0}
    - {x: 1.9698725, y: 2, z: 0.75}
    - {x: 2.8358977, y: 2, z: 0.75}
    - {x: 3.7019231, y: 2, z: 0.75}
    - {x: 4.5679483, y: 2, z: 0.75}
    - {x: 5.433974, y: 2, z: 0.75}
    - {x: 6.299999, y: 2, z: 0.75}
    - {x: 7.1660247, y: 2, z: 0.75}
    - {x: 8.03205, y: 2, z: 0.75}
    - {x: 8.898075, y: 2, z: 0.75}
    - {x: 9.764101, y: 2, z: 0.75}
    - {x: 2.4028852, y: 2, z: 1.5}
    - {x: 3.2689104, y: 2, z: 1.5}
    - {x: 4.134936, y: 2, z: 1.5}
    - {x: 5.0009613, y: 2, z: 1.5}
    - {x: 5.8669868, y: 2, z: 1.5}
    - {x: 6.7330117, y: 2, z: 1.5}
    - {x: 7.599037, y: 2, z: 1.5}
    - {x: 8.465063, y: 2, z: 1.5}
    - {x: 9.331088, y: 2, z: 1.5}
    - {x: 10.197113, y: 2, z: 1.5}
    - {x: 1.9698725, y: 2, z: 2.25}
    - {x: 2.8358977, y: 2, z: 2.25}
    - {x: 3.7019231, y: 2, z: 2.25}
    - {x: 4.5679483, y: 2, z: 2.25}
    - {x: 5.433974, y: 2, z: 2.25}
    - {x: 6.299999, y: 2, z: 2.25}
    - {x: 7.1660247, y: 2, z: 2.25}
    - {x: 8.03205, y: 2, z: 2.25}
    - {x: 8.898075, y: 2, z: 2.25}
    - {x: 9.764101, y: 2, z: 2.25}
    - {x: 2.4028852, y: 2, z: 3}
    - {x: 3.2689104, y: 2, z: 3}
    - {x: 4.134936, y: 2, z: 3}
    - {x: 5.0009613, y: 2, z: 3}
    - {x: 5.8669868, y: 2, z: 3}
    - {x: 6.7330117, y: 2, z: 3}
    - {x: 7.599037, y: 2, z: 3}
    - {x: 8.465063, y: 2, z: 3}
    - {x: 9.331088, y: 2, z: 3}
    - {x: 10.197113, y: 2, z: 3}
    pcBndIn:
    - {x: 8.465063, y: 2, z: 0}
    - {x: 7.599037, y: 2, z: 0}
    - {x: 6.7330117, y: 2, z: 0}
    - {x: 5.8669868, y: 2, z: 0}
    - {x: 5.0009613, y: 2, z: 0}
    - {x: 4.134936, y: 2, z: 0}
    pcBndOut:
    - {x: 10.197113, y: 2, z: 3}
    - {x: 9.331088, y: 2, z: 3}
    - {x: 8.465063, y: 2, z: 3}
    - {x: 7.599037, y: 2, z: 3}
    - {x: 6.7330117, y: 2, z: 3}
    - {x: 5.8669868, y: 2, z: 3}
    - {x: 5.0009613, y: 2, z: 3}
    - {x: 4.134936, y: 2, z: 3}
    - {x: 3.2689104, y: 2, z: 3}
    - {x: 2.4028852, y: 2, z: 3}
    - {x: 9.764101, y: 2, z: 2.25}
    - {x: 8.898075, y: 2, z: 2.25}
    - {x: 8.03205, y: 2, z: 2.25}
    - {x: 7.1660247, y: 2, z: 2.25}
    - {x: 6.299999, y: 2, z: 2.25}
    - {x: 5.433974, y: 2, z: 2.25}
    - {x: 4.5679483, y: 2, z: 2.25}
    - {x: 3.7019231, y: 2, z: 2.25}
    - {x: 2.8358977, y: 2, z: 2.25}
    - {x: 1.9698725, y: 2, z: 2.25}
    - {x: 10.197113, y: 2, z: 1.5}
    - {x: 9.331088, y: 2, z: 1.5}
    - {x: 8.465063, y: 2, z: 1.5}
    - {x: 7.599037, y: 2, z: 1.5}
    - {x: 6.7330117, y: 2, z: 1.5}
    - {x: 5.8669868, y: 2, z: 1.5}
    - {x: 5.0009613, y: 2, z: 1.5}
    - {x: 4.134936, y: 2, z: 1.5}
    - {x: 3.2689104, y: 2, z: 1.5}
    - {x: 2.4028852, y: 2, z: 1.5}
    - {x: 9.764101, y: 2, z: 0.75}
    - {x: 8.898075, y: 2, z: 0.75}
    - {x: 8.03205, y: 2, z: 0.75}
    - {x: 7.1660247, y: 2, z: 0.75}
    - {x: 6.299999, y: 2, z: 0.75}
    - {x: 5.433974, y: 2, z: 0.75}
    - {x: 4.5679483, y: 2, z: 0.75}
    - {x: 3.7019231, y: 2, z: 0.75}
    - {x: 2.8358977, y: 2, z: 0.75}
    - {x: 1.9698725, y: 2, z: 0.75}
    - {x: 10.197113, y: 2, z: 0}
    - {x: 9.331088, y: 2, z: 0}
    - {x: 3.2689104, y: 2, z: 0}
    - {x: 2.4028852, y: 2, z: 0}
    - {x: 9.764101, y: 2, z: -0.75}
    - {x: 8.898075, y: 2, z: -0.75}
    - {x: 8.03205, y: 2, z: -0.75}
    - {x: 7.1660247, y: 2, z: -0.75}
    - {x: 6.299999, y: 2, z: -0.75}
    - {x: 5.433974, y: 2, z: -0.75}
    - {x: 4.5679483, y: 2, z: -0.75}
    - {x: 3.7019231, y: 2, z: -0.75}
    - {x: 2.8358977, y: 2, z: -0.75}
    - {x: 1.9698725, y: 2, z: -0.75}
    - {x: 10.197113, y: 2, z: -1.5}
    - {x: 9.331088, y: 2, z: -1.5}
    - {x: 8.465063, y: 2, z: -1.5}
    - {x: 7.599037, y: 2, z: -1.5}
    - {x: 6.7330117, y: 2, z: -1.5}
    - {x: 5.8669868, y: 2, z: -1.5}
    - {x: 5.0009613, y: 2, z: -1.5}
    - {x: 4.134936, y: 2, z: -1.5}
    - {x: 3.2689104, y: 2, z: -1.5}
    - {x: 2.4028852, y: 2, z: -1.5}
    - {x: 9.764101, y: 2, z: -2.25}
    - {x: 8.898075, y: 2, z: -2.25}
    - {x: 8.03205, y: 2, z: -2.25}
    - {x: 7.1660247, y: 2, z: -2.25}
    - {x: 6.299999, y: 2, z: -2.25}
    - {x: 5.433974, y: 2, z: -2.25}
    - {x: 4.5679483, y: 2, z: -2.25}
    - {x: 3.7019231, y: 2, z: -2.25}
    - {x: 2.8358977, y: 2, z: -2.25}
    - {x: 1.9698725, y: 2, z: -2.25}
    - {x: 10.197113, y: 2, z: -3}
    - {x: 9.331088, y: 2, z: -3}
    - {x: 8.465063, y: 2, z: -3}
    - {x: 7.599037, y: 2, z: -3}
    - {x: 6.7330117, y: 2, z: -3}
    - {x: 5.8669868, y: 2, z: -3}
    - {x: 5.0009613, y: 2, z: -3}
    - {x: 4.134936, y: 2, z: -3}
    - {x: 3.2689104, y: 2, z: -3}
    - {x: 2.4028852, y: 2, z: -3}
    - {x: 9.764101, y: 2, z: -3.75}
    - {x: 8.898075, y: 2, z: -3.75}
    - {x: 8.03205, y: 2, z: -3.75}
    - {x: 7.1660247, y: 2, z: -3.75}
    - {x: 6.299999, y: 2, z: -3.75}
    - {x: 5.433974, y: 2, z: -3.75}
    - {x: 4.5679483, y: 2, z: -3.75}
    - {x: 3.7019231, y: 2, z: -3.75}
    - {x: 2.8358977, y: 2, z: -3.75}
    - {x: 1.9698725, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 203136135}
  meshFilter: {fileID: 203136138}
  meshRenderer: {fileID: 203136137}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.15703449
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 6.299999, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &203136137
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203136134}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &203136138
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203136134}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &278356555
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 278356556}
  - component: {fileID: 278356559}
  - component: {fileID: 278356558}
  - component: {fileID: 278356557}
  m_Layer: 0
  m_Name: Radial_Twist_90
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &278356556
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 278356555}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 93.6, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &278356557
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 278356555}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 90
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 89.269875, y: 2, z: -3.75}
    - {x: 90.135895, y: 2, z: -3.75}
    - {x: 91.00192, y: 2, z: -3.75}
    - {x: 91.86795, y: 2, z: -3.75}
    - {x: 92.73397, y: 2, z: -3.75}
    - {x: 93.6, y: 2, z: -3.75}
    - {x: 94.46603, y: 2, z: -3.75}
    - {x: 95.33205, y: 2, z: -3.75}
    - {x: 96.198074, y: 2, z: -3.75}
    - {x: 97.0641, y: 2, z: -3.75}
    - {x: 89.70288, y: 2, z: -3}
    - {x: 90.56891, y: 2, z: -3}
    - {x: 91.43494, y: 2, z: -3}
    - {x: 92.30096, y: 2, z: -3}
    - {x: 93.166985, y: 2, z: -3}
    - {x: 94.03301, y: 2, z: -3}
    - {x: 94.89904, y: 2, z: -3}
    - {x: 95.76506, y: 2, z: -3}
    - {x: 96.63109, y: 2, z: -3}
    - {x: 97.497116, y: 2, z: -3}
    - {x: 89.269875, y: 2, z: -2.25}
    - {x: 90.135895, y: 2, z: -2.25}
    - {x: 91.00192, y: 2, z: -2.25}
    - {x: 91.86795, y: 2, z: -2.25}
    - {x: 92.73397, y: 2, z: -2.25}
    - {x: 93.6, y: 2, z: -2.25}
    - {x: 94.46603, y: 2, z: -2.25}
    - {x: 95.33205, y: 2, z: -2.25}
    - {x: 96.198074, y: 2, z: -2.25}
    - {x: 97.0641, y: 2, z: -2.25}
    - {x: 89.70288, y: 2, z: -1.5}
    - {x: 90.56891, y: 2, z: -1.5}
    - {x: 91.43494, y: 2, z: -1.5}
    - {x: 92.30096, y: 2, z: -1.5}
    - {x: 93.166985, y: 2, z: -1.5}
    - {x: 94.03301, y: 2, z: -1.5}
    - {x: 94.89904, y: 2, z: -1.5}
    - {x: 95.76506, y: 2, z: -1.5}
    - {x: 96.63109, y: 2, z: -1.5}
    - {x: 97.497116, y: 2, z: -1.5}
    - {x: 89.269875, y: 2, z: -0.75}
    - {x: 90.135895, y: 2, z: -0.75}
    - {x: 91.00192, y: 2, z: -0.75}
    - {x: 91.86795, y: 2, z: -0.75}
    - {x: 92.73397, y: 2, z: -0.75}
    - {x: 93.6, y: 2, z: -0.75}
    - {x: 94.46603, y: 2, z: -0.75}
    - {x: 95.33205, y: 2, z: -0.75}
    - {x: 96.198074, y: 2, z: -0.75}
    - {x: 97.0641, y: 2, z: -0.75}
    - {x: 89.70288, y: 2, z: 0}
    - {x: 90.56891, y: 2, z: 0}
    - {x: 91.43494, y: 2, z: 0}
    - {x: 92.30096, y: 2, z: 0}
    - {x: 93.166985, y: 2, z: 0}
    - {x: 94.03301, y: 2, z: 0}
    - {x: 94.89904, y: 2, z: 0}
    - {x: 95.76506, y: 2, z: 0}
    - {x: 96.63109, y: 2, z: 0}
    - {x: 97.497116, y: 2, z: 0}
    - {x: 89.269875, y: 2, z: 0.75}
    - {x: 90.135895, y: 2, z: 0.75}
    - {x: 91.00192, y: 2, z: 0.75}
    - {x: 91.86795, y: 2, z: 0.75}
    - {x: 92.73397, y: 2, z: 0.75}
    - {x: 93.6, y: 2, z: 0.75}
    - {x: 94.46603, y: 2, z: 0.75}
    - {x: 95.33205, y: 2, z: 0.75}
    - {x: 96.198074, y: 2, z: 0.75}
    - {x: 97.0641, y: 2, z: 0.75}
    - {x: 89.70288, y: 2, z: 1.5}
    - {x: 90.56891, y: 2, z: 1.5}
    - {x: 91.43494, y: 2, z: 1.5}
    - {x: 92.30096, y: 2, z: 1.5}
    - {x: 93.166985, y: 2, z: 1.5}
    - {x: 94.03301, y: 2, z: 1.5}
    - {x: 94.89904, y: 2, z: 1.5}
    - {x: 95.76506, y: 2, z: 1.5}
    - {x: 96.63109, y: 2, z: 1.5}
    - {x: 97.497116, y: 2, z: 1.5}
    - {x: 89.269875, y: 2, z: 2.25}
    - {x: 90.135895, y: 2, z: 2.25}
    - {x: 91.00192, y: 2, z: 2.25}
    - {x: 91.86795, y: 2, z: 2.25}
    - {x: 92.73397, y: 2, z: 2.25}
    - {x: 93.6, y: 2, z: 2.25}
    - {x: 94.46603, y: 2, z: 2.25}
    - {x: 95.33205, y: 2, z: 2.25}
    - {x: 96.198074, y: 2, z: 2.25}
    - {x: 97.0641, y: 2, z: 2.25}
    - {x: 89.70288, y: 2, z: 3}
    - {x: 90.56891, y: 2, z: 3}
    - {x: 91.43494, y: 2, z: 3}
    - {x: 92.30096, y: 2, z: 3}
    - {x: 93.166985, y: 2, z: 3}
    - {x: 94.03301, y: 2, z: 3}
    - {x: 94.89904, y: 2, z: 3}
    - {x: 95.76506, y: 2, z: 3}
    - {x: 96.63109, y: 2, z: 3}
    - {x: 97.497116, y: 2, z: 3}
    pcBndIn:
    - {x: 95.76506, y: 2, z: 0}
    - {x: 94.89904, y: 2, z: 0}
    - {x: 94.03301, y: 2, z: 0}
    - {x: 93.166985, y: 2, z: 0}
    - {x: 92.30096, y: 2, z: 0}
    - {x: 91.43494, y: 2, z: 0}
    pcBndOut:
    - {x: 97.497116, y: 2, z: 3}
    - {x: 96.63109, y: 2, z: 3}
    - {x: 95.76506, y: 2, z: 3}
    - {x: 94.89904, y: 2, z: 3}
    - {x: 94.03301, y: 2, z: 3}
    - {x: 93.166985, y: 2, z: 3}
    - {x: 92.30096, y: 2, z: 3}
    - {x: 91.43494, y: 2, z: 3}
    - {x: 90.56891, y: 2, z: 3}
    - {x: 89.70288, y: 2, z: 3}
    - {x: 97.0641, y: 2, z: 2.25}
    - {x: 96.198074, y: 2, z: 2.25}
    - {x: 95.33205, y: 2, z: 2.25}
    - {x: 94.46603, y: 2, z: 2.25}
    - {x: 93.6, y: 2, z: 2.25}
    - {x: 92.73397, y: 2, z: 2.25}
    - {x: 91.86795, y: 2, z: 2.25}
    - {x: 91.00192, y: 2, z: 2.25}
    - {x: 90.135895, y: 2, z: 2.25}
    - {x: 89.269875, y: 2, z: 2.25}
    - {x: 97.497116, y: 2, z: 1.5}
    - {x: 96.63109, y: 2, z: 1.5}
    - {x: 95.76506, y: 2, z: 1.5}
    - {x: 94.89904, y: 2, z: 1.5}
    - {x: 94.03301, y: 2, z: 1.5}
    - {x: 93.166985, y: 2, z: 1.5}
    - {x: 92.30096, y: 2, z: 1.5}
    - {x: 91.43494, y: 2, z: 1.5}
    - {x: 90.56891, y: 2, z: 1.5}
    - {x: 89.70288, y: 2, z: 1.5}
    - {x: 97.0641, y: 2, z: 0.75}
    - {x: 96.198074, y: 2, z: 0.75}
    - {x: 95.33205, y: 2, z: 0.75}
    - {x: 94.46603, y: 2, z: 0.75}
    - {x: 93.6, y: 2, z: 0.75}
    - {x: 92.73397, y: 2, z: 0.75}
    - {x: 91.86795, y: 2, z: 0.75}
    - {x: 91.00192, y: 2, z: 0.75}
    - {x: 90.135895, y: 2, z: 0.75}
    - {x: 89.269875, y: 2, z: 0.75}
    - {x: 97.497116, y: 2, z: 0}
    - {x: 96.63109, y: 2, z: 0}
    - {x: 90.56891, y: 2, z: 0}
    - {x: 89.70288, y: 2, z: 0}
    - {x: 97.0641, y: 2, z: -0.75}
    - {x: 96.198074, y: 2, z: -0.75}
    - {x: 95.33205, y: 2, z: -0.75}
    - {x: 94.46603, y: 2, z: -0.75}
    - {x: 93.6, y: 2, z: -0.75}
    - {x: 92.73397, y: 2, z: -0.75}
    - {x: 91.86795, y: 2, z: -0.75}
    - {x: 91.00192, y: 2, z: -0.75}
    - {x: 90.135895, y: 2, z: -0.75}
    - {x: 89.269875, y: 2, z: -0.75}
    - {x: 97.497116, y: 2, z: -1.5}
    - {x: 96.63109, y: 2, z: -1.5}
    - {x: 95.76506, y: 2, z: -1.5}
    - {x: 94.89904, y: 2, z: -1.5}
    - {x: 94.03301, y: 2, z: -1.5}
    - {x: 93.166985, y: 2, z: -1.5}
    - {x: 92.30096, y: 2, z: -1.5}
    - {x: 91.43494, y: 2, z: -1.5}
    - {x: 90.56891, y: 2, z: -1.5}
    - {x: 89.70288, y: 2, z: -1.5}
    - {x: 97.0641, y: 2, z: -2.25}
    - {x: 96.198074, y: 2, z: -2.25}
    - {x: 95.33205, y: 2, z: -2.25}
    - {x: 94.46603, y: 2, z: -2.25}
    - {x: 93.6, y: 2, z: -2.25}
    - {x: 92.73397, y: 2, z: -2.25}
    - {x: 91.86795, y: 2, z: -2.25}
    - {x: 91.00192, y: 2, z: -2.25}
    - {x: 90.135895, y: 2, z: -2.25}
    - {x: 89.269875, y: 2, z: -2.25}
    - {x: 97.497116, y: 2, z: -3}
    - {x: 96.63109, y: 2, z: -3}
    - {x: 95.76506, y: 2, z: -3}
    - {x: 94.89904, y: 2, z: -3}
    - {x: 94.03301, y: 2, z: -3}
    - {x: 93.166985, y: 2, z: -3}
    - {x: 92.30096, y: 2, z: -3}
    - {x: 91.43494, y: 2, z: -3}
    - {x: 90.56891, y: 2, z: -3}
    - {x: 89.70288, y: 2, z: -3}
    - {x: 97.0641, y: 2, z: -3.75}
    - {x: 96.198074, y: 2, z: -3.75}
    - {x: 95.33205, y: 2, z: -3.75}
    - {x: 94.46603, y: 2, z: -3.75}
    - {x: 93.6, y: 2, z: -3.75}
    - {x: 92.73397, y: 2, z: -3.75}
    - {x: 91.86795, y: 2, z: -3.75}
    - {x: 91.00192, y: 2, z: -3.75}
    - {x: 90.135895, y: 2, z: -3.75}
    - {x: 89.269875, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 278356556}
  meshFilter: {fileID: 278356559}
  meshRenderer: {fileID: 278356558}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.08875861
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 93.6, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &278356558
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 278356555}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &278356559
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 278356555}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &371049144
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 371049145}
  - component: {fileID: 371049148}
  - component: {fileID: 371049147}
  - component: {fileID: 371049146}
  m_Layer: 0
  m_Name: Radial_Div0.2_PlaneOn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &371049145
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 19.2, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &371049146
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0.2
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 14.869874, y: 2, z: -3.75}
    - {x: 15.735899, y: 2, z: -3.75}
    - {x: 16.601925, y: 2, z: -3.75}
    - {x: 17.46795, y: 2, z: -3.75}
    - {x: 18.333975, y: 2, z: -3.75}
    - {x: 19.2, y: 2, z: -3.75}
    - {x: 20.066027, y: 2, z: -3.75}
    - {x: 20.93205, y: 2, z: -3.75}
    - {x: 21.798077, y: 2, z: -3.75}
    - {x: 22.664103, y: 2, z: -3.75}
    - {x: 15.302887, y: 2, z: -3}
    - {x: 16.168911, y: 2, z: -3}
    - {x: 17.034937, y: 2, z: -3}
    - {x: 17.900963, y: 2, z: -3}
    - {x: 18.766989, y: 2, z: -3}
    - {x: 19.633013, y: 2, z: -3}
    - {x: 20.499039, y: 2, z: -3}
    - {x: 21.365065, y: 2, z: -3}
    - {x: 22.23109, y: 2, z: -3}
    - {x: 23.097115, y: 2, z: -3}
    - {x: 14.869874, y: 2, z: -2.25}
    - {x: 15.735899, y: 2, z: -2.25}
    - {x: 16.601925, y: 2, z: -2.25}
    - {x: 17.46795, y: 2, z: -2.25}
    - {x: 18.333975, y: 2, z: -2.25}
    - {x: 19.2, y: 2, z: -2.25}
    - {x: 20.066027, y: 2, z: -2.25}
    - {x: 20.93205, y: 2, z: -2.25}
    - {x: 21.798077, y: 2, z: -2.25}
    - {x: 22.664103, y: 2, z: -2.25}
    - {x: 15.302887, y: 2, z: -1.5}
    - {x: 16.168911, y: 2, z: -1.5}
    - {x: 17.034937, y: 2, z: -1.5}
    - {x: 17.900963, y: 2, z: -1.5}
    - {x: 18.766989, y: 2, z: -1.5}
    - {x: 19.633013, y: 2, z: -1.5}
    - {x: 20.499039, y: 2, z: -1.5}
    - {x: 21.365065, y: 2, z: -1.5}
    - {x: 22.23109, y: 2, z: -1.5}
    - {x: 23.097115, y: 2, z: -1.5}
    - {x: 14.869874, y: 2, z: -0.75}
    - {x: 15.735899, y: 2, z: -0.75}
    - {x: 16.601925, y: 2, z: -0.75}
    - {x: 17.46795, y: 2, z: -0.75}
    - {x: 18.333975, y: 2, z: -0.75}
    - {x: 19.2, y: 2, z: -0.75}
    - {x: 20.066027, y: 2, z: -0.75}
    - {x: 20.93205, y: 2, z: -0.75}
    - {x: 21.798077, y: 2, z: -0.75}
    - {x: 22.664103, y: 2, z: -0.75}
    - {x: 15.302887, y: 2, z: 0}
    - {x: 16.168911, y: 2, z: 0}
    - {x: 17.034937, y: 2, z: 0}
    - {x: 17.900963, y: 2, z: 0}
    - {x: 18.766989, y: 2, z: 0}
    - {x: 19.633013, y: 2, z: 0}
    - {x: 20.499039, y: 2, z: 0}
    - {x: 21.365065, y: 2, z: 0}
    - {x: 22.23109, y: 2, z: 0}
    - {x: 23.097115, y: 2, z: 0}
    - {x: 14.869874, y: 2, z: 0.75}
    - {x: 15.735899, y: 2, z: 0.75}
    - {x: 16.601925, y: 2, z: 0.75}
    - {x: 17.46795, y: 2, z: 0.75}
    - {x: 18.333975, y: 2, z: 0.75}
    - {x: 19.2, y: 2, z: 0.75}
    - {x: 20.066027, y: 2, z: 0.75}
    - {x: 20.93205, y: 2, z: 0.75}
    - {x: 21.798077, y: 2, z: 0.75}
    - {x: 22.664103, y: 2, z: 0.75}
    - {x: 15.302887, y: 2, z: 1.5}
    - {x: 16.168911, y: 2, z: 1.5}
    - {x: 17.034937, y: 2, z: 1.5}
    - {x: 17.900963, y: 2, z: 1.5}
    - {x: 18.766989, y: 2, z: 1.5}
    - {x: 19.633013, y: 2, z: 1.5}
    - {x: 20.499039, y: 2, z: 1.5}
    - {x: 21.365065, y: 2, z: 1.5}
    - {x: 22.23109, y: 2, z: 1.5}
    - {x: 23.097115, y: 2, z: 1.5}
    - {x: 14.869874, y: 2, z: 2.25}
    - {x: 15.735899, y: 2, z: 2.25}
    - {x: 16.601925, y: 2, z: 2.25}
    - {x: 17.46795, y: 2, z: 2.25}
    - {x: 18.333975, y: 2, z: 2.25}
    - {x: 19.2, y: 2, z: 2.25}
    - {x: 20.066027, y: 2, z: 2.25}
    - {x: 20.93205, y: 2, z: 2.25}
    - {x: 21.798077, y: 2, z: 2.25}
    - {x: 22.664103, y: 2, z: 2.25}
    - {x: 15.302887, y: 2, z: 3}
    - {x: 16.168911, y: 2, z: 3}
    - {x: 17.034937, y: 2, z: 3}
    - {x: 17.900963, y: 2, z: 3}
    - {x: 18.766989, y: 2, z: 3}
    - {x: 19.633013, y: 2, z: 3}
    - {x: 20.499039, y: 2, z: 3}
    - {x: 21.365065, y: 2, z: 3}
    - {x: 22.23109, y: 2, z: 3}
    - {x: 23.097115, y: 2, z: 3}
    pcBndIn:
    - {x: 21.365065, y: 2, z: 0}
    - {x: 20.499039, y: 2, z: 0}
    - {x: 19.633013, y: 2, z: 0}
    - {x: 18.766989, y: 2, z: 0}
    - {x: 17.900963, y: 2, z: 0}
    - {x: 17.034937, y: 2, z: 0}
    pcBndOut:
    - {x: 23.097115, y: 2, z: 3}
    - {x: 22.23109, y: 2, z: 3}
    - {x: 21.365065, y: 2, z: 3}
    - {x: 20.499039, y: 2, z: 3}
    - {x: 19.633013, y: 2, z: 3}
    - {x: 18.766989, y: 2, z: 3}
    - {x: 17.900963, y: 2, z: 3}
    - {x: 17.034937, y: 2, z: 3}
    - {x: 16.168911, y: 2, z: 3}
    - {x: 15.302887, y: 2, z: 3}
    - {x: 22.664103, y: 2, z: 2.25}
    - {x: 21.798077, y: 2, z: 2.25}
    - {x: 20.93205, y: 2, z: 2.25}
    - {x: 20.066027, y: 2, z: 2.25}
    - {x: 19.2, y: 2, z: 2.25}
    - {x: 18.333975, y: 2, z: 2.25}
    - {x: 17.46795, y: 2, z: 2.25}
    - {x: 16.601925, y: 2, z: 2.25}
    - {x: 15.735899, y: 2, z: 2.25}
    - {x: 14.869874, y: 2, z: 2.25}
    - {x: 23.097115, y: 2, z: 1.5}
    - {x: 22.23109, y: 2, z: 1.5}
    - {x: 21.365065, y: 2, z: 1.5}
    - {x: 20.499039, y: 2, z: 1.5}
    - {x: 19.633013, y: 2, z: 1.5}
    - {x: 18.766989, y: 2, z: 1.5}
    - {x: 17.900963, y: 2, z: 1.5}
    - {x: 17.034937, y: 2, z: 1.5}
    - {x: 16.168911, y: 2, z: 1.5}
    - {x: 15.302887, y: 2, z: 1.5}
    - {x: 22.664103, y: 2, z: 0.75}
    - {x: 21.798077, y: 2, z: 0.75}
    - {x: 20.93205, y: 2, z: 0.75}
    - {x: 20.066027, y: 2, z: 0.75}
    - {x: 19.2, y: 2, z: 0.75}
    - {x: 18.333975, y: 2, z: 0.75}
    - {x: 17.46795, y: 2, z: 0.75}
    - {x: 16.601925, y: 2, z: 0.75}
    - {x: 15.735899, y: 2, z: 0.75}
    - {x: 14.869874, y: 2, z: 0.75}
    - {x: 23.097115, y: 2, z: 0}
    - {x: 22.23109, y: 2, z: 0}
    - {x: 16.168911, y: 2, z: 0}
    - {x: 15.302887, y: 2, z: 0}
    - {x: 22.664103, y: 2, z: -0.75}
    - {x: 21.798077, y: 2, z: -0.75}
    - {x: 20.93205, y: 2, z: -0.75}
    - {x: 20.066027, y: 2, z: -0.75}
    - {x: 19.2, y: 2, z: -0.75}
    - {x: 18.333975, y: 2, z: -0.75}
    - {x: 17.46795, y: 2, z: -0.75}
    - {x: 16.601925, y: 2, z: -0.75}
    - {x: 15.735899, y: 2, z: -0.75}
    - {x: 14.869874, y: 2, z: -0.75}
    - {x: 23.097115, y: 2, z: -1.5}
    - {x: 22.23109, y: 2, z: -1.5}
    - {x: 21.365065, y: 2, z: -1.5}
    - {x: 20.499039, y: 2, z: -1.5}
    - {x: 19.633013, y: 2, z: -1.5}
    - {x: 18.766989, y: 2, z: -1.5}
    - {x: 17.900963, y: 2, z: -1.5}
    - {x: 17.034937, y: 2, z: -1.5}
    - {x: 16.168911, y: 2, z: -1.5}
    - {x: 15.302887, y: 2, z: -1.5}
    - {x: 22.664103, y: 2, z: -2.25}
    - {x: 21.798077, y: 2, z: -2.25}
    - {x: 20.93205, y: 2, z: -2.25}
    - {x: 20.066027, y: 2, z: -2.25}
    - {x: 19.2, y: 2, z: -2.25}
    - {x: 18.333975, y: 2, z: -2.25}
    - {x: 17.46795, y: 2, z: -2.25}
    - {x: 16.601925, y: 2, z: -2.25}
    - {x: 15.735899, y: 2, z: -2.25}
    - {x: 14.869874, y: 2, z: -2.25}
    - {x: 23.097115, y: 2, z: -3}
    - {x: 22.23109, y: 2, z: -3}
    - {x: 21.365065, y: 2, z: -3}
    - {x: 20.499039, y: 2, z: -3}
    - {x: 19.633013, y: 2, z: -3}
    - {x: 18.766989, y: 2, z: -3}
    - {x: 17.900963, y: 2, z: -3}
    - {x: 17.034937, y: 2, z: -3}
    - {x: 16.168911, y: 2, z: -3}
    - {x: 15.302887, y: 2, z: -3}
    - {x: 22.664103, y: 2, z: -3.75}
    - {x: 21.798077, y: 2, z: -3.75}
    - {x: 20.93205, y: 2, z: -3.75}
    - {x: 20.066027, y: 2, z: -3.75}
    - {x: 19.2, y: 2, z: -3.75}
    - {x: 18.333975, y: 2, z: -3.75}
    - {x: 17.46795, y: 2, z: -3.75}
    - {x: 16.601925, y: 2, z: -3.75}
    - {x: 15.735899, y: 2, z: -3.75}
    - {x: 14.869874, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 1
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 371049145}
  meshFilter: {fileID: 371049148}
  meshRenderer: {fileID: 371049147}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.22872414
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 19.2, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &371049147
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &371049148
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &399585496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 399585497}
  - component: {fileID: 399585500}
  - component: {fileID: 399585499}
  - component: {fileID: 399585498}
  m_Layer: 0
  m_Name: Radial_Rad1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &399585497
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &399585498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 1
    divergence: 0
    restrictToPlane: 1
    rings: 5
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 10
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: -4.330127, y: 2, z: -3.75}
    - {x: -3.4641016, y: 2, z: -3.75}
    - {x: -2.598076, y: 2, z: -3.75}
    - {x: -1.7320508, y: 2, z: -3.75}
    - {x: -0.8660254, y: 2, z: -3.75}
    - {x: 0, y: 2, z: -3.75}
    - {x: 0.8660254, y: 2, z: -3.75}
    - {x: 1.7320508, y: 2, z: -3.75}
    - {x: 2.598076, y: 2, z: -3.75}
    - {x: 3.4641016, y: 2, z: -3.75}
    - {x: -3.897114, y: 2, z: -3}
    - {x: -3.0310888, y: 2, z: -3}
    - {x: -2.1650634, y: 2, z: -3}
    - {x: -1.299038, y: 2, z: -3}
    - {x: -0.4330127, y: 2, z: -3}
    - {x: 0.4330127, y: 2, z: -3}
    - {x: 1.299038, y: 2, z: -3}
    - {x: 2.1650634, y: 2, z: -3}
    - {x: 3.0310888, y: 2, z: -3}
    - {x: 3.8971143, y: 2, z: -3}
    - {x: -4.330127, y: 2, z: -2.25}
    - {x: -3.4641016, y: 2, z: -2.25}
    - {x: -2.598076, y: 2, z: -2.25}
    - {x: -1.7320508, y: 2, z: -2.25}
    - {x: -0.8660254, y: 2, z: -2.25}
    - {x: 0, y: 2, z: -2.25}
    - {x: 0.8660254, y: 2, z: -2.25}
    - {x: 1.7320508, y: 2, z: -2.25}
    - {x: 2.598076, y: 2, z: -2.25}
    - {x: 3.4641016, y: 2, z: -2.25}
    - {x: -3.897114, y: 2, z: -1.5}
    - {x: -3.0310888, y: 2, z: -1.5}
    - {x: -2.1650634, y: 2, z: -1.5}
    - {x: -1.299038, y: 2, z: -1.5}
    - {x: -0.4330127, y: 2, z: -1.5}
    - {x: 0.4330127, y: 2, z: -1.5}
    - {x: 1.299038, y: 2, z: -1.5}
    - {x: 2.1650634, y: 2, z: -1.5}
    - {x: 3.0310888, y: 2, z: -1.5}
    - {x: 3.8971143, y: 2, z: -1.5}
    - {x: -4.330127, y: 2, z: -0.75}
    - {x: -3.4641016, y: 2, z: -0.75}
    - {x: -2.598076, y: 2, z: -0.75}
    - {x: -1.7320508, y: 2, z: -0.75}
    - {x: -0.8660254, y: 2, z: -0.75}
    - {x: 0, y: 2, z: -0.75}
    - {x: 0.8660254, y: 2, z: -0.75}
    - {x: 1.7320508, y: 2, z: -0.75}
    - {x: 2.598076, y: 2, z: -0.75}
    - {x: 3.4641016, y: 2, z: -0.75}
    - {x: -3.897114, y: 2, z: 0}
    - {x: -3.0310888, y: 2, z: 0}
    - {x: -2.1650634, y: 2, z: 0}
    - {x: -1.299038, y: 2, z: 0}
    - {x: -0.4330127, y: 2, z: 0}
    - {x: 0.4330127, y: 2, z: 0}
    - {x: 1.299038, y: 2, z: 0}
    - {x: 2.1650634, y: 2, z: 0}
    - {x: 3.0310888, y: 2, z: 0}
    - {x: 3.8971143, y: 2, z: 0}
    - {x: -4.330127, y: 2, z: 0.75}
    - {x: -3.4641016, y: 2, z: 0.75}
    - {x: -2.598076, y: 2, z: 0.75}
    - {x: -1.7320508, y: 2, z: 0.75}
    - {x: -0.8660254, y: 2, z: 0.75}
    - {x: 0, y: 2, z: 0.75}
    - {x: 0.8660254, y: 2, z: 0.75}
    - {x: 1.7320508, y: 2, z: 0.75}
    - {x: 2.598076, y: 2, z: 0.75}
    - {x: 3.4641016, y: 2, z: 0.75}
    - {x: -3.897114, y: 2, z: 1.5}
    - {x: -3.0310888, y: 2, z: 1.5}
    - {x: -2.1650634, y: 2, z: 1.5}
    - {x: -1.299038, y: 2, z: 1.5}
    - {x: -0.4330127, y: 2, z: 1.5}
    - {x: 0.4330127, y: 2, z: 1.5}
    - {x: 1.299038, y: 2, z: 1.5}
    - {x: 2.1650634, y: 2, z: 1.5}
    - {x: 3.0310888, y: 2, z: 1.5}
    - {x: 3.8971143, y: 2, z: 1.5}
    - {x: -4.330127, y: 2, z: 2.25}
    - {x: -3.4641016, y: 2, z: 2.25}
    - {x: -2.598076, y: 2, z: 2.25}
    - {x: -1.7320508, y: 2, z: 2.25}
    - {x: -0.8660254, y: 2, z: 2.25}
    - {x: 0, y: 2, z: 2.25}
    - {x: 0.8660254, y: 2, z: 2.25}
    - {x: 1.7320508, y: 2, z: 2.25}
    - {x: 2.598076, y: 2, z: 2.25}
    - {x: 3.4641016, y: 2, z: 2.25}
    - {x: -3.897114, y: 2, z: 3}
    - {x: -3.0310888, y: 2, z: 3}
    - {x: -2.1650634, y: 2, z: 3}
    - {x: -1.299038, y: 2, z: 3}
    - {x: -0.4330127, y: 2, z: 3}
    - {x: 0.4330127, y: 2, z: 3}
    - {x: 1.299038, y: 2, z: 3}
    - {x: 2.1650634, y: 2, z: 3}
    - {x: 3.0310888, y: 2, z: 3}
    - {x: 3.8971143, y: 2, z: 3}
    pcBndIn:
    - {x: 2.1650634, y: 2, z: 0}
    - {x: 1.299038, y: 2, z: 0}
    - {x: 0.4330127, y: 2, z: 0}
    - {x: -0.4330127, y: 2, z: 0}
    - {x: -1.299038, y: 2, z: 0}
    - {x: -2.1650634, y: 2, z: 0}
    pcBndOut:
    - {x: 3.8971143, y: 2, z: 3}
    - {x: 3.0310888, y: 2, z: 3}
    - {x: 2.1650634, y: 2, z: 3}
    - {x: 1.299038, y: 2, z: 3}
    - {x: 0.4330127, y: 2, z: 3}
    - {x: -0.4330127, y: 2, z: 3}
    - {x: -1.299038, y: 2, z: 3}
    - {x: -2.1650634, y: 2, z: 3}
    - {x: -3.0310888, y: 2, z: 3}
    - {x: -3.897114, y: 2, z: 3}
    - {x: 3.4641016, y: 2, z: 2.25}
    - {x: 2.598076, y: 2, z: 2.25}
    - {x: 1.7320508, y: 2, z: 2.25}
    - {x: 0.8660254, y: 2, z: 2.25}
    - {x: 0, y: 2, z: 2.25}
    - {x: -0.8660254, y: 2, z: 2.25}
    - {x: -1.7320508, y: 2, z: 2.25}
    - {x: -2.598076, y: 2, z: 2.25}
    - {x: -3.4641016, y: 2, z: 2.25}
    - {x: -4.330127, y: 2, z: 2.25}
    - {x: 3.8971143, y: 2, z: 1.5}
    - {x: 3.0310888, y: 2, z: 1.5}
    - {x: 2.1650634, y: 2, z: 1.5}
    - {x: 1.299038, y: 2, z: 1.5}
    - {x: 0.4330127, y: 2, z: 1.5}
    - {x: -0.4330127, y: 2, z: 1.5}
    - {x: -1.299038, y: 2, z: 1.5}
    - {x: -2.1650634, y: 2, z: 1.5}
    - {x: -3.0310888, y: 2, z: 1.5}
    - {x: -3.897114, y: 2, z: 1.5}
    - {x: 3.4641016, y: 2, z: 0.75}
    - {x: 2.598076, y: 2, z: 0.75}
    - {x: 1.7320508, y: 2, z: 0.75}
    - {x: 0.8660254, y: 2, z: 0.75}
    - {x: 0, y: 2, z: 0.75}
    - {x: -0.8660254, y: 2, z: 0.75}
    - {x: -1.7320508, y: 2, z: 0.75}
    - {x: -2.598076, y: 2, z: 0.75}
    - {x: -3.4641016, y: 2, z: 0.75}
    - {x: -4.330127, y: 2, z: 0.75}
    - {x: 3.8971143, y: 2, z: 0}
    - {x: 3.0310888, y: 2, z: 0}
    - {x: -3.0310888, y: 2, z: 0}
    - {x: -3.897114, y: 2, z: 0}
    - {x: 3.4641016, y: 2, z: -0.75}
    - {x: 2.598076, y: 2, z: -0.75}
    - {x: 1.7320508, y: 2, z: -0.75}
    - {x: 0.8660254, y: 2, z: -0.75}
    - {x: 0, y: 2, z: -0.75}
    - {x: -0.8660254, y: 2, z: -0.75}
    - {x: -1.7320508, y: 2, z: -0.75}
    - {x: -2.598076, y: 2, z: -0.75}
    - {x: -3.4641016, y: 2, z: -0.75}
    - {x: -4.330127, y: 2, z: -0.75}
    - {x: 3.8971143, y: 2, z: -1.5}
    - {x: 3.0310888, y: 2, z: -1.5}
    - {x: 2.1650634, y: 2, z: -1.5}
    - {x: 1.299038, y: 2, z: -1.5}
    - {x: 0.4330127, y: 2, z: -1.5}
    - {x: -0.4330127, y: 2, z: -1.5}
    - {x: -1.299038, y: 2, z: -1.5}
    - {x: -2.1650634, y: 2, z: -1.5}
    - {x: -3.0310888, y: 2, z: -1.5}
    - {x: -3.897114, y: 2, z: -1.5}
    - {x: 3.4641016, y: 2, z: -2.25}
    - {x: 2.598076, y: 2, z: -2.25}
    - {x: 1.7320508, y: 2, z: -2.25}
    - {x: 0.8660254, y: 2, z: -2.25}
    - {x: 0, y: 2, z: -2.25}
    - {x: -0.8660254, y: 2, z: -2.25}
    - {x: -1.7320508, y: 2, z: -2.25}
    - {x: -2.598076, y: 2, z: -2.25}
    - {x: -3.4641016, y: 2, z: -2.25}
    - {x: -4.330127, y: 2, z: -2.25}
    - {x: 3.8971143, y: 2, z: -3}
    - {x: 3.0310888, y: 2, z: -3}
    - {x: 2.1650634, y: 2, z: -3}
    - {x: 1.299038, y: 2, z: -3}
    - {x: 0.4330127, y: 2, z: -3}
    - {x: -0.4330127, y: 2, z: -3}
    - {x: -1.299038, y: 2, z: -3}
    - {x: -2.1650634, y: 2, z: -3}
    - {x: -3.0310888, y: 2, z: -3}
    - {x: -3.897114, y: 2, z: -3}
    - {x: 3.4641016, y: 2, z: -3.75}
    - {x: 2.598076, y: 2, z: -3.75}
    - {x: 1.7320508, y: 2, z: -3.75}
    - {x: 0.8660254, y: 2, z: -3.75}
    - {x: 0, y: 2, z: -3.75}
    - {x: -0.8660254, y: 2, z: -3.75}
    - {x: -1.7320508, y: 2, z: -3.75}
    - {x: -2.598076, y: 2, z: -3.75}
    - {x: -3.4641016, y: 2, z: -3.75}
    - {x: -4.330127, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 399585497}
  meshFilter: {fileID: 399585500}
  meshRenderer: {fileID: 399585499}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.24237931
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 0, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &399585499
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &399585500
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &410587154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 410587155}
  - component: {fileID: 410587158}
  - component: {fileID: 410587157}
  - component: {fileID: 410587156}
  m_Layer: 0
  m_Name: Radial_Twist_0_RandomRays_100
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &410587155
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410587154}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 74.1, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &410587156
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410587154}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 100
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 69.769875, y: 2, z: -3.75}
    - {x: 70.635895, y: 2, z: -3.75}
    - {x: 71.50192, y: 2, z: -3.75}
    - {x: 72.36795, y: 2, z: -3.75}
    - {x: 73.23397, y: 2, z: -3.75}
    - {x: 74.1, y: 2, z: -3.75}
    - {x: 74.96603, y: 2, z: -3.75}
    - {x: 75.83205, y: 2, z: -3.75}
    - {x: 76.698074, y: 2, z: -3.75}
    - {x: 77.5641, y: 2, z: -3.75}
    - {x: 70.20288, y: 2, z: -3}
    - {x: 71.06891, y: 2, z: -3}
    - {x: 71.93494, y: 2, z: -3}
    - {x: 72.80096, y: 2, z: -3}
    - {x: 73.666985, y: 2, z: -3}
    - {x: 74.53301, y: 2, z: -3}
    - {x: 75.39904, y: 2, z: -3}
    - {x: 76.26506, y: 2, z: -3}
    - {x: 77.13109, y: 2, z: -3}
    - {x: 77.997116, y: 2, z: -3}
    - {x: 69.769875, y: 2, z: -2.25}
    - {x: 70.635895, y: 2, z: -2.25}
    - {x: 71.50192, y: 2, z: -2.25}
    - {x: 72.36795, y: 2, z: -2.25}
    - {x: 73.23397, y: 2, z: -2.25}
    - {x: 74.1, y: 2, z: -2.25}
    - {x: 74.96603, y: 2, z: -2.25}
    - {x: 75.83205, y: 2, z: -2.25}
    - {x: 76.698074, y: 2, z: -2.25}
    - {x: 77.5641, y: 2, z: -2.25}
    - {x: 70.20288, y: 2, z: -1.5}
    - {x: 71.06891, y: 2, z: -1.5}
    - {x: 71.93494, y: 2, z: -1.5}
    - {x: 72.80096, y: 2, z: -1.5}
    - {x: 73.666985, y: 2, z: -1.5}
    - {x: 74.53301, y: 2, z: -1.5}
    - {x: 75.39904, y: 2, z: -1.5}
    - {x: 76.26506, y: 2, z: -1.5}
    - {x: 77.13109, y: 2, z: -1.5}
    - {x: 77.997116, y: 2, z: -1.5}
    - {x: 69.769875, y: 2, z: -0.75}
    - {x: 70.635895, y: 2, z: -0.75}
    - {x: 71.50192, y: 2, z: -0.75}
    - {x: 72.36795, y: 2, z: -0.75}
    - {x: 73.23397, y: 2, z: -0.75}
    - {x: 74.1, y: 2, z: -0.75}
    - {x: 74.96603, y: 2, z: -0.75}
    - {x: 75.83205, y: 2, z: -0.75}
    - {x: 76.698074, y: 2, z: -0.75}
    - {x: 77.5641, y: 2, z: -0.75}
    - {x: 70.20288, y: 2, z: 0}
    - {x: 71.06891, y: 2, z: 0}
    - {x: 71.93494, y: 2, z: 0}
    - {x: 72.80096, y: 2, z: 0}
    - {x: 73.666985, y: 2, z: 0}
    - {x: 74.53301, y: 2, z: 0}
    - {x: 75.39904, y: 2, z: 0}
    - {x: 76.26506, y: 2, z: 0}
    - {x: 77.13109, y: 2, z: 0}
    - {x: 77.997116, y: 2, z: 0}
    - {x: 69.769875, y: 2, z: 0.75}
    - {x: 70.635895, y: 2, z: 0.75}
    - {x: 71.50192, y: 2, z: 0.75}
    - {x: 72.36795, y: 2, z: 0.75}
    - {x: 73.23397, y: 2, z: 0.75}
    - {x: 74.1, y: 2, z: 0.75}
    - {x: 74.96603, y: 2, z: 0.75}
    - {x: 75.83205, y: 2, z: 0.75}
    - {x: 76.698074, y: 2, z: 0.75}
    - {x: 77.5641, y: 2, z: 0.75}
    - {x: 70.20288, y: 2, z: 1.5}
    - {x: 71.06891, y: 2, z: 1.5}
    - {x: 71.93494, y: 2, z: 1.5}
    - {x: 72.80096, y: 2, z: 1.5}
    - {x: 73.666985, y: 2, z: 1.5}
    - {x: 74.53301, y: 2, z: 1.5}
    - {x: 75.39904, y: 2, z: 1.5}
    - {x: 76.26506, y: 2, z: 1.5}
    - {x: 77.13109, y: 2, z: 1.5}
    - {x: 77.997116, y: 2, z: 1.5}
    - {x: 69.769875, y: 2, z: 2.25}
    - {x: 70.635895, y: 2, z: 2.25}
    - {x: 71.50192, y: 2, z: 2.25}
    - {x: 72.36795, y: 2, z: 2.25}
    - {x: 73.23397, y: 2, z: 2.25}
    - {x: 74.1, y: 2, z: 2.25}
    - {x: 74.96603, y: 2, z: 2.25}
    - {x: 75.83205, y: 2, z: 2.25}
    - {x: 76.698074, y: 2, z: 2.25}
    - {x: 77.5641, y: 2, z: 2.25}
    - {x: 70.20288, y: 2, z: 3}
    - {x: 71.06891, y: 2, z: 3}
    - {x: 71.93494, y: 2, z: 3}
    - {x: 72.80096, y: 2, z: 3}
    - {x: 73.666985, y: 2, z: 3}
    - {x: 74.53301, y: 2, z: 3}
    - {x: 75.39904, y: 2, z: 3}
    - {x: 76.26506, y: 2, z: 3}
    - {x: 77.13109, y: 2, z: 3}
    - {x: 77.997116, y: 2, z: 3}
    pcBndIn:
    - {x: 76.26506, y: 2, z: 0}
    - {x: 75.39904, y: 2, z: 0}
    - {x: 74.53301, y: 2, z: 0}
    - {x: 73.666985, y: 2, z: 0}
    - {x: 72.80096, y: 2, z: 0}
    - {x: 71.93494, y: 2, z: 0}
    pcBndOut:
    - {x: 77.997116, y: 2, z: 3}
    - {x: 77.13109, y: 2, z: 3}
    - {x: 76.26506, y: 2, z: 3}
    - {x: 75.39904, y: 2, z: 3}
    - {x: 74.53301, y: 2, z: 3}
    - {x: 73.666985, y: 2, z: 3}
    - {x: 72.80096, y: 2, z: 3}
    - {x: 71.93494, y: 2, z: 3}
    - {x: 71.06891, y: 2, z: 3}
    - {x: 70.20288, y: 2, z: 3}
    - {x: 77.5641, y: 2, z: 2.25}
    - {x: 76.698074, y: 2, z: 2.25}
    - {x: 75.83205, y: 2, z: 2.25}
    - {x: 74.96603, y: 2, z: 2.25}
    - {x: 74.1, y: 2, z: 2.25}
    - {x: 73.23397, y: 2, z: 2.25}
    - {x: 72.36795, y: 2, z: 2.25}
    - {x: 71.50192, y: 2, z: 2.25}
    - {x: 70.635895, y: 2, z: 2.25}
    - {x: 69.769875, y: 2, z: 2.25}
    - {x: 77.997116, y: 2, z: 1.5}
    - {x: 77.13109, y: 2, z: 1.5}
    - {x: 76.26506, y: 2, z: 1.5}
    - {x: 75.39904, y: 2, z: 1.5}
    - {x: 74.53301, y: 2, z: 1.5}
    - {x: 73.666985, y: 2, z: 1.5}
    - {x: 72.80096, y: 2, z: 1.5}
    - {x: 71.93494, y: 2, z: 1.5}
    - {x: 71.06891, y: 2, z: 1.5}
    - {x: 70.20288, y: 2, z: 1.5}
    - {x: 77.5641, y: 2, z: 0.75}
    - {x: 76.698074, y: 2, z: 0.75}
    - {x: 75.83205, y: 2, z: 0.75}
    - {x: 74.96603, y: 2, z: 0.75}
    - {x: 74.1, y: 2, z: 0.75}
    - {x: 73.23397, y: 2, z: 0.75}
    - {x: 72.36795, y: 2, z: 0.75}
    - {x: 71.50192, y: 2, z: 0.75}
    - {x: 70.635895, y: 2, z: 0.75}
    - {x: 69.769875, y: 2, z: 0.75}
    - {x: 77.997116, y: 2, z: 0}
    - {x: 77.13109, y: 2, z: 0}
    - {x: 71.06891, y: 2, z: 0}
    - {x: 70.20288, y: 2, z: 0}
    - {x: 77.5641, y: 2, z: -0.75}
    - {x: 76.698074, y: 2, z: -0.75}
    - {x: 75.83205, y: 2, z: -0.75}
    - {x: 74.96603, y: 2, z: -0.75}
    - {x: 74.1, y: 2, z: -0.75}
    - {x: 73.23397, y: 2, z: -0.75}
    - {x: 72.36795, y: 2, z: -0.75}
    - {x: 71.50192, y: 2, z: -0.75}
    - {x: 70.635895, y: 2, z: -0.75}
    - {x: 69.769875, y: 2, z: -0.75}
    - {x: 77.997116, y: 2, z: -1.5}
    - {x: 77.13109, y: 2, z: -1.5}
    - {x: 76.26506, y: 2, z: -1.5}
    - {x: 75.39904, y: 2, z: -1.5}
    - {x: 74.53301, y: 2, z: -1.5}
    - {x: 73.666985, y: 2, z: -1.5}
    - {x: 72.80096, y: 2, z: -1.5}
    - {x: 71.93494, y: 2, z: -1.5}
    - {x: 71.06891, y: 2, z: -1.5}
    - {x: 70.20288, y: 2, z: -1.5}
    - {x: 77.5641, y: 2, z: -2.25}
    - {x: 76.698074, y: 2, z: -2.25}
    - {x: 75.83205, y: 2, z: -2.25}
    - {x: 74.96603, y: 2, z: -2.25}
    - {x: 74.1, y: 2, z: -2.25}
    - {x: 73.23397, y: 2, z: -2.25}
    - {x: 72.36795, y: 2, z: -2.25}
    - {x: 71.50192, y: 2, z: -2.25}
    - {x: 70.635895, y: 2, z: -2.25}
    - {x: 69.769875, y: 2, z: -2.25}
    - {x: 77.997116, y: 2, z: -3}
    - {x: 77.13109, y: 2, z: -3}
    - {x: 76.26506, y: 2, z: -3}
    - {x: 75.39904, y: 2, z: -3}
    - {x: 74.53301, y: 2, z: -3}
    - {x: 73.666985, y: 2, z: -3}
    - {x: 72.80096, y: 2, z: -3}
    - {x: 71.93494, y: 2, z: -3}
    - {x: 71.06891, y: 2, z: -3}
    - {x: 70.20288, y: 2, z: -3}
    - {x: 77.5641, y: 2, z: -3.75}
    - {x: 76.698074, y: 2, z: -3.75}
    - {x: 75.83205, y: 2, z: -3.75}
    - {x: 74.96603, y: 2, z: -3.75}
    - {x: 74.1, y: 2, z: -3.75}
    - {x: 73.23397, y: 2, z: -3.75}
    - {x: 72.36795, y: 2, z: -3.75}
    - {x: 71.50192, y: 2, z: -3.75}
    - {x: 70.635895, y: 2, z: -3.75}
    - {x: 69.769875, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 410587155}
  meshFilter: {fileID: 410587158}
  meshRenderer: {fileID: 410587157}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.092172414
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 74.1, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &410587157
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410587154}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &410587158
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410587154}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &580638904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 580638905}
  - component: {fileID: 580638908}
  - component: {fileID: 580638907}
  - component: {fileID: 580638906}
  m_Layer: 0
  m_Name: Radial_Div0.2_PlaneOff
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &580638905
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 25.5, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &580638906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0.2
    restrictToPlane: 0
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 21.169872, y: 2, z: -3.75}
    - {x: 22.035898, y: 2, z: -3.75}
    - {x: 22.901924, y: 2, z: -3.75}
    - {x: 23.76795, y: 2, z: -3.75}
    - {x: 24.633974, y: 2, z: -3.75}
    - {x: 25.5, y: 2, z: -3.75}
    - {x: 26.366026, y: 2, z: -3.75}
    - {x: 27.23205, y: 2, z: -3.75}
    - {x: 28.098076, y: 2, z: -3.75}
    - {x: 28.964102, y: 2, z: -3.75}
    - {x: 21.602886, y: 2, z: -3}
    - {x: 22.46891, y: 2, z: -3}
    - {x: 23.334936, y: 2, z: -3}
    - {x: 24.200962, y: 2, z: -3}
    - {x: 25.066988, y: 2, z: -3}
    - {x: 25.933012, y: 2, z: -3}
    - {x: 26.799038, y: 2, z: -3}
    - {x: 27.665064, y: 2, z: -3}
    - {x: 28.53109, y: 2, z: -3}
    - {x: 29.397114, y: 2, z: -3}
    - {x: 21.169872, y: 2, z: -2.25}
    - {x: 22.035898, y: 2, z: -2.25}
    - {x: 22.901924, y: 2, z: -2.25}
    - {x: 23.76795, y: 2, z: -2.25}
    - {x: 24.633974, y: 2, z: -2.25}
    - {x: 25.5, y: 2, z: -2.25}
    - {x: 26.366026, y: 2, z: -2.25}
    - {x: 27.23205, y: 2, z: -2.25}
    - {x: 28.098076, y: 2, z: -2.25}
    - {x: 28.964102, y: 2, z: -2.25}
    - {x: 21.602886, y: 2, z: -1.5}
    - {x: 22.46891, y: 2, z: -1.5}
    - {x: 23.334936, y: 2, z: -1.5}
    - {x: 24.200962, y: 2, z: -1.5}
    - {x: 25.066988, y: 2, z: -1.5}
    - {x: 25.933012, y: 2, z: -1.5}
    - {x: 26.799038, y: 2, z: -1.5}
    - {x: 27.665064, y: 2, z: -1.5}
    - {x: 28.53109, y: 2, z: -1.5}
    - {x: 29.397114, y: 2, z: -1.5}
    - {x: 21.169872, y: 2, z: -0.75}
    - {x: 22.035898, y: 2, z: -0.75}
    - {x: 22.901924, y: 2, z: -0.75}
    - {x: 23.76795, y: 2, z: -0.75}
    - {x: 24.633974, y: 2, z: -0.75}
    - {x: 25.5, y: 2, z: -0.75}
    - {x: 26.366026, y: 2, z: -0.75}
    - {x: 27.23205, y: 2, z: -0.75}
    - {x: 28.098076, y: 2, z: -0.75}
    - {x: 28.964102, y: 2, z: -0.75}
    - {x: 21.602886, y: 2, z: 0}
    - {x: 22.46891, y: 2, z: 0}
    - {x: 23.334936, y: 2, z: 0}
    - {x: 24.200962, y: 2, z: 0}
    - {x: 25.066988, y: 2, z: 0}
    - {x: 25.933012, y: 2, z: 0}
    - {x: 26.799038, y: 2, z: 0}
    - {x: 27.665064, y: 2, z: 0}
    - {x: 28.53109, y: 2, z: 0}
    - {x: 29.397114, y: 2, z: 0}
    - {x: 21.169872, y: 2, z: 0.75}
    - {x: 22.035898, y: 2, z: 0.75}
    - {x: 22.901924, y: 2, z: 0.75}
    - {x: 23.76795, y: 2, z: 0.75}
    - {x: 24.633974, y: 2, z: 0.75}
    - {x: 25.5, y: 2, z: 0.75}
    - {x: 26.366026, y: 2, z: 0.75}
    - {x: 27.23205, y: 2, z: 0.75}
    - {x: 28.098076, y: 2, z: 0.75}
    - {x: 28.964102, y: 2, z: 0.75}
    - {x: 21.602886, y: 2, z: 1.5}
    - {x: 22.46891, y: 2, z: 1.5}
    - {x: 23.334936, y: 2, z: 1.5}
    - {x: 24.200962, y: 2, z: 1.5}
    - {x: 25.066988, y: 2, z: 1.5}
    - {x: 25.933012, y: 2, z: 1.5}
    - {x: 26.799038, y: 2, z: 1.5}
    - {x: 27.665064, y: 2, z: 1.5}
    - {x: 28.53109, y: 2, z: 1.5}
    - {x: 29.397114, y: 2, z: 1.5}
    - {x: 21.169872, y: 2, z: 2.25}
    - {x: 22.035898, y: 2, z: 2.25}
    - {x: 22.901924, y: 2, z: 2.25}
    - {x: 23.76795, y: 2, z: 2.25}
    - {x: 24.633974, y: 2, z: 2.25}
    - {x: 25.5, y: 2, z: 2.25}
    - {x: 26.366026, y: 2, z: 2.25}
    - {x: 27.23205, y: 2, z: 2.25}
    - {x: 28.098076, y: 2, z: 2.25}
    - {x: 28.964102, y: 2, z: 2.25}
    - {x: 21.602886, y: 2, z: 3}
    - {x: 22.46891, y: 2, z: 3}
    - {x: 23.334936, y: 2, z: 3}
    - {x: 24.200962, y: 2, z: 3}
    - {x: 25.066988, y: 2, z: 3}
    - {x: 25.933012, y: 2, z: 3}
    - {x: 26.799038, y: 2, z: 3}
    - {x: 27.665064, y: 2, z: 3}
    - {x: 28.53109, y: 2, z: 3}
    - {x: 29.397114, y: 2, z: 3}
    pcBndIn:
    - {x: 27.665064, y: 2, z: 0}
    - {x: 26.799038, y: 2, z: 0}
    - {x: 25.933012, y: 2, z: 0}
    - {x: 25.066988, y: 2, z: 0}
    - {x: 24.200962, y: 2, z: 0}
    - {x: 23.334936, y: 2, z: 0}
    pcBndOut:
    - {x: 29.397114, y: 2, z: 3}
    - {x: 28.53109, y: 2, z: 3}
    - {x: 27.665064, y: 2, z: 3}
    - {x: 26.799038, y: 2, z: 3}
    - {x: 25.933012, y: 2, z: 3}
    - {x: 25.066988, y: 2, z: 3}
    - {x: 24.200962, y: 2, z: 3}
    - {x: 23.334936, y: 2, z: 3}
    - {x: 22.46891, y: 2, z: 3}
    - {x: 21.602886, y: 2, z: 3}
    - {x: 28.964102, y: 2, z: 2.25}
    - {x: 28.098076, y: 2, z: 2.25}
    - {x: 27.23205, y: 2, z: 2.25}
    - {x: 26.366026, y: 2, z: 2.25}
    - {x: 25.5, y: 2, z: 2.25}
    - {x: 24.633974, y: 2, z: 2.25}
    - {x: 23.76795, y: 2, z: 2.25}
    - {x: 22.901924, y: 2, z: 2.25}
    - {x: 22.035898, y: 2, z: 2.25}
    - {x: 21.169872, y: 2, z: 2.25}
    - {x: 29.397114, y: 2, z: 1.5}
    - {x: 28.53109, y: 2, z: 1.5}
    - {x: 27.665064, y: 2, z: 1.5}
    - {x: 26.799038, y: 2, z: 1.5}
    - {x: 25.933012, y: 2, z: 1.5}
    - {x: 25.066988, y: 2, z: 1.5}
    - {x: 24.200962, y: 2, z: 1.5}
    - {x: 23.334936, y: 2, z: 1.5}
    - {x: 22.46891, y: 2, z: 1.5}
    - {x: 21.602886, y: 2, z: 1.5}
    - {x: 28.964102, y: 2, z: 0.75}
    - {x: 28.098076, y: 2, z: 0.75}
    - {x: 27.23205, y: 2, z: 0.75}
    - {x: 26.366026, y: 2, z: 0.75}
    - {x: 25.5, y: 2, z: 0.75}
    - {x: 24.633974, y: 2, z: 0.75}
    - {x: 23.76795, y: 2, z: 0.75}
    - {x: 22.901924, y: 2, z: 0.75}
    - {x: 22.035898, y: 2, z: 0.75}
    - {x: 21.169872, y: 2, z: 0.75}
    - {x: 29.397114, y: 2, z: 0}
    - {x: 28.53109, y: 2, z: 0}
    - {x: 22.46891, y: 2, z: 0}
    - {x: 21.602886, y: 2, z: 0}
    - {x: 28.964102, y: 2, z: -0.75}
    - {x: 28.098076, y: 2, z: -0.75}
    - {x: 27.23205, y: 2, z: -0.75}
    - {x: 26.366026, y: 2, z: -0.75}
    - {x: 25.5, y: 2, z: -0.75}
    - {x: 24.633974, y: 2, z: -0.75}
    - {x: 23.76795, y: 2, z: -0.75}
    - {x: 22.901924, y: 2, z: -0.75}
    - {x: 22.035898, y: 2, z: -0.75}
    - {x: 21.169872, y: 2, z: -0.75}
    - {x: 29.397114, y: 2, z: -1.5}
    - {x: 28.53109, y: 2, z: -1.5}
    - {x: 27.665064, y: 2, z: -1.5}
    - {x: 26.799038, y: 2, z: -1.5}
    - {x: 25.933012, y: 2, z: -1.5}
    - {x: 25.066988, y: 2, z: -1.5}
    - {x: 24.200962, y: 2, z: -1.5}
    - {x: 23.334936, y: 2, z: -1.5}
    - {x: 22.46891, y: 2, z: -1.5}
    - {x: 21.602886, y: 2, z: -1.5}
    - {x: 28.964102, y: 2, z: -2.25}
    - {x: 28.098076, y: 2, z: -2.25}
    - {x: 27.23205, y: 2, z: -2.25}
    - {x: 26.366026, y: 2, z: -2.25}
    - {x: 25.5, y: 2, z: -2.25}
    - {x: 24.633974, y: 2, z: -2.25}
    - {x: 23.76795, y: 2, z: -2.25}
    - {x: 22.901924, y: 2, z: -2.25}
    - {x: 22.035898, y: 2, z: -2.25}
    - {x: 21.169872, y: 2, z: -2.25}
    - {x: 29.397114, y: 2, z: -3}
    - {x: 28.53109, y: 2, z: -3}
    - {x: 27.665064, y: 2, z: -3}
    - {x: 26.799038, y: 2, z: -3}
    - {x: 25.933012, y: 2, z: -3}
    - {x: 25.066988, y: 2, z: -3}
    - {x: 24.200962, y: 2, z: -3}
    - {x: 23.334936, y: 2, z: -3}
    - {x: 22.46891, y: 2, z: -3}
    - {x: 21.602886, y: 2, z: -3}
    - {x: 28.964102, y: 2, z: -3.75}
    - {x: 28.098076, y: 2, z: -3.75}
    - {x: 27.23205, y: 2, z: -3.75}
    - {x: 26.366026, y: 2, z: -3.75}
    - {x: 25.5, y: 2, z: -3.75}
    - {x: 24.633974, y: 2, z: -3.75}
    - {x: 23.76795, y: 2, z: -3.75}
    - {x: 22.901924, y: 2, z: -3.75}
    - {x: 22.035898, y: 2, z: -3.75}
    - {x: 21.169872, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 1
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 580638905}
  meshFilter: {fileID: 580638908}
  meshRenderer: {fileID: 580638907}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.26968965
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 25.5, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &580638907
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &580638908
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &696151832
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 696151833}
  - component: {fileID: 696151836}
  - component: {fileID: 696151835}
  - component: {fileID: 696151834}
  m_Layer: 0
  m_Name: Radial_Focus_0_100
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &696151833
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 40.5, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &696151834
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 100
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 36.169872, y: 2, z: -3.75}
    - {x: 37.0359, y: 2, z: -3.75}
    - {x: 37.901924, y: 2, z: -3.75}
    - {x: 38.76795, y: 2, z: -3.75}
    - {x: 39.633976, y: 2, z: -3.75}
    - {x: 40.5, y: 2, z: -3.75}
    - {x: 41.366024, y: 2, z: -3.75}
    - {x: 42.23205, y: 2, z: -3.75}
    - {x: 43.098076, y: 2, z: -3.75}
    - {x: 43.9641, y: 2, z: -3.75}
    - {x: 36.602886, y: 2, z: -3}
    - {x: 37.46891, y: 2, z: -3}
    - {x: 38.334938, y: 2, z: -3}
    - {x: 39.200962, y: 2, z: -3}
    - {x: 40.066986, y: 2, z: -3}
    - {x: 40.933014, y: 2, z: -3}
    - {x: 41.799038, y: 2, z: -3}
    - {x: 42.665062, y: 2, z: -3}
    - {x: 43.53109, y: 2, z: -3}
    - {x: 44.397114, y: 2, z: -3}
    - {x: 36.169872, y: 2, z: -2.25}
    - {x: 37.0359, y: 2, z: -2.25}
    - {x: 37.901924, y: 2, z: -2.25}
    - {x: 38.76795, y: 2, z: -2.25}
    - {x: 39.633976, y: 2, z: -2.25}
    - {x: 40.5, y: 2, z: -2.25}
    - {x: 41.366024, y: 2, z: -2.25}
    - {x: 42.23205, y: 2, z: -2.25}
    - {x: 43.098076, y: 2, z: -2.25}
    - {x: 43.9641, y: 2, z: -2.25}
    - {x: 36.602886, y: 2, z: -1.5}
    - {x: 37.46891, y: 2, z: -1.5}
    - {x: 38.334938, y: 2, z: -1.5}
    - {x: 39.200962, y: 2, z: -1.5}
    - {x: 40.066986, y: 2, z: -1.5}
    - {x: 40.933014, y: 2, z: -1.5}
    - {x: 41.799038, y: 2, z: -1.5}
    - {x: 42.665062, y: 2, z: -1.5}
    - {x: 43.53109, y: 2, z: -1.5}
    - {x: 44.397114, y: 2, z: -1.5}
    - {x: 36.169872, y: 2, z: -0.75}
    - {x: 37.0359, y: 2, z: -0.75}
    - {x: 37.901924, y: 2, z: -0.75}
    - {x: 38.76795, y: 2, z: -0.75}
    - {x: 39.633976, y: 2, z: -0.75}
    - {x: 40.5, y: 2, z: -0.75}
    - {x: 41.366024, y: 2, z: -0.75}
    - {x: 42.23205, y: 2, z: -0.75}
    - {x: 43.098076, y: 2, z: -0.75}
    - {x: 43.9641, y: 2, z: -0.75}
    - {x: 36.602886, y: 2, z: 0}
    - {x: 37.46891, y: 2, z: 0}
    - {x: 38.334938, y: 2, z: 0}
    - {x: 39.200962, y: 2, z: 0}
    - {x: 40.066986, y: 2, z: 0}
    - {x: 40.933014, y: 2, z: 0}
    - {x: 41.799038, y: 2, z: 0}
    - {x: 42.665062, y: 2, z: 0}
    - {x: 43.53109, y: 2, z: 0}
    - {x: 44.397114, y: 2, z: 0}
    - {x: 36.169872, y: 2, z: 0.75}
    - {x: 37.0359, y: 2, z: 0.75}
    - {x: 37.901924, y: 2, z: 0.75}
    - {x: 38.76795, y: 2, z: 0.75}
    - {x: 39.633976, y: 2, z: 0.75}
    - {x: 40.5, y: 2, z: 0.75}
    - {x: 41.366024, y: 2, z: 0.75}
    - {x: 42.23205, y: 2, z: 0.75}
    - {x: 43.098076, y: 2, z: 0.75}
    - {x: 43.9641, y: 2, z: 0.75}
    - {x: 36.602886, y: 2, z: 1.5}
    - {x: 37.46891, y: 2, z: 1.5}
    - {x: 38.334938, y: 2, z: 1.5}
    - {x: 39.200962, y: 2, z: 1.5}
    - {x: 40.066986, y: 2, z: 1.5}
    - {x: 40.933014, y: 2, z: 1.5}
    - {x: 41.799038, y: 2, z: 1.5}
    - {x: 42.665062, y: 2, z: 1.5}
    - {x: 43.53109, y: 2, z: 1.5}
    - {x: 44.397114, y: 2, z: 1.5}
    - {x: 36.169872, y: 2, z: 2.25}
    - {x: 37.0359, y: 2, z: 2.25}
    - {x: 37.901924, y: 2, z: 2.25}
    - {x: 38.76795, y: 2, z: 2.25}
    - {x: 39.633976, y: 2, z: 2.25}
    - {x: 40.5, y: 2, z: 2.25}
    - {x: 41.366024, y: 2, z: 2.25}
    - {x: 42.23205, y: 2, z: 2.25}
    - {x: 43.098076, y: 2, z: 2.25}
    - {x: 43.9641, y: 2, z: 2.25}
    - {x: 36.602886, y: 2, z: 3}
    - {x: 37.46891, y: 2, z: 3}
    - {x: 38.334938, y: 2, z: 3}
    - {x: 39.200962, y: 2, z: 3}
    - {x: 40.066986, y: 2, z: 3}
    - {x: 40.933014, y: 2, z: 3}
    - {x: 41.799038, y: 2, z: 3}
    - {x: 42.665062, y: 2, z: 3}
    - {x: 43.53109, y: 2, z: 3}
    - {x: 44.397114, y: 2, z: 3}
    pcBndIn:
    - {x: 42.665062, y: 2, z: 0}
    - {x: 41.799038, y: 2, z: 0}
    - {x: 40.933014, y: 2, z: 0}
    - {x: 40.066986, y: 2, z: 0}
    - {x: 39.200962, y: 2, z: 0}
    - {x: 38.334938, y: 2, z: 0}
    pcBndOut:
    - {x: 44.397114, y: 2, z: 3}
    - {x: 43.53109, y: 2, z: 3}
    - {x: 42.665062, y: 2, z: 3}
    - {x: 41.799038, y: 2, z: 3}
    - {x: 40.933014, y: 2, z: 3}
    - {x: 40.066986, y: 2, z: 3}
    - {x: 39.200962, y: 2, z: 3}
    - {x: 38.334938, y: 2, z: 3}
    - {x: 37.46891, y: 2, z: 3}
    - {x: 36.602886, y: 2, z: 3}
    - {x: 43.9641, y: 2, z: 2.25}
    - {x: 43.098076, y: 2, z: 2.25}
    - {x: 42.23205, y: 2, z: 2.25}
    - {x: 41.366024, y: 2, z: 2.25}
    - {x: 40.5, y: 2, z: 2.25}
    - {x: 39.633976, y: 2, z: 2.25}
    - {x: 38.76795, y: 2, z: 2.25}
    - {x: 37.901924, y: 2, z: 2.25}
    - {x: 37.0359, y: 2, z: 2.25}
    - {x: 36.169872, y: 2, z: 2.25}
    - {x: 44.397114, y: 2, z: 1.5}
    - {x: 43.53109, y: 2, z: 1.5}
    - {x: 42.665062, y: 2, z: 1.5}
    - {x: 41.799038, y: 2, z: 1.5}
    - {x: 40.933014, y: 2, z: 1.5}
    - {x: 40.066986, y: 2, z: 1.5}
    - {x: 39.200962, y: 2, z: 1.5}
    - {x: 38.334938, y: 2, z: 1.5}
    - {x: 37.46891, y: 2, z: 1.5}
    - {x: 36.602886, y: 2, z: 1.5}
    - {x: 43.9641, y: 2, z: 0.75}
    - {x: 43.098076, y: 2, z: 0.75}
    - {x: 42.23205, y: 2, z: 0.75}
    - {x: 41.366024, y: 2, z: 0.75}
    - {x: 40.5, y: 2, z: 0.75}
    - {x: 39.633976, y: 2, z: 0.75}
    - {x: 38.76795, y: 2, z: 0.75}
    - {x: 37.901924, y: 2, z: 0.75}
    - {x: 37.0359, y: 2, z: 0.75}
    - {x: 36.169872, y: 2, z: 0.75}
    - {x: 44.397114, y: 2, z: 0}
    - {x: 43.53109, y: 2, z: 0}
    - {x: 37.46891, y: 2, z: 0}
    - {x: 36.602886, y: 2, z: 0}
    - {x: 43.9641, y: 2, z: -0.75}
    - {x: 43.098076, y: 2, z: -0.75}
    - {x: 42.23205, y: 2, z: -0.75}
    - {x: 41.366024, y: 2, z: -0.75}
    - {x: 40.5, y: 2, z: -0.75}
    - {x: 39.633976, y: 2, z: -0.75}
    - {x: 38.76795, y: 2, z: -0.75}
    - {x: 37.901924, y: 2, z: -0.75}
    - {x: 37.0359, y: 2, z: -0.75}
    - {x: 36.169872, y: 2, z: -0.75}
    - {x: 44.397114, y: 2, z: -1.5}
    - {x: 43.53109, y: 2, z: -1.5}
    - {x: 42.665062, y: 2, z: -1.5}
    - {x: 41.799038, y: 2, z: -1.5}
    - {x: 40.933014, y: 2, z: -1.5}
    - {x: 40.066986, y: 2, z: -1.5}
    - {x: 39.200962, y: 2, z: -1.5}
    - {x: 38.334938, y: 2, z: -1.5}
    - {x: 37.46891, y: 2, z: -1.5}
    - {x: 36.602886, y: 2, z: -1.5}
    - {x: 43.9641, y: 2, z: -2.25}
    - {x: 43.098076, y: 2, z: -2.25}
    - {x: 42.23205, y: 2, z: -2.25}
    - {x: 41.366024, y: 2, z: -2.25}
    - {x: 40.5, y: 2, z: -2.25}
    - {x: 39.633976, y: 2, z: -2.25}
    - {x: 38.76795, y: 2, z: -2.25}
    - {x: 37.901924, y: 2, z: -2.25}
    - {x: 37.0359, y: 2, z: -2.25}
    - {x: 36.169872, y: 2, z: -2.25}
    - {x: 44.397114, y: 2, z: -3}
    - {x: 43.53109, y: 2, z: -3}
    - {x: 42.665062, y: 2, z: -3}
    - {x: 41.799038, y: 2, z: -3}
    - {x: 40.933014, y: 2, z: -3}
    - {x: 40.066986, y: 2, z: -3}
    - {x: 39.200962, y: 2, z: -3}
    - {x: 38.334938, y: 2, z: -3}
    - {x: 37.46891, y: 2, z: -3}
    - {x: 36.602886, y: 2, z: -3}
    - {x: 43.9641, y: 2, z: -3.75}
    - {x: 43.098076, y: 2, z: -3.75}
    - {x: 42.23205, y: 2, z: -3.75}
    - {x: 41.366024, y: 2, z: -3.75}
    - {x: 40.5, y: 2, z: -3.75}
    - {x: 39.633976, y: 2, z: -3.75}
    - {x: 38.76795, y: 2, z: -3.75}
    - {x: 37.901924, y: 2, z: -3.75}
    - {x: 37.0359, y: 2, z: -3.75}
    - {x: 36.169872, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 1
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 696151833}
  meshFilter: {fileID: 696151836}
  meshRenderer: {fileID: 696151835}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.09900001
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 40.5, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &696151835
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &696151836
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &858165925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 858165926}
  - component: {fileID: 858165929}
  - component: {fileID: 858165928}
  - component: {fileID: 858165927}
  m_Layer: 0
  m_Name: Radial_Focus_100_100
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &858165926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858165925}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 46.5, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &858165927
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858165925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 100
    focusStr: 100
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 42.169872, y: 2, z: -3.75}
    - {x: 43.0359, y: 2, z: -3.75}
    - {x: 43.901924, y: 2, z: -3.75}
    - {x: 44.76795, y: 2, z: -3.75}
    - {x: 45.633976, y: 2, z: -3.75}
    - {x: 46.5, y: 2, z: -3.75}
    - {x: 47.366024, y: 2, z: -3.75}
    - {x: 48.23205, y: 2, z: -3.75}
    - {x: 49.098076, y: 2, z: -3.75}
    - {x: 49.9641, y: 2, z: -3.75}
    - {x: 42.602886, y: 2, z: -3}
    - {x: 43.46891, y: 2, z: -3}
    - {x: 44.334938, y: 2, z: -3}
    - {x: 45.200962, y: 2, z: -3}
    - {x: 46.066986, y: 2, z: -3}
    - {x: 46.933014, y: 2, z: -3}
    - {x: 47.799038, y: 2, z: -3}
    - {x: 48.665062, y: 2, z: -3}
    - {x: 49.53109, y: 2, z: -3}
    - {x: 50.397114, y: 2, z: -3}
    - {x: 42.169872, y: 2, z: -2.25}
    - {x: 43.0359, y: 2, z: -2.25}
    - {x: 43.901924, y: 2, z: -2.25}
    - {x: 44.76795, y: 2, z: -2.25}
    - {x: 45.633976, y: 2, z: -2.25}
    - {x: 46.5, y: 2, z: -2.25}
    - {x: 47.366024, y: 2, z: -2.25}
    - {x: 48.23205, y: 2, z: -2.25}
    - {x: 49.098076, y: 2, z: -2.25}
    - {x: 49.9641, y: 2, z: -2.25}
    - {x: 42.602886, y: 2, z: -1.5}
    - {x: 43.46891, y: 2, z: -1.5}
    - {x: 44.334938, y: 2, z: -1.5}
    - {x: 45.200962, y: 2, z: -1.5}
    - {x: 46.066986, y: 2, z: -1.5}
    - {x: 46.933014, y: 2, z: -1.5}
    - {x: 47.799038, y: 2, z: -1.5}
    - {x: 48.665062, y: 2, z: -1.5}
    - {x: 49.53109, y: 2, z: -1.5}
    - {x: 50.397114, y: 2, z: -1.5}
    - {x: 42.169872, y: 2, z: -0.75}
    - {x: 43.0359, y: 2, z: -0.75}
    - {x: 43.901924, y: 2, z: -0.75}
    - {x: 44.76795, y: 2, z: -0.75}
    - {x: 45.633976, y: 2, z: -0.75}
    - {x: 46.5, y: 2, z: -0.75}
    - {x: 47.366024, y: 2, z: -0.75}
    - {x: 48.23205, y: 2, z: -0.75}
    - {x: 49.098076, y: 2, z: -0.75}
    - {x: 49.9641, y: 2, z: -0.75}
    - {x: 42.602886, y: 2, z: 0}
    - {x: 43.46891, y: 2, z: 0}
    - {x: 44.334938, y: 2, z: 0}
    - {x: 45.200962, y: 2, z: 0}
    - {x: 46.066986, y: 2, z: 0}
    - {x: 46.933014, y: 2, z: 0}
    - {x: 47.799038, y: 2, z: 0}
    - {x: 48.665062, y: 2, z: 0}
    - {x: 49.53109, y: 2, z: 0}
    - {x: 50.397114, y: 2, z: 0}
    - {x: 42.169872, y: 2, z: 0.75}
    - {x: 43.0359, y: 2, z: 0.75}
    - {x: 43.901924, y: 2, z: 0.75}
    - {x: 44.76795, y: 2, z: 0.75}
    - {x: 45.633976, y: 2, z: 0.75}
    - {x: 46.5, y: 2, z: 0.75}
    - {x: 47.366024, y: 2, z: 0.75}
    - {x: 48.23205, y: 2, z: 0.75}
    - {x: 49.098076, y: 2, z: 0.75}
    - {x: 49.9641, y: 2, z: 0.75}
    - {x: 42.602886, y: 2, z: 1.5}
    - {x: 43.46891, y: 2, z: 1.5}
    - {x: 44.334938, y: 2, z: 1.5}
    - {x: 45.200962, y: 2, z: 1.5}
    - {x: 46.066986, y: 2, z: 1.5}
    - {x: 46.933014, y: 2, z: 1.5}
    - {x: 47.799038, y: 2, z: 1.5}
    - {x: 48.665062, y: 2, z: 1.5}
    - {x: 49.53109, y: 2, z: 1.5}
    - {x: 50.397114, y: 2, z: 1.5}
    - {x: 42.169872, y: 2, z: 2.25}
    - {x: 43.0359, y: 2, z: 2.25}
    - {x: 43.901924, y: 2, z: 2.25}
    - {x: 44.76795, y: 2, z: 2.25}
    - {x: 45.633976, y: 2, z: 2.25}
    - {x: 46.5, y: 2, z: 2.25}
    - {x: 47.366024, y: 2, z: 2.25}
    - {x: 48.23205, y: 2, z: 2.25}
    - {x: 49.098076, y: 2, z: 2.25}
    - {x: 49.9641, y: 2, z: 2.25}
    - {x: 42.602886, y: 2, z: 3}
    - {x: 43.46891, y: 2, z: 3}
    - {x: 44.334938, y: 2, z: 3}
    - {x: 45.200962, y: 2, z: 3}
    - {x: 46.066986, y: 2, z: 3}
    - {x: 46.933014, y: 2, z: 3}
    - {x: 47.799038, y: 2, z: 3}
    - {x: 48.665062, y: 2, z: 3}
    - {x: 49.53109, y: 2, z: 3}
    - {x: 50.397114, y: 2, z: 3}
    pcBndIn:
    - {x: 48.665062, y: 2, z: 0}
    - {x: 47.799038, y: 2, z: 0}
    - {x: 46.933014, y: 2, z: 0}
    - {x: 46.066986, y: 2, z: 0}
    - {x: 45.200962, y: 2, z: 0}
    - {x: 44.334938, y: 2, z: 0}
    pcBndOut:
    - {x: 50.397114, y: 2, z: 3}
    - {x: 49.53109, y: 2, z: 3}
    - {x: 48.665062, y: 2, z: 3}
    - {x: 47.799038, y: 2, z: 3}
    - {x: 46.933014, y: 2, z: 3}
    - {x: 46.066986, y: 2, z: 3}
    - {x: 45.200962, y: 2, z: 3}
    - {x: 44.334938, y: 2, z: 3}
    - {x: 43.46891, y: 2, z: 3}
    - {x: 42.602886, y: 2, z: 3}
    - {x: 49.9641, y: 2, z: 2.25}
    - {x: 49.098076, y: 2, z: 2.25}
    - {x: 48.23205, y: 2, z: 2.25}
    - {x: 47.366024, y: 2, z: 2.25}
    - {x: 46.5, y: 2, z: 2.25}
    - {x: 45.633976, y: 2, z: 2.25}
    - {x: 44.76795, y: 2, z: 2.25}
    - {x: 43.901924, y: 2, z: 2.25}
    - {x: 43.0359, y: 2, z: 2.25}
    - {x: 42.169872, y: 2, z: 2.25}
    - {x: 50.397114, y: 2, z: 1.5}
    - {x: 49.53109, y: 2, z: 1.5}
    - {x: 48.665062, y: 2, z: 1.5}
    - {x: 47.799038, y: 2, z: 1.5}
    - {x: 46.933014, y: 2, z: 1.5}
    - {x: 46.066986, y: 2, z: 1.5}
    - {x: 45.200962, y: 2, z: 1.5}
    - {x: 44.334938, y: 2, z: 1.5}
    - {x: 43.46891, y: 2, z: 1.5}
    - {x: 42.602886, y: 2, z: 1.5}
    - {x: 49.9641, y: 2, z: 0.75}
    - {x: 49.098076, y: 2, z: 0.75}
    - {x: 48.23205, y: 2, z: 0.75}
    - {x: 47.366024, y: 2, z: 0.75}
    - {x: 46.5, y: 2, z: 0.75}
    - {x: 45.633976, y: 2, z: 0.75}
    - {x: 44.76795, y: 2, z: 0.75}
    - {x: 43.901924, y: 2, z: 0.75}
    - {x: 43.0359, y: 2, z: 0.75}
    - {x: 42.169872, y: 2, z: 0.75}
    - {x: 50.397114, y: 2, z: 0}
    - {x: 49.53109, y: 2, z: 0}
    - {x: 43.46891, y: 2, z: 0}
    - {x: 42.602886, y: 2, z: 0}
    - {x: 49.9641, y: 2, z: -0.75}
    - {x: 49.098076, y: 2, z: -0.75}
    - {x: 48.23205, y: 2, z: -0.75}
    - {x: 47.366024, y: 2, z: -0.75}
    - {x: 46.5, y: 2, z: -0.75}
    - {x: 45.633976, y: 2, z: -0.75}
    - {x: 44.76795, y: 2, z: -0.75}
    - {x: 43.901924, y: 2, z: -0.75}
    - {x: 43.0359, y: 2, z: -0.75}
    - {x: 42.169872, y: 2, z: -0.75}
    - {x: 50.397114, y: 2, z: -1.5}
    - {x: 49.53109, y: 2, z: -1.5}
    - {x: 48.665062, y: 2, z: -1.5}
    - {x: 47.799038, y: 2, z: -1.5}
    - {x: 46.933014, y: 2, z: -1.5}
    - {x: 46.066986, y: 2, z: -1.5}
    - {x: 45.200962, y: 2, z: -1.5}
    - {x: 44.334938, y: 2, z: -1.5}
    - {x: 43.46891, y: 2, z: -1.5}
    - {x: 42.602886, y: 2, z: -1.5}
    - {x: 49.9641, y: 2, z: -2.25}
    - {x: 49.098076, y: 2, z: -2.25}
    - {x: 48.23205, y: 2, z: -2.25}
    - {x: 47.366024, y: 2, z: -2.25}
    - {x: 46.5, y: 2, z: -2.25}
    - {x: 45.633976, y: 2, z: -2.25}
    - {x: 44.76795, y: 2, z: -2.25}
    - {x: 43.901924, y: 2, z: -2.25}
    - {x: 43.0359, y: 2, z: -2.25}
    - {x: 42.169872, y: 2, z: -2.25}
    - {x: 50.397114, y: 2, z: -3}
    - {x: 49.53109, y: 2, z: -3}
    - {x: 48.665062, y: 2, z: -3}
    - {x: 47.799038, y: 2, z: -3}
    - {x: 46.933014, y: 2, z: -3}
    - {x: 46.066986, y: 2, z: -3}
    - {x: 45.200962, y: 2, z: -3}
    - {x: 44.334938, y: 2, z: -3}
    - {x: 43.46891, y: 2, z: -3}
    - {x: 42.602886, y: 2, z: -3}
    - {x: 49.9641, y: 2, z: -3.75}
    - {x: 49.098076, y: 2, z: -3.75}
    - {x: 48.23205, y: 2, z: -3.75}
    - {x: 47.366024, y: 2, z: -3.75}
    - {x: 46.5, y: 2, z: -3.75}
    - {x: 45.633976, y: 2, z: -3.75}
    - {x: 44.76795, y: 2, z: -3.75}
    - {x: 43.901924, y: 2, z: -3.75}
    - {x: 43.0359, y: 2, z: -3.75}
    - {x: 42.169872, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 858165926}
  meshFilter: {fileID: 858165929}
  meshRenderer: {fileID: 858165928}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.088758625
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 46.5, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &858165928
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858165925}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &858165929
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 858165925}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &869776492
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 869776493}
  - component: {fileID: 869776496}
  - component: {fileID: 869776495}
  - component: {fileID: 869776494}
  m_Layer: 0
  m_Name: Radial_Twist_0_RandomRays_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &869776493
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 869776492}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 67.8, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &869776494
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 869776492}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 63.469875, y: 2, z: -3.75}
    - {x: 64.3359, y: 2, z: -3.75}
    - {x: 65.20193, y: 2, z: -3.75}
    - {x: 66.067955, y: 2, z: -3.75}
    - {x: 66.933975, y: 2, z: -3.75}
    - {x: 67.8, y: 2, z: -3.75}
    - {x: 68.66603, y: 2, z: -3.75}
    - {x: 69.53205, y: 2, z: -3.75}
    - {x: 70.39808, y: 2, z: -3.75}
    - {x: 71.26411, y: 2, z: -3.75}
    - {x: 63.90289, y: 2, z: -3}
    - {x: 64.76891, y: 2, z: -3}
    - {x: 65.63494, y: 2, z: -3}
    - {x: 66.50096, y: 2, z: -3}
    - {x: 67.36699, y: 2, z: -3}
    - {x: 68.23302, y: 2, z: -3}
    - {x: 69.099045, y: 2, z: -3}
    - {x: 69.965065, y: 2, z: -3}
    - {x: 70.83109, y: 2, z: -3}
    - {x: 71.69712, y: 2, z: -3}
    - {x: 63.469875, y: 2, z: -2.25}
    - {x: 64.3359, y: 2, z: -2.25}
    - {x: 65.20193, y: 2, z: -2.25}
    - {x: 66.067955, y: 2, z: -2.25}
    - {x: 66.933975, y: 2, z: -2.25}
    - {x: 67.8, y: 2, z: -2.25}
    - {x: 68.66603, y: 2, z: -2.25}
    - {x: 69.53205, y: 2, z: -2.25}
    - {x: 70.39808, y: 2, z: -2.25}
    - {x: 71.26411, y: 2, z: -2.25}
    - {x: 63.90289, y: 2, z: -1.5}
    - {x: 64.76891, y: 2, z: -1.5}
    - {x: 65.63494, y: 2, z: -1.5}
    - {x: 66.50096, y: 2, z: -1.5}
    - {x: 67.36699, y: 2, z: -1.5}
    - {x: 68.23302, y: 2, z: -1.5}
    - {x: 69.099045, y: 2, z: -1.5}
    - {x: 69.965065, y: 2, z: -1.5}
    - {x: 70.83109, y: 2, z: -1.5}
    - {x: 71.69712, y: 2, z: -1.5}
    - {x: 63.469875, y: 2, z: -0.75}
    - {x: 64.3359, y: 2, z: -0.75}
    - {x: 65.20193, y: 2, z: -0.75}
    - {x: 66.067955, y: 2, z: -0.75}
    - {x: 66.933975, y: 2, z: -0.75}
    - {x: 67.8, y: 2, z: -0.75}
    - {x: 68.66603, y: 2, z: -0.75}
    - {x: 69.53205, y: 2, z: -0.75}
    - {x: 70.39808, y: 2, z: -0.75}
    - {x: 71.26411, y: 2, z: -0.75}
    - {x: 63.90289, y: 2, z: 0}
    - {x: 64.76891, y: 2, z: 0}
    - {x: 65.63494, y: 2, z: 0}
    - {x: 66.50096, y: 2, z: 0}
    - {x: 67.36699, y: 2, z: 0}
    - {x: 68.23302, y: 2, z: 0}
    - {x: 69.099045, y: 2, z: 0}
    - {x: 69.965065, y: 2, z: 0}
    - {x: 70.83109, y: 2, z: 0}
    - {x: 71.69712, y: 2, z: 0}
    - {x: 63.469875, y: 2, z: 0.75}
    - {x: 64.3359, y: 2, z: 0.75}
    - {x: 65.20193, y: 2, z: 0.75}
    - {x: 66.067955, y: 2, z: 0.75}
    - {x: 66.933975, y: 2, z: 0.75}
    - {x: 67.8, y: 2, z: 0.75}
    - {x: 68.66603, y: 2, z: 0.75}
    - {x: 69.53205, y: 2, z: 0.75}
    - {x: 70.39808, y: 2, z: 0.75}
    - {x: 71.26411, y: 2, z: 0.75}
    - {x: 63.90289, y: 2, z: 1.5}
    - {x: 64.76891, y: 2, z: 1.5}
    - {x: 65.63494, y: 2, z: 1.5}
    - {x: 66.50096, y: 2, z: 1.5}
    - {x: 67.36699, y: 2, z: 1.5}
    - {x: 68.23302, y: 2, z: 1.5}
    - {x: 69.099045, y: 2, z: 1.5}
    - {x: 69.965065, y: 2, z: 1.5}
    - {x: 70.83109, y: 2, z: 1.5}
    - {x: 71.69712, y: 2, z: 1.5}
    - {x: 63.469875, y: 2, z: 2.25}
    - {x: 64.3359, y: 2, z: 2.25}
    - {x: 65.20193, y: 2, z: 2.25}
    - {x: 66.067955, y: 2, z: 2.25}
    - {x: 66.933975, y: 2, z: 2.25}
    - {x: 67.8, y: 2, z: 2.25}
    - {x: 68.66603, y: 2, z: 2.25}
    - {x: 69.53205, y: 2, z: 2.25}
    - {x: 70.39808, y: 2, z: 2.25}
    - {x: 71.26411, y: 2, z: 2.25}
    - {x: 63.90289, y: 2, z: 3}
    - {x: 64.76891, y: 2, z: 3}
    - {x: 65.63494, y: 2, z: 3}
    - {x: 66.50096, y: 2, z: 3}
    - {x: 67.36699, y: 2, z: 3}
    - {x: 68.23302, y: 2, z: 3}
    - {x: 69.099045, y: 2, z: 3}
    - {x: 69.965065, y: 2, z: 3}
    - {x: 70.83109, y: 2, z: 3}
    - {x: 71.69712, y: 2, z: 3}
    pcBndIn:
    - {x: 69.965065, y: 2, z: 0}
    - {x: 69.099045, y: 2, z: 0}
    - {x: 68.23302, y: 2, z: 0}
    - {x: 67.36699, y: 2, z: 0}
    - {x: 66.50096, y: 2, z: 0}
    - {x: 65.63494, y: 2, z: 0}
    pcBndOut:
    - {x: 71.69712, y: 2, z: 3}
    - {x: 70.83109, y: 2, z: 3}
    - {x: 69.965065, y: 2, z: 3}
    - {x: 69.099045, y: 2, z: 3}
    - {x: 68.23302, y: 2, z: 3}
    - {x: 67.36699, y: 2, z: 3}
    - {x: 66.50096, y: 2, z: 3}
    - {x: 65.63494, y: 2, z: 3}
    - {x: 64.76891, y: 2, z: 3}
    - {x: 63.90289, y: 2, z: 3}
    - {x: 71.26411, y: 2, z: 2.25}
    - {x: 70.39808, y: 2, z: 2.25}
    - {x: 69.53205, y: 2, z: 2.25}
    - {x: 68.66603, y: 2, z: 2.25}
    - {x: 67.8, y: 2, z: 2.25}
    - {x: 66.933975, y: 2, z: 2.25}
    - {x: 66.067955, y: 2, z: 2.25}
    - {x: 65.20193, y: 2, z: 2.25}
    - {x: 64.3359, y: 2, z: 2.25}
    - {x: 63.469875, y: 2, z: 2.25}
    - {x: 71.69712, y: 2, z: 1.5}
    - {x: 70.83109, y: 2, z: 1.5}
    - {x: 69.965065, y: 2, z: 1.5}
    - {x: 69.099045, y: 2, z: 1.5}
    - {x: 68.23302, y: 2, z: 1.5}
    - {x: 67.36699, y: 2, z: 1.5}
    - {x: 66.50096, y: 2, z: 1.5}
    - {x: 65.63494, y: 2, z: 1.5}
    - {x: 64.76891, y: 2, z: 1.5}
    - {x: 63.90289, y: 2, z: 1.5}
    - {x: 71.26411, y: 2, z: 0.75}
    - {x: 70.39808, y: 2, z: 0.75}
    - {x: 69.53205, y: 2, z: 0.75}
    - {x: 68.66603, y: 2, z: 0.75}
    - {x: 67.8, y: 2, z: 0.75}
    - {x: 66.933975, y: 2, z: 0.75}
    - {x: 66.067955, y: 2, z: 0.75}
    - {x: 65.20193, y: 2, z: 0.75}
    - {x: 64.3359, y: 2, z: 0.75}
    - {x: 63.469875, y: 2, z: 0.75}
    - {x: 71.69712, y: 2, z: 0}
    - {x: 70.83109, y: 2, z: 0}
    - {x: 64.76891, y: 2, z: 0}
    - {x: 63.90289, y: 2, z: 0}
    - {x: 71.26411, y: 2, z: -0.75}
    - {x: 70.39808, y: 2, z: -0.75}
    - {x: 69.53205, y: 2, z: -0.75}
    - {x: 68.66603, y: 2, z: -0.75}
    - {x: 67.8, y: 2, z: -0.75}
    - {x: 66.933975, y: 2, z: -0.75}
    - {x: 66.067955, y: 2, z: -0.75}
    - {x: 65.20193, y: 2, z: -0.75}
    - {x: 64.3359, y: 2, z: -0.75}
    - {x: 63.469875, y: 2, z: -0.75}
    - {x: 71.69712, y: 2, z: -1.5}
    - {x: 70.83109, y: 2, z: -1.5}
    - {x: 69.965065, y: 2, z: -1.5}
    - {x: 69.099045, y: 2, z: -1.5}
    - {x: 68.23302, y: 2, z: -1.5}
    - {x: 67.36699, y: 2, z: -1.5}
    - {x: 66.50096, y: 2, z: -1.5}
    - {x: 65.63494, y: 2, z: -1.5}
    - {x: 64.76891, y: 2, z: -1.5}
    - {x: 63.90289, y: 2, z: -1.5}
    - {x: 71.26411, y: 2, z: -2.25}
    - {x: 70.39808, y: 2, z: -2.25}
    - {x: 69.53205, y: 2, z: -2.25}
    - {x: 68.66603, y: 2, z: -2.25}
    - {x: 67.8, y: 2, z: -2.25}
    - {x: 66.933975, y: 2, z: -2.25}
    - {x: 66.067955, y: 2, z: -2.25}
    - {x: 65.20193, y: 2, z: -2.25}
    - {x: 64.3359, y: 2, z: -2.25}
    - {x: 63.469875, y: 2, z: -2.25}
    - {x: 71.69712, y: 2, z: -3}
    - {x: 70.83109, y: 2, z: -3}
    - {x: 69.965065, y: 2, z: -3}
    - {x: 69.099045, y: 2, z: -3}
    - {x: 68.23302, y: 2, z: -3}
    - {x: 67.36699, y: 2, z: -3}
    - {x: 66.50096, y: 2, z: -3}
    - {x: 65.63494, y: 2, z: -3}
    - {x: 64.76891, y: 2, z: -3}
    - {x: 63.90289, y: 2, z: -3}
    - {x: 71.26411, y: 2, z: -3.75}
    - {x: 70.39808, y: 2, z: -3.75}
    - {x: 69.53205, y: 2, z: -3.75}
    - {x: 68.66603, y: 2, z: -3.75}
    - {x: 67.8, y: 2, z: -3.75}
    - {x: 66.933975, y: 2, z: -3.75}
    - {x: 66.067955, y: 2, z: -3.75}
    - {x: 65.20193, y: 2, z: -3.75}
    - {x: 64.3359, y: 2, z: -3.75}
    - {x: 63.469875, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 869776493}
  meshFilter: {fileID: 869776496}
  meshRenderer: {fileID: 869776495}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.08534484
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 67.8, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &869776495
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 869776492}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &869776496
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 869776492}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1181678840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1181678841}
  - component: {fileID: 1181678844}
  - component: {fileID: 1181678843}
  - component: {fileID: 1181678842}
  m_Layer: 0
  m_Name: Radial_Ring10_Ray20_Div0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1181678841
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 12.599999, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1181678842
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 8.269873, y: 2, z: -3.75}
    - {x: 9.135898, y: 2, z: -3.75}
    - {x: 10.001924, y: 2, z: -3.75}
    - {x: 10.867949, y: 2, z: -3.75}
    - {x: 11.733974, y: 2, z: -3.75}
    - {x: 12.599999, y: 2, z: -3.75}
    - {x: 13.466024, y: 2, z: -3.75}
    - {x: 14.33205, y: 2, z: -3.75}
    - {x: 15.198075, y: 2, z: -3.75}
    - {x: 16.0641, y: 2, z: -3.75}
    - {x: 8.702886, y: 2, z: -3}
    - {x: 9.568911, y: 2, z: -3}
    - {x: 10.434937, y: 2, z: -3}
    - {x: 11.3009615, y: 2, z: -3}
    - {x: 12.166986, y: 2, z: -3}
    - {x: 13.033012, y: 2, z: -3}
    - {x: 13.899037, y: 2, z: -3}
    - {x: 14.765062, y: 2, z: -3}
    - {x: 15.631088, y: 2, z: -3}
    - {x: 16.497114, y: 2, z: -3}
    - {x: 8.269873, y: 2, z: -2.25}
    - {x: 9.135898, y: 2, z: -2.25}
    - {x: 10.001924, y: 2, z: -2.25}
    - {x: 10.867949, y: 2, z: -2.25}
    - {x: 11.733974, y: 2, z: -2.25}
    - {x: 12.599999, y: 2, z: -2.25}
    - {x: 13.466024, y: 2, z: -2.25}
    - {x: 14.33205, y: 2, z: -2.25}
    - {x: 15.198075, y: 2, z: -2.25}
    - {x: 16.0641, y: 2, z: -2.25}
    - {x: 8.702886, y: 2, z: -1.5}
    - {x: 9.568911, y: 2, z: -1.5}
    - {x: 10.434937, y: 2, z: -1.5}
    - {x: 11.3009615, y: 2, z: -1.5}
    - {x: 12.166986, y: 2, z: -1.5}
    - {x: 13.033012, y: 2, z: -1.5}
    - {x: 13.899037, y: 2, z: -1.5}
    - {x: 14.765062, y: 2, z: -1.5}
    - {x: 15.631088, y: 2, z: -1.5}
    - {x: 16.497114, y: 2, z: -1.5}
    - {x: 8.269873, y: 2, z: -0.75}
    - {x: 9.135898, y: 2, z: -0.75}
    - {x: 10.001924, y: 2, z: -0.75}
    - {x: 10.867949, y: 2, z: -0.75}
    - {x: 11.733974, y: 2, z: -0.75}
    - {x: 12.599999, y: 2, z: -0.75}
    - {x: 13.466024, y: 2, z: -0.75}
    - {x: 14.33205, y: 2, z: -0.75}
    - {x: 15.198075, y: 2, z: -0.75}
    - {x: 16.0641, y: 2, z: -0.75}
    - {x: 8.702886, y: 2, z: 0}
    - {x: 9.568911, y: 2, z: 0}
    - {x: 10.434937, y: 2, z: 0}
    - {x: 11.3009615, y: 2, z: 0}
    - {x: 12.166986, y: 2, z: 0}
    - {x: 13.033012, y: 2, z: 0}
    - {x: 13.899037, y: 2, z: 0}
    - {x: 14.765062, y: 2, z: 0}
    - {x: 15.631088, y: 2, z: 0}
    - {x: 16.497114, y: 2, z: 0}
    - {x: 8.269873, y: 2, z: 0.75}
    - {x: 9.135898, y: 2, z: 0.75}
    - {x: 10.001924, y: 2, z: 0.75}
    - {x: 10.867949, y: 2, z: 0.75}
    - {x: 11.733974, y: 2, z: 0.75}
    - {x: 12.599999, y: 2, z: 0.75}
    - {x: 13.466024, y: 2, z: 0.75}
    - {x: 14.33205, y: 2, z: 0.75}
    - {x: 15.198075, y: 2, z: 0.75}
    - {x: 16.0641, y: 2, z: 0.75}
    - {x: 8.702886, y: 2, z: 1.5}
    - {x: 9.568911, y: 2, z: 1.5}
    - {x: 10.434937, y: 2, z: 1.5}
    - {x: 11.3009615, y: 2, z: 1.5}
    - {x: 12.166986, y: 2, z: 1.5}
    - {x: 13.033012, y: 2, z: 1.5}
    - {x: 13.899037, y: 2, z: 1.5}
    - {x: 14.765062, y: 2, z: 1.5}
    - {x: 15.631088, y: 2, z: 1.5}
    - {x: 16.497114, y: 2, z: 1.5}
    - {x: 8.269873, y: 2, z: 2.25}
    - {x: 9.135898, y: 2, z: 2.25}
    - {x: 10.001924, y: 2, z: 2.25}
    - {x: 10.867949, y: 2, z: 2.25}
    - {x: 11.733974, y: 2, z: 2.25}
    - {x: 12.599999, y: 2, z: 2.25}
    - {x: 13.466024, y: 2, z: 2.25}
    - {x: 14.33205, y: 2, z: 2.25}
    - {x: 15.198075, y: 2, z: 2.25}
    - {x: 16.0641, y: 2, z: 2.25}
    - {x: 8.702886, y: 2, z: 3}
    - {x: 9.568911, y: 2, z: 3}
    - {x: 10.434937, y: 2, z: 3}
    - {x: 11.3009615, y: 2, z: 3}
    - {x: 12.166986, y: 2, z: 3}
    - {x: 13.033012, y: 2, z: 3}
    - {x: 13.899037, y: 2, z: 3}
    - {x: 14.765062, y: 2, z: 3}
    - {x: 15.631088, y: 2, z: 3}
    - {x: 16.497114, y: 2, z: 3}
    pcBndIn:
    - {x: 14.765062, y: 2, z: 0}
    - {x: 13.899037, y: 2, z: 0}
    - {x: 13.033012, y: 2, z: 0}
    - {x: 12.166986, y: 2, z: 0}
    - {x: 11.3009615, y: 2, z: 0}
    - {x: 10.434937, y: 2, z: 0}
    pcBndOut:
    - {x: 16.497114, y: 2, z: 3}
    - {x: 15.631088, y: 2, z: 3}
    - {x: 14.765062, y: 2, z: 3}
    - {x: 13.899037, y: 2, z: 3}
    - {x: 13.033012, y: 2, z: 3}
    - {x: 12.166986, y: 2, z: 3}
    - {x: 11.3009615, y: 2, z: 3}
    - {x: 10.434937, y: 2, z: 3}
    - {x: 9.568911, y: 2, z: 3}
    - {x: 8.702886, y: 2, z: 3}
    - {x: 16.0641, y: 2, z: 2.25}
    - {x: 15.198075, y: 2, z: 2.25}
    - {x: 14.33205, y: 2, z: 2.25}
    - {x: 13.466024, y: 2, z: 2.25}
    - {x: 12.599999, y: 2, z: 2.25}
    - {x: 11.733974, y: 2, z: 2.25}
    - {x: 10.867949, y: 2, z: 2.25}
    - {x: 10.001924, y: 2, z: 2.25}
    - {x: 9.135898, y: 2, z: 2.25}
    - {x: 8.269873, y: 2, z: 2.25}
    - {x: 16.497114, y: 2, z: 1.5}
    - {x: 15.631088, y: 2, z: 1.5}
    - {x: 14.765062, y: 2, z: 1.5}
    - {x: 13.899037, y: 2, z: 1.5}
    - {x: 13.033012, y: 2, z: 1.5}
    - {x: 12.166986, y: 2, z: 1.5}
    - {x: 11.3009615, y: 2, z: 1.5}
    - {x: 10.434937, y: 2, z: 1.5}
    - {x: 9.568911, y: 2, z: 1.5}
    - {x: 8.702886, y: 2, z: 1.5}
    - {x: 16.0641, y: 2, z: 0.75}
    - {x: 15.198075, y: 2, z: 0.75}
    - {x: 14.33205, y: 2, z: 0.75}
    - {x: 13.466024, y: 2, z: 0.75}
    - {x: 12.599999, y: 2, z: 0.75}
    - {x: 11.733974, y: 2, z: 0.75}
    - {x: 10.867949, y: 2, z: 0.75}
    - {x: 10.001924, y: 2, z: 0.75}
    - {x: 9.135898, y: 2, z: 0.75}
    - {x: 8.269873, y: 2, z: 0.75}
    - {x: 16.497114, y: 2, z: 0}
    - {x: 15.631088, y: 2, z: 0}
    - {x: 9.568911, y: 2, z: 0}
    - {x: 8.702886, y: 2, z: 0}
    - {x: 16.0641, y: 2, z: -0.75}
    - {x: 15.198075, y: 2, z: -0.75}
    - {x: 14.33205, y: 2, z: -0.75}
    - {x: 13.466024, y: 2, z: -0.75}
    - {x: 12.599999, y: 2, z: -0.75}
    - {x: 11.733974, y: 2, z: -0.75}
    - {x: 10.867949, y: 2, z: -0.75}
    - {x: 10.001924, y: 2, z: -0.75}
    - {x: 9.135898, y: 2, z: -0.75}
    - {x: 8.269873, y: 2, z: -0.75}
    - {x: 16.497114, y: 2, z: -1.5}
    - {x: 15.631088, y: 2, z: -1.5}
    - {x: 14.765062, y: 2, z: -1.5}
    - {x: 13.899037, y: 2, z: -1.5}
    - {x: 13.033012, y: 2, z: -1.5}
    - {x: 12.166986, y: 2, z: -1.5}
    - {x: 11.3009615, y: 2, z: -1.5}
    - {x: 10.434937, y: 2, z: -1.5}
    - {x: 9.568911, y: 2, z: -1.5}
    - {x: 8.702886, y: 2, z: -1.5}
    - {x: 16.0641, y: 2, z: -2.25}
    - {x: 15.198075, y: 2, z: -2.25}
    - {x: 14.33205, y: 2, z: -2.25}
    - {x: 13.466024, y: 2, z: -2.25}
    - {x: 12.599999, y: 2, z: -2.25}
    - {x: 11.733974, y: 2, z: -2.25}
    - {x: 10.867949, y: 2, z: -2.25}
    - {x: 10.001924, y: 2, z: -2.25}
    - {x: 9.135898, y: 2, z: -2.25}
    - {x: 8.269873, y: 2, z: -2.25}
    - {x: 16.497114, y: 2, z: -3}
    - {x: 15.631088, y: 2, z: -3}
    - {x: 14.765062, y: 2, z: -3}
    - {x: 13.899037, y: 2, z: -3}
    - {x: 13.033012, y: 2, z: -3}
    - {x: 12.166986, y: 2, z: -3}
    - {x: 11.3009615, y: 2, z: -3}
    - {x: 10.434937, y: 2, z: -3}
    - {x: 9.568911, y: 2, z: -3}
    - {x: 8.702886, y: 2, z: -3}
    - {x: 16.0641, y: 2, z: -3.75}
    - {x: 15.198075, y: 2, z: -3.75}
    - {x: 14.33205, y: 2, z: -3.75}
    - {x: 13.466024, y: 2, z: -3.75}
    - {x: 12.599999, y: 2, z: -3.75}
    - {x: 11.733974, y: 2, z: -3.75}
    - {x: 10.867949, y: 2, z: -3.75}
    - {x: 10.001924, y: 2, z: -3.75}
    - {x: 9.135898, y: 2, z: -3.75}
    - {x: 8.269873, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1181678841}
  meshFilter: {fileID: 1181678844}
  meshRenderer: {fileID: 1181678843}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.12972413
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 12.599999, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1181678843
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1181678844
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1296339193
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1296339194}
  - component: {fileID: 1296339197}
  - component: {fileID: 1296339196}
  - component: {fileID: 1296339195}
  m_Layer: 0
  m_Name: Radial_Center
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1296339194
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296339193}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 103.2, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1296339195
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296339193}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0.1
    restrictToPlane: 0
    rings: 10
    focus: 0
    focusStr: 100
    randomRings: 32
    rays: 20
    randomRays: 30
    twist: 4
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 98.86987, y: 2, z: -2.4478855}
    - {x: 99.73589, y: 2, z: -2.4478855}
    - {x: 100.60192, y: 2, z: -2.4478855}
    - {x: 101.46795, y: 2, z: -2.4478855}
    - {x: 102.33397, y: 2, z: -2.4478855}
    - {x: 103.2, y: 2, z: -2.4478855}
    - {x: 104.066025, y: 2, z: -2.4478855}
    - {x: 104.932045, y: 2, z: -2.4478855}
    - {x: 105.79807, y: 2, z: -2.4478855}
    - {x: 106.6641, y: 2, z: -2.4478855}
    - {x: 99.30288, y: 2, z: -1.6978855}
    - {x: 100.16891, y: 2, z: -1.6978855}
    - {x: 101.034935, y: 2, z: -1.6978855}
    - {x: 101.900955, y: 2, z: -1.6978855}
    - {x: 102.76698, y: 2, z: -1.6978855}
    - {x: 103.63301, y: 2, z: -1.6978855}
    - {x: 104.49904, y: 2, z: -1.6978855}
    - {x: 105.36506, y: 2, z: -1.6978855}
    - {x: 106.23109, y: 2, z: -1.6978855}
    - {x: 107.097115, y: 2, z: -1.6978855}
    - {x: 98.86987, y: 2, z: -0.9478855}
    - {x: 99.73589, y: 2, z: -0.9478855}
    - {x: 100.60192, y: 2, z: -0.9478855}
    - {x: 101.46795, y: 2, z: -0.9478855}
    - {x: 102.33397, y: 2, z: -0.9478855}
    - {x: 103.2, y: 2, z: -0.9478855}
    - {x: 104.066025, y: 2, z: -0.9478855}
    - {x: 104.932045, y: 2, z: -0.9478855}
    - {x: 105.79807, y: 2, z: -0.9478855}
    - {x: 106.6641, y: 2, z: -0.9478855}
    - {x: 99.30288, y: 2, z: -0.19788551}
    - {x: 100.16891, y: 2, z: -0.19788551}
    - {x: 101.034935, y: 2, z: -0.19788551}
    - {x: 101.900955, y: 2, z: -0.19788551}
    - {x: 102.76698, y: 2, z: -0.19788551}
    - {x: 103.63301, y: 2, z: -0.19788551}
    - {x: 104.49904, y: 2, z: -0.19788551}
    - {x: 105.36506, y: 2, z: -0.19788551}
    - {x: 106.23109, y: 2, z: -0.19788551}
    - {x: 107.097115, y: 2, z: -0.19788551}
    - {x: 98.86987, y: 2, z: 0.5521145}
    - {x: 99.73589, y: 2, z: 0.5521145}
    - {x: 100.60192, y: 2, z: 0.5521145}
    - {x: 101.46795, y: 2, z: 0.5521145}
    - {x: 102.33397, y: 2, z: 0.5521145}
    - {x: 103.2, y: 2, z: 0.5521145}
    - {x: 104.066025, y: 2, z: 0.5521145}
    - {x: 104.932045, y: 2, z: 0.5521145}
    - {x: 105.79807, y: 2, z: 0.5521145}
    - {x: 106.6641, y: 2, z: 0.5521145}
    - {x: 99.30288, y: 2, z: 1.3021145}
    - {x: 100.16891, y: 2, z: 1.3021145}
    - {x: 101.034935, y: 2, z: 1.3021145}
    - {x: 101.900955, y: 2, z: 1.3021145}
    - {x: 102.76698, y: 2, z: 1.3021145}
    - {x: 103.63301, y: 2, z: 1.3021145}
    - {x: 104.49904, y: 2, z: 1.3021145}
    - {x: 105.36506, y: 2, z: 1.3021145}
    - {x: 106.23109, y: 2, z: 1.3021145}
    - {x: 107.097115, y: 2, z: 1.3021145}
    - {x: 98.86987, y: 2, z: 2.0521145}
    - {x: 99.73589, y: 2, z: 2.0521145}
    - {x: 100.60192, y: 2, z: 2.0521145}
    - {x: 101.46795, y: 2, z: 2.0521145}
    - {x: 102.33397, y: 2, z: 2.0521145}
    - {x: 103.2, y: 2, z: 2.0521145}
    - {x: 104.066025, y: 2, z: 2.0521145}
    - {x: 104.932045, y: 2, z: 2.0521145}
    - {x: 105.79807, y: 2, z: 2.0521145}
    - {x: 106.6641, y: 2, z: 2.0521145}
    - {x: 99.30288, y: 2, z: 2.8021145}
    - {x: 100.16891, y: 2, z: 2.8021145}
    - {x: 101.034935, y: 2, z: 2.8021145}
    - {x: 101.900955, y: 2, z: 2.8021145}
    - {x: 102.76698, y: 2, z: 2.8021145}
    - {x: 103.63301, y: 2, z: 2.8021145}
    - {x: 104.49904, y: 2, z: 2.8021145}
    - {x: 105.36506, y: 2, z: 2.8021145}
    - {x: 106.23109, y: 2, z: 2.8021145}
    - {x: 107.097115, y: 2, z: 2.8021145}
    - {x: 98.86987, y: 2, z: 3.5521145}
    - {x: 99.73589, y: 2, z: 3.5521145}
    - {x: 100.60192, y: 2, z: 3.5521145}
    - {x: 101.46795, y: 2, z: 3.5521145}
    - {x: 102.33397, y: 2, z: 3.5521145}
    - {x: 103.2, y: 2, z: 3.5521145}
    - {x: 104.066025, y: 2, z: 3.5521145}
    - {x: 104.932045, y: 2, z: 3.5521145}
    - {x: 105.79807, y: 2, z: 3.5521145}
    - {x: 106.6641, y: 2, z: 3.5521145}
    - {x: 99.30288, y: 2, z: 4.3021145}
    - {x: 100.16891, y: 2, z: 4.3021145}
    - {x: 101.034935, y: 2, z: 4.3021145}
    - {x: 101.900955, y: 2, z: 4.3021145}
    - {x: 102.76698, y: 2, z: 4.3021145}
    - {x: 103.63301, y: 2, z: 4.3021145}
    - {x: 104.49904, y: 2, z: 4.3021145}
    - {x: 105.36506, y: 2, z: 4.3021145}
    - {x: 106.23109, y: 2, z: 4.3021145}
    - {x: 107.097115, y: 2, z: 4.3021145}
    pcBndIn:
    - {x: 105.36506, y: 2, z: -0.19788551}
    - {x: 104.49904, y: 2, z: -0.19788551}
    - {x: 103.63301, y: 2, z: -0.19788551}
    - {x: 102.76698, y: 2, z: -0.19788551}
    - {x: 101.900955, y: 2, z: -0.19788551}
    - {x: 101.034935, y: 2, z: -0.19788551}
    pcBndOut:
    - {x: 107.097115, y: 2, z: 4.3021145}
    - {x: 106.23109, y: 2, z: 4.3021145}
    - {x: 105.36506, y: 2, z: 4.3021145}
    - {x: 104.49904, y: 2, z: 4.3021145}
    - {x: 103.63301, y: 2, z: 4.3021145}
    - {x: 102.76698, y: 2, z: 4.3021145}
    - {x: 101.900955, y: 2, z: 4.3021145}
    - {x: 101.034935, y: 2, z: 4.3021145}
    - {x: 100.16891, y: 2, z: 4.3021145}
    - {x: 99.30288, y: 2, z: 4.3021145}
    - {x: 106.6641, y: 2, z: 3.5521145}
    - {x: 105.79807, y: 2, z: 3.5521145}
    - {x: 104.932045, y: 2, z: 3.5521145}
    - {x: 104.066025, y: 2, z: 3.5521145}
    - {x: 103.2, y: 2, z: 3.5521145}
    - {x: 102.33397, y: 2, z: 3.5521145}
    - {x: 101.46795, y: 2, z: 3.5521145}
    - {x: 100.60192, y: 2, z: 3.5521145}
    - {x: 99.73589, y: 2, z: 3.5521145}
    - {x: 98.86987, y: 2, z: 3.5521145}
    - {x: 107.097115, y: 2, z: 2.8021145}
    - {x: 106.23109, y: 2, z: 2.8021145}
    - {x: 105.36506, y: 2, z: 2.8021145}
    - {x: 104.49904, y: 2, z: 2.8021145}
    - {x: 103.63301, y: 2, z: 2.8021145}
    - {x: 102.76698, y: 2, z: 2.8021145}
    - {x: 101.900955, y: 2, z: 2.8021145}
    - {x: 101.034935, y: 2, z: 2.8021145}
    - {x: 100.16891, y: 2, z: 2.8021145}
    - {x: 99.30288, y: 2, z: 2.8021145}
    - {x: 106.6641, y: 2, z: 2.0521145}
    - {x: 105.79807, y: 2, z: 2.0521145}
    - {x: 104.932045, y: 2, z: 2.0521145}
    - {x: 104.066025, y: 2, z: 2.0521145}
    - {x: 103.2, y: 2, z: 2.0521145}
    - {x: 102.33397, y: 2, z: 2.0521145}
    - {x: 101.46795, y: 2, z: 2.0521145}
    - {x: 100.60192, y: 2, z: 2.0521145}
    - {x: 99.73589, y: 2, z: 2.0521145}
    - {x: 98.86987, y: 2, z: 2.0521145}
    - {x: 107.097115, y: 2, z: 1.3021145}
    - {x: 106.23109, y: 2, z: 1.3021145}
    - {x: 105.36506, y: 2, z: 1.3021145}
    - {x: 104.49904, y: 2, z: 1.3021145}
    - {x: 103.63301, y: 2, z: 1.3021145}
    - {x: 102.76698, y: 2, z: 1.3021145}
    - {x: 101.900955, y: 2, z: 1.3021145}
    - {x: 101.034935, y: 2, z: 1.3021145}
    - {x: 100.16891, y: 2, z: 1.3021145}
    - {x: 99.30288, y: 2, z: 1.3021145}
    - {x: 106.6641, y: 2, z: 0.5521145}
    - {x: 105.79807, y: 2, z: 0.5521145}
    - {x: 104.932045, y: 2, z: 0.5521145}
    - {x: 104.066025, y: 2, z: 0.5521145}
    - {x: 103.2, y: 2, z: 0.5521145}
    - {x: 102.33397, y: 2, z: 0.5521145}
    - {x: 101.46795, y: 2, z: 0.5521145}
    - {x: 100.60192, y: 2, z: 0.5521145}
    - {x: 99.73589, y: 2, z: 0.5521145}
    - {x: 98.86987, y: 2, z: 0.5521145}
    - {x: 107.097115, y: 2, z: -0.19788551}
    - {x: 106.23109, y: 2, z: -0.19788551}
    - {x: 100.16891, y: 2, z: -0.19788551}
    - {x: 99.30288, y: 2, z: -0.19788551}
    - {x: 106.6641, y: 2, z: -0.9478855}
    - {x: 105.79807, y: 2, z: -0.9478855}
    - {x: 104.932045, y: 2, z: -0.9478855}
    - {x: 104.066025, y: 2, z: -0.9478855}
    - {x: 103.2, y: 2, z: -0.9478855}
    - {x: 102.33397, y: 2, z: -0.9478855}
    - {x: 101.46795, y: 2, z: -0.9478855}
    - {x: 100.60192, y: 2, z: -0.9478855}
    - {x: 99.73589, y: 2, z: -0.9478855}
    - {x: 98.86987, y: 2, z: -0.9478855}
    - {x: 107.097115, y: 2, z: -1.6978855}
    - {x: 106.23109, y: 2, z: -1.6978855}
    - {x: 105.36506, y: 2, z: -1.6978855}
    - {x: 104.49904, y: 2, z: -1.6978855}
    - {x: 103.63301, y: 2, z: -1.6978855}
    - {x: 102.76698, y: 2, z: -1.6978855}
    - {x: 101.900955, y: 2, z: -1.6978855}
    - {x: 101.034935, y: 2, z: -1.6978855}
    - {x: 100.16891, y: 2, z: -1.6978855}
    - {x: 99.30288, y: 2, z: -1.6978855}
    - {x: 106.6641, y: 2, z: -2.4478855}
    - {x: 105.79807, y: 2, z: -2.4478855}
    - {x: 104.932045, y: 2, z: -2.4478855}
    - {x: 104.066025, y: 2, z: -2.4478855}
    - {x: 103.2, y: 2, z: -2.4478855}
    - {x: 102.33397, y: 2, z: -2.4478855}
    - {x: 101.46795, y: 2, z: -2.4478855}
    - {x: 100.60192, y: 2, z: -2.4478855}
    - {x: 99.73589, y: 2, z: -2.4478855}
    - {x: 98.86987, y: 2, z: -2.4478855}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 1
  centerPosition: {x: 0, y: 0, z: 2.604229}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1296339194}
  meshFilter: {fileID: 1296339197}
  meshRenderer: {fileID: 1296339196}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.21741177
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 103.2, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1296339196
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296339193}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1296339197
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296339193}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1482064831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1482064832}
  - component: {fileID: 1482064835}
  - component: {fileID: 1482064834}
  - component: {fileID: 1482064833}
  m_Layer: 0
  m_Name: Radial_Twist_45
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1482064832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1482064831}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 87.3, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1482064833
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1482064831}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 45
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 82.96988, y: 2, z: -3.75}
    - {x: 83.8359, y: 2, z: -3.75}
    - {x: 84.70193, y: 2, z: -3.75}
    - {x: 85.567955, y: 2, z: -3.75}
    - {x: 86.433975, y: 2, z: -3.75}
    - {x: 87.3, y: 2, z: -3.75}
    - {x: 88.16603, y: 2, z: -3.75}
    - {x: 89.03205, y: 2, z: -3.75}
    - {x: 89.89808, y: 2, z: -3.75}
    - {x: 90.76411, y: 2, z: -3.75}
    - {x: 83.402885, y: 2, z: -3}
    - {x: 84.26891, y: 2, z: -3}
    - {x: 85.13494, y: 2, z: -3}
    - {x: 86.00096, y: 2, z: -3}
    - {x: 86.86699, y: 2, z: -3}
    - {x: 87.73302, y: 2, z: -3}
    - {x: 88.599045, y: 2, z: -3}
    - {x: 89.465065, y: 2, z: -3}
    - {x: 90.33109, y: 2, z: -3}
    - {x: 91.19712, y: 2, z: -3}
    - {x: 82.96988, y: 2, z: -2.25}
    - {x: 83.8359, y: 2, z: -2.25}
    - {x: 84.70193, y: 2, z: -2.25}
    - {x: 85.567955, y: 2, z: -2.25}
    - {x: 86.433975, y: 2, z: -2.25}
    - {x: 87.3, y: 2, z: -2.25}
    - {x: 88.16603, y: 2, z: -2.25}
    - {x: 89.03205, y: 2, z: -2.25}
    - {x: 89.89808, y: 2, z: -2.25}
    - {x: 90.76411, y: 2, z: -2.25}
    - {x: 83.402885, y: 2, z: -1.5}
    - {x: 84.26891, y: 2, z: -1.5}
    - {x: 85.13494, y: 2, z: -1.5}
    - {x: 86.00096, y: 2, z: -1.5}
    - {x: 86.86699, y: 2, z: -1.5}
    - {x: 87.73302, y: 2, z: -1.5}
    - {x: 88.599045, y: 2, z: -1.5}
    - {x: 89.465065, y: 2, z: -1.5}
    - {x: 90.33109, y: 2, z: -1.5}
    - {x: 91.19712, y: 2, z: -1.5}
    - {x: 82.96988, y: 2, z: -0.75}
    - {x: 83.8359, y: 2, z: -0.75}
    - {x: 84.70193, y: 2, z: -0.75}
    - {x: 85.567955, y: 2, z: -0.75}
    - {x: 86.433975, y: 2, z: -0.75}
    - {x: 87.3, y: 2, z: -0.75}
    - {x: 88.16603, y: 2, z: -0.75}
    - {x: 89.03205, y: 2, z: -0.75}
    - {x: 89.89808, y: 2, z: -0.75}
    - {x: 90.76411, y: 2, z: -0.75}
    - {x: 83.402885, y: 2, z: 0}
    - {x: 84.26891, y: 2, z: 0}
    - {x: 85.13494, y: 2, z: 0}
    - {x: 86.00096, y: 2, z: 0}
    - {x: 86.86699, y: 2, z: 0}
    - {x: 87.73302, y: 2, z: 0}
    - {x: 88.599045, y: 2, z: 0}
    - {x: 89.465065, y: 2, z: 0}
    - {x: 90.33109, y: 2, z: 0}
    - {x: 91.19712, y: 2, z: 0}
    - {x: 82.96988, y: 2, z: 0.75}
    - {x: 83.8359, y: 2, z: 0.75}
    - {x: 84.70193, y: 2, z: 0.75}
    - {x: 85.567955, y: 2, z: 0.75}
    - {x: 86.433975, y: 2, z: 0.75}
    - {x: 87.3, y: 2, z: 0.75}
    - {x: 88.16603, y: 2, z: 0.75}
    - {x: 89.03205, y: 2, z: 0.75}
    - {x: 89.89808, y: 2, z: 0.75}
    - {x: 90.76411, y: 2, z: 0.75}
    - {x: 83.402885, y: 2, z: 1.5}
    - {x: 84.26891, y: 2, z: 1.5}
    - {x: 85.13494, y: 2, z: 1.5}
    - {x: 86.00096, y: 2, z: 1.5}
    - {x: 86.86699, y: 2, z: 1.5}
    - {x: 87.73302, y: 2, z: 1.5}
    - {x: 88.599045, y: 2, z: 1.5}
    - {x: 89.465065, y: 2, z: 1.5}
    - {x: 90.33109, y: 2, z: 1.5}
    - {x: 91.19712, y: 2, z: 1.5}
    - {x: 82.96988, y: 2, z: 2.25}
    - {x: 83.8359, y: 2, z: 2.25}
    - {x: 84.70193, y: 2, z: 2.25}
    - {x: 85.567955, y: 2, z: 2.25}
    - {x: 86.433975, y: 2, z: 2.25}
    - {x: 87.3, y: 2, z: 2.25}
    - {x: 88.16603, y: 2, z: 2.25}
    - {x: 89.03205, y: 2, z: 2.25}
    - {x: 89.89808, y: 2, z: 2.25}
    - {x: 90.76411, y: 2, z: 2.25}
    - {x: 83.402885, y: 2, z: 3}
    - {x: 84.26891, y: 2, z: 3}
    - {x: 85.13494, y: 2, z: 3}
    - {x: 86.00096, y: 2, z: 3}
    - {x: 86.86699, y: 2, z: 3}
    - {x: 87.73302, y: 2, z: 3}
    - {x: 88.599045, y: 2, z: 3}
    - {x: 89.465065, y: 2, z: 3}
    - {x: 90.33109, y: 2, z: 3}
    - {x: 91.19712, y: 2, z: 3}
    pcBndIn:
    - {x: 89.465065, y: 2, z: 0}
    - {x: 88.599045, y: 2, z: 0}
    - {x: 87.73302, y: 2, z: 0}
    - {x: 86.86699, y: 2, z: 0}
    - {x: 86.00096, y: 2, z: 0}
    - {x: 85.13494, y: 2, z: 0}
    pcBndOut:
    - {x: 91.19712, y: 2, z: 3}
    - {x: 90.33109, y: 2, z: 3}
    - {x: 89.465065, y: 2, z: 3}
    - {x: 88.599045, y: 2, z: 3}
    - {x: 87.73302, y: 2, z: 3}
    - {x: 86.86699, y: 2, z: 3}
    - {x: 86.00096, y: 2, z: 3}
    - {x: 85.13494, y: 2, z: 3}
    - {x: 84.26891, y: 2, z: 3}
    - {x: 83.402885, y: 2, z: 3}
    - {x: 90.76411, y: 2, z: 2.25}
    - {x: 89.89808, y: 2, z: 2.25}
    - {x: 89.03205, y: 2, z: 2.25}
    - {x: 88.16603, y: 2, z: 2.25}
    - {x: 87.3, y: 2, z: 2.25}
    - {x: 86.433975, y: 2, z: 2.25}
    - {x: 85.567955, y: 2, z: 2.25}
    - {x: 84.70193, y: 2, z: 2.25}
    - {x: 83.8359, y: 2, z: 2.25}
    - {x: 82.96988, y: 2, z: 2.25}
    - {x: 91.19712, y: 2, z: 1.5}
    - {x: 90.33109, y: 2, z: 1.5}
    - {x: 89.465065, y: 2, z: 1.5}
    - {x: 88.599045, y: 2, z: 1.5}
    - {x: 87.73302, y: 2, z: 1.5}
    - {x: 86.86699, y: 2, z: 1.5}
    - {x: 86.00096, y: 2, z: 1.5}
    - {x: 85.13494, y: 2, z: 1.5}
    - {x: 84.26891, y: 2, z: 1.5}
    - {x: 83.402885, y: 2, z: 1.5}
    - {x: 90.76411, y: 2, z: 0.75}
    - {x: 89.89808, y: 2, z: 0.75}
    - {x: 89.03205, y: 2, z: 0.75}
    - {x: 88.16603, y: 2, z: 0.75}
    - {x: 87.3, y: 2, z: 0.75}
    - {x: 86.433975, y: 2, z: 0.75}
    - {x: 85.567955, y: 2, z: 0.75}
    - {x: 84.70193, y: 2, z: 0.75}
    - {x: 83.8359, y: 2, z: 0.75}
    - {x: 82.96988, y: 2, z: 0.75}
    - {x: 91.19712, y: 2, z: 0}
    - {x: 90.33109, y: 2, z: 0}
    - {x: 84.26891, y: 2, z: 0}
    - {x: 83.402885, y: 2, z: 0}
    - {x: 90.76411, y: 2, z: -0.75}
    - {x: 89.89808, y: 2, z: -0.75}
    - {x: 89.03205, y: 2, z: -0.75}
    - {x: 88.16603, y: 2, z: -0.75}
    - {x: 87.3, y: 2, z: -0.75}
    - {x: 86.433975, y: 2, z: -0.75}
    - {x: 85.567955, y: 2, z: -0.75}
    - {x: 84.70193, y: 2, z: -0.75}
    - {x: 83.8359, y: 2, z: -0.75}
    - {x: 82.96988, y: 2, z: -0.75}
    - {x: 91.19712, y: 2, z: -1.5}
    - {x: 90.33109, y: 2, z: -1.5}
    - {x: 89.465065, y: 2, z: -1.5}
    - {x: 88.599045, y: 2, z: -1.5}
    - {x: 87.73302, y: 2, z: -1.5}
    - {x: 86.86699, y: 2, z: -1.5}
    - {x: 86.00096, y: 2, z: -1.5}
    - {x: 85.13494, y: 2, z: -1.5}
    - {x: 84.26891, y: 2, z: -1.5}
    - {x: 83.402885, y: 2, z: -1.5}
    - {x: 90.76411, y: 2, z: -2.25}
    - {x: 89.89808, y: 2, z: -2.25}
    - {x: 89.03205, y: 2, z: -2.25}
    - {x: 88.16603, y: 2, z: -2.25}
    - {x: 87.3, y: 2, z: -2.25}
    - {x: 86.433975, y: 2, z: -2.25}
    - {x: 85.567955, y: 2, z: -2.25}
    - {x: 84.70193, y: 2, z: -2.25}
    - {x: 83.8359, y: 2, z: -2.25}
    - {x: 82.96988, y: 2, z: -2.25}
    - {x: 91.19712, y: 2, z: -3}
    - {x: 90.33109, y: 2, z: -3}
    - {x: 89.465065, y: 2, z: -3}
    - {x: 88.599045, y: 2, z: -3}
    - {x: 87.73302, y: 2, z: -3}
    - {x: 86.86699, y: 2, z: -3}
    - {x: 86.00096, y: 2, z: -3}
    - {x: 85.13494, y: 2, z: -3}
    - {x: 84.26891, y: 2, z: -3}
    - {x: 83.402885, y: 2, z: -3}
    - {x: 90.76411, y: 2, z: -3.75}
    - {x: 89.89808, y: 2, z: -3.75}
    - {x: 89.03205, y: 2, z: -3.75}
    - {x: 88.16603, y: 2, z: -3.75}
    - {x: 87.3, y: 2, z: -3.75}
    - {x: 86.433975, y: 2, z: -3.75}
    - {x: 85.567955, y: 2, z: -3.75}
    - {x: 84.70193, y: 2, z: -3.75}
    - {x: 83.8359, y: 2, z: -3.75}
    - {x: 82.96988, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1482064832}
  meshFilter: {fileID: 1482064835}
  meshRenderer: {fileID: 1482064834}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.102413796
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 87.3, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1482064834
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1482064831}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1482064835
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1482064831}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1719300433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1719300434}
  - component: {fileID: 1719300437}
  - component: {fileID: 1719300436}
  - component: {fileID: 1719300435}
  m_Layer: 0
  m_Name: Radial_Focus_0_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1719300434
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 34.199997, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1719300435
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 29.86987, y: 2, z: -3.75}
    - {x: 30.735895, y: 2, z: -3.75}
    - {x: 31.601921, y: 2, z: -3.75}
    - {x: 32.467945, y: 2, z: -3.75}
    - {x: 33.333973, y: 2, z: -3.75}
    - {x: 34.199997, y: 2, z: -3.75}
    - {x: 35.06602, y: 2, z: -3.75}
    - {x: 35.93205, y: 2, z: -3.75}
    - {x: 36.798073, y: 2, z: -3.75}
    - {x: 37.664097, y: 2, z: -3.75}
    - {x: 30.302883, y: 2, z: -3}
    - {x: 31.168907, y: 2, z: -3}
    - {x: 32.034935, y: 2, z: -3}
    - {x: 32.90096, y: 2, z: -3}
    - {x: 33.766983, y: 2, z: -3}
    - {x: 34.63301, y: 2, z: -3}
    - {x: 35.499035, y: 2, z: -3}
    - {x: 36.36506, y: 2, z: -3}
    - {x: 37.231087, y: 2, z: -3}
    - {x: 38.09711, y: 2, z: -3}
    - {x: 29.86987, y: 2, z: -2.25}
    - {x: 30.735895, y: 2, z: -2.25}
    - {x: 31.601921, y: 2, z: -2.25}
    - {x: 32.467945, y: 2, z: -2.25}
    - {x: 33.333973, y: 2, z: -2.25}
    - {x: 34.199997, y: 2, z: -2.25}
    - {x: 35.06602, y: 2, z: -2.25}
    - {x: 35.93205, y: 2, z: -2.25}
    - {x: 36.798073, y: 2, z: -2.25}
    - {x: 37.664097, y: 2, z: -2.25}
    - {x: 30.302883, y: 2, z: -1.5}
    - {x: 31.168907, y: 2, z: -1.5}
    - {x: 32.034935, y: 2, z: -1.5}
    - {x: 32.90096, y: 2, z: -1.5}
    - {x: 33.766983, y: 2, z: -1.5}
    - {x: 34.63301, y: 2, z: -1.5}
    - {x: 35.499035, y: 2, z: -1.5}
    - {x: 36.36506, y: 2, z: -1.5}
    - {x: 37.231087, y: 2, z: -1.5}
    - {x: 38.09711, y: 2, z: -1.5}
    - {x: 29.86987, y: 2, z: -0.75}
    - {x: 30.735895, y: 2, z: -0.75}
    - {x: 31.601921, y: 2, z: -0.75}
    - {x: 32.467945, y: 2, z: -0.75}
    - {x: 33.333973, y: 2, z: -0.75}
    - {x: 34.199997, y: 2, z: -0.75}
    - {x: 35.06602, y: 2, z: -0.75}
    - {x: 35.93205, y: 2, z: -0.75}
    - {x: 36.798073, y: 2, z: -0.75}
    - {x: 37.664097, y: 2, z: -0.75}
    - {x: 30.302883, y: 2, z: 0}
    - {x: 31.168907, y: 2, z: 0}
    - {x: 32.034935, y: 2, z: 0}
    - {x: 32.90096, y: 2, z: 0}
    - {x: 33.766983, y: 2, z: 0}
    - {x: 34.63301, y: 2, z: 0}
    - {x: 35.499035, y: 2, z: 0}
    - {x: 36.36506, y: 2, z: 0}
    - {x: 37.231087, y: 2, z: 0}
    - {x: 38.09711, y: 2, z: 0}
    - {x: 29.86987, y: 2, z: 0.75}
    - {x: 30.735895, y: 2, z: 0.75}
    - {x: 31.601921, y: 2, z: 0.75}
    - {x: 32.467945, y: 2, z: 0.75}
    - {x: 33.333973, y: 2, z: 0.75}
    - {x: 34.199997, y: 2, z: 0.75}
    - {x: 35.06602, y: 2, z: 0.75}
    - {x: 35.93205, y: 2, z: 0.75}
    - {x: 36.798073, y: 2, z: 0.75}
    - {x: 37.664097, y: 2, z: 0.75}
    - {x: 30.302883, y: 2, z: 1.5}
    - {x: 31.168907, y: 2, z: 1.5}
    - {x: 32.034935, y: 2, z: 1.5}
    - {x: 32.90096, y: 2, z: 1.5}
    - {x: 33.766983, y: 2, z: 1.5}
    - {x: 34.63301, y: 2, z: 1.5}
    - {x: 35.499035, y: 2, z: 1.5}
    - {x: 36.36506, y: 2, z: 1.5}
    - {x: 37.231087, y: 2, z: 1.5}
    - {x: 38.09711, y: 2, z: 1.5}
    - {x: 29.86987, y: 2, z: 2.25}
    - {x: 30.735895, y: 2, z: 2.25}
    - {x: 31.601921, y: 2, z: 2.25}
    - {x: 32.467945, y: 2, z: 2.25}
    - {x: 33.333973, y: 2, z: 2.25}
    - {x: 34.199997, y: 2, z: 2.25}
    - {x: 35.06602, y: 2, z: 2.25}
    - {x: 35.93205, y: 2, z: 2.25}
    - {x: 36.798073, y: 2, z: 2.25}
    - {x: 37.664097, y: 2, z: 2.25}
    - {x: 30.302883, y: 2, z: 3}
    - {x: 31.168907, y: 2, z: 3}
    - {x: 32.034935, y: 2, z: 3}
    - {x: 32.90096, y: 2, z: 3}
    - {x: 33.766983, y: 2, z: 3}
    - {x: 34.63301, y: 2, z: 3}
    - {x: 35.499035, y: 2, z: 3}
    - {x: 36.36506, y: 2, z: 3}
    - {x: 37.231087, y: 2, z: 3}
    - {x: 38.09711, y: 2, z: 3}
    pcBndIn:
    - {x: 36.36506, y: 2, z: 0}
    - {x: 35.499035, y: 2, z: 0}
    - {x: 34.63301, y: 2, z: 0}
    - {x: 33.766983, y: 2, z: 0}
    - {x: 32.90096, y: 2, z: 0}
    - {x: 32.034935, y: 2, z: 0}
    pcBndOut:
    - {x: 38.09711, y: 2, z: 3}
    - {x: 37.231087, y: 2, z: 3}
    - {x: 36.36506, y: 2, z: 3}
    - {x: 35.499035, y: 2, z: 3}
    - {x: 34.63301, y: 2, z: 3}
    - {x: 33.766983, y: 2, z: 3}
    - {x: 32.90096, y: 2, z: 3}
    - {x: 32.034935, y: 2, z: 3}
    - {x: 31.168907, y: 2, z: 3}
    - {x: 30.302883, y: 2, z: 3}
    - {x: 37.664097, y: 2, z: 2.25}
    - {x: 36.798073, y: 2, z: 2.25}
    - {x: 35.93205, y: 2, z: 2.25}
    - {x: 35.06602, y: 2, z: 2.25}
    - {x: 34.199997, y: 2, z: 2.25}
    - {x: 33.333973, y: 2, z: 2.25}
    - {x: 32.467945, y: 2, z: 2.25}
    - {x: 31.601921, y: 2, z: 2.25}
    - {x: 30.735895, y: 2, z: 2.25}
    - {x: 29.86987, y: 2, z: 2.25}
    - {x: 38.09711, y: 2, z: 1.5}
    - {x: 37.231087, y: 2, z: 1.5}
    - {x: 36.36506, y: 2, z: 1.5}
    - {x: 35.499035, y: 2, z: 1.5}
    - {x: 34.63301, y: 2, z: 1.5}
    - {x: 33.766983, y: 2, z: 1.5}
    - {x: 32.90096, y: 2, z: 1.5}
    - {x: 32.034935, y: 2, z: 1.5}
    - {x: 31.168907, y: 2, z: 1.5}
    - {x: 30.302883, y: 2, z: 1.5}
    - {x: 37.664097, y: 2, z: 0.75}
    - {x: 36.798073, y: 2, z: 0.75}
    - {x: 35.93205, y: 2, z: 0.75}
    - {x: 35.06602, y: 2, z: 0.75}
    - {x: 34.199997, y: 2, z: 0.75}
    - {x: 33.333973, y: 2, z: 0.75}
    - {x: 32.467945, y: 2, z: 0.75}
    - {x: 31.601921, y: 2, z: 0.75}
    - {x: 30.735895, y: 2, z: 0.75}
    - {x: 29.86987, y: 2, z: 0.75}
    - {x: 38.09711, y: 2, z: 0}
    - {x: 37.231087, y: 2, z: 0}
    - {x: 31.168907, y: 2, z: 0}
    - {x: 30.302883, y: 2, z: 0}
    - {x: 37.664097, y: 2, z: -0.75}
    - {x: 36.798073, y: 2, z: -0.75}
    - {x: 35.93205, y: 2, z: -0.75}
    - {x: 35.06602, y: 2, z: -0.75}
    - {x: 34.199997, y: 2, z: -0.75}
    - {x: 33.333973, y: 2, z: -0.75}
    - {x: 32.467945, y: 2, z: -0.75}
    - {x: 31.601921, y: 2, z: -0.75}
    - {x: 30.735895, y: 2, z: -0.75}
    - {x: 29.86987, y: 2, z: -0.75}
    - {x: 38.09711, y: 2, z: -1.5}
    - {x: 37.231087, y: 2, z: -1.5}
    - {x: 36.36506, y: 2, z: -1.5}
    - {x: 35.499035, y: 2, z: -1.5}
    - {x: 34.63301, y: 2, z: -1.5}
    - {x: 33.766983, y: 2, z: -1.5}
    - {x: 32.90096, y: 2, z: -1.5}
    - {x: 32.034935, y: 2, z: -1.5}
    - {x: 31.168907, y: 2, z: -1.5}
    - {x: 30.302883, y: 2, z: -1.5}
    - {x: 37.664097, y: 2, z: -2.25}
    - {x: 36.798073, y: 2, z: -2.25}
    - {x: 35.93205, y: 2, z: -2.25}
    - {x: 35.06602, y: 2, z: -2.25}
    - {x: 34.199997, y: 2, z: -2.25}
    - {x: 33.333973, y: 2, z: -2.25}
    - {x: 32.467945, y: 2, z: -2.25}
    - {x: 31.601921, y: 2, z: -2.25}
    - {x: 30.735895, y: 2, z: -2.25}
    - {x: 29.86987, y: 2, z: -2.25}
    - {x: 38.09711, y: 2, z: -3}
    - {x: 37.231087, y: 2, z: -3}
    - {x: 36.36506, y: 2, z: -3}
    - {x: 35.499035, y: 2, z: -3}
    - {x: 34.63301, y: 2, z: -3}
    - {x: 33.766983, y: 2, z: -3}
    - {x: 32.90096, y: 2, z: -3}
    - {x: 32.034935, y: 2, z: -3}
    - {x: 31.168907, y: 2, z: -3}
    - {x: 30.302883, y: 2, z: -3}
    - {x: 37.664097, y: 2, z: -3.75}
    - {x: 36.798073, y: 2, z: -3.75}
    - {x: 35.93205, y: 2, z: -3.75}
    - {x: 35.06602, y: 2, z: -3.75}
    - {x: 34.199997, y: 2, z: -3.75}
    - {x: 33.333973, y: 2, z: -3.75}
    - {x: 32.467945, y: 2, z: -3.75}
    - {x: 31.601921, y: 2, z: -3.75}
    - {x: 30.735895, y: 2, z: -3.75}
    - {x: 29.86987, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 1
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1719300434}
  meshFilter: {fileID: 1719300437}
  meshRenderer: {fileID: 1719300436}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.12289656
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 34.199997, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1719300436
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1719300437
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &1762396893
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 15.2336445
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!1 &1780556219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1780556220}
  - component: {fileID: 1780556223}
  - component: {fileID: 1780556222}
  - component: {fileID: 1780556221}
  m_Layer: 0
  m_Name: Radial_RandomRings_75
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1780556220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780556219}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 60, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1780556221
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780556219}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 75
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 55.669872, y: 2, z: -3.75}
    - {x: 56.5359, y: 2, z: -3.75}
    - {x: 57.401924, y: 2, z: -3.75}
    - {x: 58.26795, y: 2, z: -3.75}
    - {x: 59.133976, y: 2, z: -3.75}
    - {x: 60, y: 2, z: -3.75}
    - {x: 60.866024, y: 2, z: -3.75}
    - {x: 61.73205, y: 2, z: -3.75}
    - {x: 62.598076, y: 2, z: -3.75}
    - {x: 63.4641, y: 2, z: -3.75}
    - {x: 56.102886, y: 2, z: -3}
    - {x: 56.96891, y: 2, z: -3}
    - {x: 57.834938, y: 2, z: -3}
    - {x: 58.700962, y: 2, z: -3}
    - {x: 59.566986, y: 2, z: -3}
    - {x: 60.433014, y: 2, z: -3}
    - {x: 61.299038, y: 2, z: -3}
    - {x: 62.165062, y: 2, z: -3}
    - {x: 63.03109, y: 2, z: -3}
    - {x: 63.897114, y: 2, z: -3}
    - {x: 55.669872, y: 2, z: -2.25}
    - {x: 56.5359, y: 2, z: -2.25}
    - {x: 57.401924, y: 2, z: -2.25}
    - {x: 58.26795, y: 2, z: -2.25}
    - {x: 59.133976, y: 2, z: -2.25}
    - {x: 60, y: 2, z: -2.25}
    - {x: 60.866024, y: 2, z: -2.25}
    - {x: 61.73205, y: 2, z: -2.25}
    - {x: 62.598076, y: 2, z: -2.25}
    - {x: 63.4641, y: 2, z: -2.25}
    - {x: 56.102886, y: 2, z: -1.5}
    - {x: 56.96891, y: 2, z: -1.5}
    - {x: 57.834938, y: 2, z: -1.5}
    - {x: 58.700962, y: 2, z: -1.5}
    - {x: 59.566986, y: 2, z: -1.5}
    - {x: 60.433014, y: 2, z: -1.5}
    - {x: 61.299038, y: 2, z: -1.5}
    - {x: 62.165062, y: 2, z: -1.5}
    - {x: 63.03109, y: 2, z: -1.5}
    - {x: 63.897114, y: 2, z: -1.5}
    - {x: 55.669872, y: 2, z: -0.75}
    - {x: 56.5359, y: 2, z: -0.75}
    - {x: 57.401924, y: 2, z: -0.75}
    - {x: 58.26795, y: 2, z: -0.75}
    - {x: 59.133976, y: 2, z: -0.75}
    - {x: 60, y: 2, z: -0.75}
    - {x: 60.866024, y: 2, z: -0.75}
    - {x: 61.73205, y: 2, z: -0.75}
    - {x: 62.598076, y: 2, z: -0.75}
    - {x: 63.4641, y: 2, z: -0.75}
    - {x: 56.102886, y: 2, z: 0}
    - {x: 56.96891, y: 2, z: 0}
    - {x: 57.834938, y: 2, z: 0}
    - {x: 58.700962, y: 2, z: 0}
    - {x: 59.566986, y: 2, z: 0}
    - {x: 60.433014, y: 2, z: 0}
    - {x: 61.299038, y: 2, z: 0}
    - {x: 62.165062, y: 2, z: 0}
    - {x: 63.03109, y: 2, z: 0}
    - {x: 63.897114, y: 2, z: 0}
    - {x: 55.669872, y: 2, z: 0.75}
    - {x: 56.5359, y: 2, z: 0.75}
    - {x: 57.401924, y: 2, z: 0.75}
    - {x: 58.26795, y: 2, z: 0.75}
    - {x: 59.133976, y: 2, z: 0.75}
    - {x: 60, y: 2, z: 0.75}
    - {x: 60.866024, y: 2, z: 0.75}
    - {x: 61.73205, y: 2, z: 0.75}
    - {x: 62.598076, y: 2, z: 0.75}
    - {x: 63.4641, y: 2, z: 0.75}
    - {x: 56.102886, y: 2, z: 1.5}
    - {x: 56.96891, y: 2, z: 1.5}
    - {x: 57.834938, y: 2, z: 1.5}
    - {x: 58.700962, y: 2, z: 1.5}
    - {x: 59.566986, y: 2, z: 1.5}
    - {x: 60.433014, y: 2, z: 1.5}
    - {x: 61.299038, y: 2, z: 1.5}
    - {x: 62.165062, y: 2, z: 1.5}
    - {x: 63.03109, y: 2, z: 1.5}
    - {x: 63.897114, y: 2, z: 1.5}
    - {x: 55.669872, y: 2, z: 2.25}
    - {x: 56.5359, y: 2, z: 2.25}
    - {x: 57.401924, y: 2, z: 2.25}
    - {x: 58.26795, y: 2, z: 2.25}
    - {x: 59.133976, y: 2, z: 2.25}
    - {x: 60, y: 2, z: 2.25}
    - {x: 60.866024, y: 2, z: 2.25}
    - {x: 61.73205, y: 2, z: 2.25}
    - {x: 62.598076, y: 2, z: 2.25}
    - {x: 63.4641, y: 2, z: 2.25}
    - {x: 56.102886, y: 2, z: 3}
    - {x: 56.96891, y: 2, z: 3}
    - {x: 57.834938, y: 2, z: 3}
    - {x: 58.700962, y: 2, z: 3}
    - {x: 59.566986, y: 2, z: 3}
    - {x: 60.433014, y: 2, z: 3}
    - {x: 61.299038, y: 2, z: 3}
    - {x: 62.165062, y: 2, z: 3}
    - {x: 63.03109, y: 2, z: 3}
    - {x: 63.897114, y: 2, z: 3}
    pcBndIn:
    - {x: 62.165062, y: 2, z: 0}
    - {x: 61.299038, y: 2, z: 0}
    - {x: 60.433014, y: 2, z: 0}
    - {x: 59.566986, y: 2, z: 0}
    - {x: 58.700962, y: 2, z: 0}
    - {x: 57.834938, y: 2, z: 0}
    pcBndOut:
    - {x: 63.897114, y: 2, z: 3}
    - {x: 63.03109, y: 2, z: 3}
    - {x: 62.165062, y: 2, z: 3}
    - {x: 61.299038, y: 2, z: 3}
    - {x: 60.433014, y: 2, z: 3}
    - {x: 59.566986, y: 2, z: 3}
    - {x: 58.700962, y: 2, z: 3}
    - {x: 57.834938, y: 2, z: 3}
    - {x: 56.96891, y: 2, z: 3}
    - {x: 56.102886, y: 2, z: 3}
    - {x: 63.4641, y: 2, z: 2.25}
    - {x: 62.598076, y: 2, z: 2.25}
    - {x: 61.73205, y: 2, z: 2.25}
    - {x: 60.866024, y: 2, z: 2.25}
    - {x: 60, y: 2, z: 2.25}
    - {x: 59.133976, y: 2, z: 2.25}
    - {x: 58.26795, y: 2, z: 2.25}
    - {x: 57.401924, y: 2, z: 2.25}
    - {x: 56.5359, y: 2, z: 2.25}
    - {x: 55.669872, y: 2, z: 2.25}
    - {x: 63.897114, y: 2, z: 1.5}
    - {x: 63.03109, y: 2, z: 1.5}
    - {x: 62.165062, y: 2, z: 1.5}
    - {x: 61.299038, y: 2, z: 1.5}
    - {x: 60.433014, y: 2, z: 1.5}
    - {x: 59.566986, y: 2, z: 1.5}
    - {x: 58.700962, y: 2, z: 1.5}
    - {x: 57.834938, y: 2, z: 1.5}
    - {x: 56.96891, y: 2, z: 1.5}
    - {x: 56.102886, y: 2, z: 1.5}
    - {x: 63.4641, y: 2, z: 0.75}
    - {x: 62.598076, y: 2, z: 0.75}
    - {x: 61.73205, y: 2, z: 0.75}
    - {x: 60.866024, y: 2, z: 0.75}
    - {x: 60, y: 2, z: 0.75}
    - {x: 59.133976, y: 2, z: 0.75}
    - {x: 58.26795, y: 2, z: 0.75}
    - {x: 57.401924, y: 2, z: 0.75}
    - {x: 56.5359, y: 2, z: 0.75}
    - {x: 55.669872, y: 2, z: 0.75}
    - {x: 63.897114, y: 2, z: 0}
    - {x: 63.03109, y: 2, z: 0}
    - {x: 56.96891, y: 2, z: 0}
    - {x: 56.102886, y: 2, z: 0}
    - {x: 63.4641, y: 2, z: -0.75}
    - {x: 62.598076, y: 2, z: -0.75}
    - {x: 61.73205, y: 2, z: -0.75}
    - {x: 60.866024, y: 2, z: -0.75}
    - {x: 60, y: 2, z: -0.75}
    - {x: 59.133976, y: 2, z: -0.75}
    - {x: 58.26795, y: 2, z: -0.75}
    - {x: 57.401924, y: 2, z: -0.75}
    - {x: 56.5359, y: 2, z: -0.75}
    - {x: 55.669872, y: 2, z: -0.75}
    - {x: 63.897114, y: 2, z: -1.5}
    - {x: 63.03109, y: 2, z: -1.5}
    - {x: 62.165062, y: 2, z: -1.5}
    - {x: 61.299038, y: 2, z: -1.5}
    - {x: 60.433014, y: 2, z: -1.5}
    - {x: 59.566986, y: 2, z: -1.5}
    - {x: 58.700962, y: 2, z: -1.5}
    - {x: 57.834938, y: 2, z: -1.5}
    - {x: 56.96891, y: 2, z: -1.5}
    - {x: 56.102886, y: 2, z: -1.5}
    - {x: 63.4641, y: 2, z: -2.25}
    - {x: 62.598076, y: 2, z: -2.25}
    - {x: 61.73205, y: 2, z: -2.25}
    - {x: 60.866024, y: 2, z: -2.25}
    - {x: 60, y: 2, z: -2.25}
    - {x: 59.133976, y: 2, z: -2.25}
    - {x: 58.26795, y: 2, z: -2.25}
    - {x: 57.401924, y: 2, z: -2.25}
    - {x: 56.5359, y: 2, z: -2.25}
    - {x: 55.669872, y: 2, z: -2.25}
    - {x: 63.897114, y: 2, z: -3}
    - {x: 63.03109, y: 2, z: -3}
    - {x: 62.165062, y: 2, z: -3}
    - {x: 61.299038, y: 2, z: -3}
    - {x: 60.433014, y: 2, z: -3}
    - {x: 59.566986, y: 2, z: -3}
    - {x: 58.700962, y: 2, z: -3}
    - {x: 57.834938, y: 2, z: -3}
    - {x: 56.96891, y: 2, z: -3}
    - {x: 56.102886, y: 2, z: -3}
    - {x: 63.4641, y: 2, z: -3.75}
    - {x: 62.598076, y: 2, z: -3.75}
    - {x: 61.73205, y: 2, z: -3.75}
    - {x: 60.866024, y: 2, z: -3.75}
    - {x: 60, y: 2, z: -3.75}
    - {x: 59.133976, y: 2, z: -3.75}
    - {x: 58.26795, y: 2, z: -3.75}
    - {x: 57.401924, y: 2, z: -3.75}
    - {x: 56.5359, y: 2, z: -3.75}
    - {x: 55.669872, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1780556220}
  meshFilter: {fileID: 1780556223}
  meshRenderer: {fileID: 1780556222}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.075103454
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 60, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1780556222
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780556219}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1780556223
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780556219}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1876691644
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1876691645}
  - component: {fileID: 1876691648}
  - component: {fileID: 1876691647}
  - component: {fileID: 1876691646}
  m_Layer: 0
  m_Name: Radial_RandomRings_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1876691645
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876691644}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 54, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1876691646
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876691644}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 0
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 49.669872, y: 2, z: -3.75}
    - {x: 50.5359, y: 2, z: -3.75}
    - {x: 51.401924, y: 2, z: -3.75}
    - {x: 52.26795, y: 2, z: -3.75}
    - {x: 53.133976, y: 2, z: -3.75}
    - {x: 54, y: 2, z: -3.75}
    - {x: 54.866024, y: 2, z: -3.75}
    - {x: 55.73205, y: 2, z: -3.75}
    - {x: 56.598076, y: 2, z: -3.75}
    - {x: 57.4641, y: 2, z: -3.75}
    - {x: 50.102886, y: 2, z: -3}
    - {x: 50.96891, y: 2, z: -3}
    - {x: 51.834938, y: 2, z: -3}
    - {x: 52.700962, y: 2, z: -3}
    - {x: 53.566986, y: 2, z: -3}
    - {x: 54.433014, y: 2, z: -3}
    - {x: 55.299038, y: 2, z: -3}
    - {x: 56.165062, y: 2, z: -3}
    - {x: 57.03109, y: 2, z: -3}
    - {x: 57.897114, y: 2, z: -3}
    - {x: 49.669872, y: 2, z: -2.25}
    - {x: 50.5359, y: 2, z: -2.25}
    - {x: 51.401924, y: 2, z: -2.25}
    - {x: 52.26795, y: 2, z: -2.25}
    - {x: 53.133976, y: 2, z: -2.25}
    - {x: 54, y: 2, z: -2.25}
    - {x: 54.866024, y: 2, z: -2.25}
    - {x: 55.73205, y: 2, z: -2.25}
    - {x: 56.598076, y: 2, z: -2.25}
    - {x: 57.4641, y: 2, z: -2.25}
    - {x: 50.102886, y: 2, z: -1.5}
    - {x: 50.96891, y: 2, z: -1.5}
    - {x: 51.834938, y: 2, z: -1.5}
    - {x: 52.700962, y: 2, z: -1.5}
    - {x: 53.566986, y: 2, z: -1.5}
    - {x: 54.433014, y: 2, z: -1.5}
    - {x: 55.299038, y: 2, z: -1.5}
    - {x: 56.165062, y: 2, z: -1.5}
    - {x: 57.03109, y: 2, z: -1.5}
    - {x: 57.897114, y: 2, z: -1.5}
    - {x: 49.669872, y: 2, z: -0.75}
    - {x: 50.5359, y: 2, z: -0.75}
    - {x: 51.401924, y: 2, z: -0.75}
    - {x: 52.26795, y: 2, z: -0.75}
    - {x: 53.133976, y: 2, z: -0.75}
    - {x: 54, y: 2, z: -0.75}
    - {x: 54.866024, y: 2, z: -0.75}
    - {x: 55.73205, y: 2, z: -0.75}
    - {x: 56.598076, y: 2, z: -0.75}
    - {x: 57.4641, y: 2, z: -0.75}
    - {x: 50.102886, y: 2, z: 0}
    - {x: 50.96891, y: 2, z: 0}
    - {x: 51.834938, y: 2, z: 0}
    - {x: 52.700962, y: 2, z: 0}
    - {x: 53.566986, y: 2, z: 0}
    - {x: 54.433014, y: 2, z: 0}
    - {x: 55.299038, y: 2, z: 0}
    - {x: 56.165062, y: 2, z: 0}
    - {x: 57.03109, y: 2, z: 0}
    - {x: 57.897114, y: 2, z: 0}
    - {x: 49.669872, y: 2, z: 0.75}
    - {x: 50.5359, y: 2, z: 0.75}
    - {x: 51.401924, y: 2, z: 0.75}
    - {x: 52.26795, y: 2, z: 0.75}
    - {x: 53.133976, y: 2, z: 0.75}
    - {x: 54, y: 2, z: 0.75}
    - {x: 54.866024, y: 2, z: 0.75}
    - {x: 55.73205, y: 2, z: 0.75}
    - {x: 56.598076, y: 2, z: 0.75}
    - {x: 57.4641, y: 2, z: 0.75}
    - {x: 50.102886, y: 2, z: 1.5}
    - {x: 50.96891, y: 2, z: 1.5}
    - {x: 51.834938, y: 2, z: 1.5}
    - {x: 52.700962, y: 2, z: 1.5}
    - {x: 53.566986, y: 2, z: 1.5}
    - {x: 54.433014, y: 2, z: 1.5}
    - {x: 55.299038, y: 2, z: 1.5}
    - {x: 56.165062, y: 2, z: 1.5}
    - {x: 57.03109, y: 2, z: 1.5}
    - {x: 57.897114, y: 2, z: 1.5}
    - {x: 49.669872, y: 2, z: 2.25}
    - {x: 50.5359, y: 2, z: 2.25}
    - {x: 51.401924, y: 2, z: 2.25}
    - {x: 52.26795, y: 2, z: 2.25}
    - {x: 53.133976, y: 2, z: 2.25}
    - {x: 54, y: 2, z: 2.25}
    - {x: 54.866024, y: 2, z: 2.25}
    - {x: 55.73205, y: 2, z: 2.25}
    - {x: 56.598076, y: 2, z: 2.25}
    - {x: 57.4641, y: 2, z: 2.25}
    - {x: 50.102886, y: 2, z: 3}
    - {x: 50.96891, y: 2, z: 3}
    - {x: 51.834938, y: 2, z: 3}
    - {x: 52.700962, y: 2, z: 3}
    - {x: 53.566986, y: 2, z: 3}
    - {x: 54.433014, y: 2, z: 3}
    - {x: 55.299038, y: 2, z: 3}
    - {x: 56.165062, y: 2, z: 3}
    - {x: 57.03109, y: 2, z: 3}
    - {x: 57.897114, y: 2, z: 3}
    pcBndIn:
    - {x: 56.165062, y: 2, z: 0}
    - {x: 55.299038, y: 2, z: 0}
    - {x: 54.433014, y: 2, z: 0}
    - {x: 53.566986, y: 2, z: 0}
    - {x: 52.700962, y: 2, z: 0}
    - {x: 51.834938, y: 2, z: 0}
    pcBndOut:
    - {x: 57.897114, y: 2, z: 3}
    - {x: 57.03109, y: 2, z: 3}
    - {x: 56.165062, y: 2, z: 3}
    - {x: 55.299038, y: 2, z: 3}
    - {x: 54.433014, y: 2, z: 3}
    - {x: 53.566986, y: 2, z: 3}
    - {x: 52.700962, y: 2, z: 3}
    - {x: 51.834938, y: 2, z: 3}
    - {x: 50.96891, y: 2, z: 3}
    - {x: 50.102886, y: 2, z: 3}
    - {x: 57.4641, y: 2, z: 2.25}
    - {x: 56.598076, y: 2, z: 2.25}
    - {x: 55.73205, y: 2, z: 2.25}
    - {x: 54.866024, y: 2, z: 2.25}
    - {x: 54, y: 2, z: 2.25}
    - {x: 53.133976, y: 2, z: 2.25}
    - {x: 52.26795, y: 2, z: 2.25}
    - {x: 51.401924, y: 2, z: 2.25}
    - {x: 50.5359, y: 2, z: 2.25}
    - {x: 49.669872, y: 2, z: 2.25}
    - {x: 57.897114, y: 2, z: 1.5}
    - {x: 57.03109, y: 2, z: 1.5}
    - {x: 56.165062, y: 2, z: 1.5}
    - {x: 55.299038, y: 2, z: 1.5}
    - {x: 54.433014, y: 2, z: 1.5}
    - {x: 53.566986, y: 2, z: 1.5}
    - {x: 52.700962, y: 2, z: 1.5}
    - {x: 51.834938, y: 2, z: 1.5}
    - {x: 50.96891, y: 2, z: 1.5}
    - {x: 50.102886, y: 2, z: 1.5}
    - {x: 57.4641, y: 2, z: 0.75}
    - {x: 56.598076, y: 2, z: 0.75}
    - {x: 55.73205, y: 2, z: 0.75}
    - {x: 54.866024, y: 2, z: 0.75}
    - {x: 54, y: 2, z: 0.75}
    - {x: 53.133976, y: 2, z: 0.75}
    - {x: 52.26795, y: 2, z: 0.75}
    - {x: 51.401924, y: 2, z: 0.75}
    - {x: 50.5359, y: 2, z: 0.75}
    - {x: 49.669872, y: 2, z: 0.75}
    - {x: 57.897114, y: 2, z: 0}
    - {x: 57.03109, y: 2, z: 0}
    - {x: 50.96891, y: 2, z: 0}
    - {x: 50.102886, y: 2, z: 0}
    - {x: 57.4641, y: 2, z: -0.75}
    - {x: 56.598076, y: 2, z: -0.75}
    - {x: 55.73205, y: 2, z: -0.75}
    - {x: 54.866024, y: 2, z: -0.75}
    - {x: 54, y: 2, z: -0.75}
    - {x: 53.133976, y: 2, z: -0.75}
    - {x: 52.26795, y: 2, z: -0.75}
    - {x: 51.401924, y: 2, z: -0.75}
    - {x: 50.5359, y: 2, z: -0.75}
    - {x: 49.669872, y: 2, z: -0.75}
    - {x: 57.897114, y: 2, z: -1.5}
    - {x: 57.03109, y: 2, z: -1.5}
    - {x: 56.165062, y: 2, z: -1.5}
    - {x: 55.299038, y: 2, z: -1.5}
    - {x: 54.433014, y: 2, z: -1.5}
    - {x: 53.566986, y: 2, z: -1.5}
    - {x: 52.700962, y: 2, z: -1.5}
    - {x: 51.834938, y: 2, z: -1.5}
    - {x: 50.96891, y: 2, z: -1.5}
    - {x: 50.102886, y: 2, z: -1.5}
    - {x: 57.4641, y: 2, z: -2.25}
    - {x: 56.598076, y: 2, z: -2.25}
    - {x: 55.73205, y: 2, z: -2.25}
    - {x: 54.866024, y: 2, z: -2.25}
    - {x: 54, y: 2, z: -2.25}
    - {x: 53.133976, y: 2, z: -2.25}
    - {x: 52.26795, y: 2, z: -2.25}
    - {x: 51.401924, y: 2, z: -2.25}
    - {x: 50.5359, y: 2, z: -2.25}
    - {x: 49.669872, y: 2, z: -2.25}
    - {x: 57.897114, y: 2, z: -3}
    - {x: 57.03109, y: 2, z: -3}
    - {x: 56.165062, y: 2, z: -3}
    - {x: 55.299038, y: 2, z: -3}
    - {x: 54.433014, y: 2, z: -3}
    - {x: 53.566986, y: 2, z: -3}
    - {x: 52.700962, y: 2, z: -3}
    - {x: 51.834938, y: 2, z: -3}
    - {x: 50.96891, y: 2, z: -3}
    - {x: 50.102886, y: 2, z: -3}
    - {x: 57.4641, y: 2, z: -3.75}
    - {x: 56.598076, y: 2, z: -3.75}
    - {x: 55.73205, y: 2, z: -3.75}
    - {x: 54.866024, y: 2, z: -3.75}
    - {x: 54, y: 2, z: -3.75}
    - {x: 53.133976, y: 2, z: -3.75}
    - {x: 52.26795, y: 2, z: -3.75}
    - {x: 51.401924, y: 2, z: -3.75}
    - {x: 50.5359, y: 2, z: -3.75}
    - {x: 49.669872, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1876691645}
  meshFilter: {fileID: 1876691648}
  meshRenderer: {fileID: 1876691647}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.058034483
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 54, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1876691647
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876691644}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1876691648
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876691644}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1901299097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1901299098}
  - component: {fileID: 1901299101}
  - component: {fileID: 1901299100}
  - component: {fileID: 1901299099}
  m_Layer: 0
  m_Name: Radial_Twist_10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1901299098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1901299097}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 80.7, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1901299099
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1901299097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 4
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 50
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 2
    radius: 2
    divergence: 0
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 0
    randomRings: 0
    rays: 20
    randomRays: 0
    twist: 10
  hexagon:
    plane: 1
    row: 10
    col: 10
    size: 0.5
    div: 0
    rest: 1
    enable: 1
    noPc: 0
    pc:
    - {x: 76.36987, y: 2, z: -3.75}
    - {x: 77.23589, y: 2, z: -3.75}
    - {x: 78.10192, y: 2, z: -3.75}
    - {x: 78.96795, y: 2, z: -3.75}
    - {x: 79.83397, y: 2, z: -3.75}
    - {x: 80.7, y: 2, z: -3.75}
    - {x: 81.566025, y: 2, z: -3.75}
    - {x: 82.432045, y: 2, z: -3.75}
    - {x: 83.29807, y: 2, z: -3.75}
    - {x: 84.1641, y: 2, z: -3.75}
    - {x: 76.80288, y: 2, z: -3}
    - {x: 77.66891, y: 2, z: -3}
    - {x: 78.534935, y: 2, z: -3}
    - {x: 79.400955, y: 2, z: -3}
    - {x: 80.26698, y: 2, z: -3}
    - {x: 81.13301, y: 2, z: -3}
    - {x: 81.99904, y: 2, z: -3}
    - {x: 82.86506, y: 2, z: -3}
    - {x: 83.73109, y: 2, z: -3}
    - {x: 84.597115, y: 2, z: -3}
    - {x: 76.36987, y: 2, z: -2.25}
    - {x: 77.23589, y: 2, z: -2.25}
    - {x: 78.10192, y: 2, z: -2.25}
    - {x: 78.96795, y: 2, z: -2.25}
    - {x: 79.83397, y: 2, z: -2.25}
    - {x: 80.7, y: 2, z: -2.25}
    - {x: 81.566025, y: 2, z: -2.25}
    - {x: 82.432045, y: 2, z: -2.25}
    - {x: 83.29807, y: 2, z: -2.25}
    - {x: 84.1641, y: 2, z: -2.25}
    - {x: 76.80288, y: 2, z: -1.5}
    - {x: 77.66891, y: 2, z: -1.5}
    - {x: 78.534935, y: 2, z: -1.5}
    - {x: 79.400955, y: 2, z: -1.5}
    - {x: 80.26698, y: 2, z: -1.5}
    - {x: 81.13301, y: 2, z: -1.5}
    - {x: 81.99904, y: 2, z: -1.5}
    - {x: 82.86506, y: 2, z: -1.5}
    - {x: 83.73109, y: 2, z: -1.5}
    - {x: 84.597115, y: 2, z: -1.5}
    - {x: 76.36987, y: 2, z: -0.75}
    - {x: 77.23589, y: 2, z: -0.75}
    - {x: 78.10192, y: 2, z: -0.75}
    - {x: 78.96795, y: 2, z: -0.75}
    - {x: 79.83397, y: 2, z: -0.75}
    - {x: 80.7, y: 2, z: -0.75}
    - {x: 81.566025, y: 2, z: -0.75}
    - {x: 82.432045, y: 2, z: -0.75}
    - {x: 83.29807, y: 2, z: -0.75}
    - {x: 84.1641, y: 2, z: -0.75}
    - {x: 76.80288, y: 2, z: 0}
    - {x: 77.66891, y: 2, z: 0}
    - {x: 78.534935, y: 2, z: 0}
    - {x: 79.400955, y: 2, z: 0}
    - {x: 80.26698, y: 2, z: 0}
    - {x: 81.13301, y: 2, z: 0}
    - {x: 81.99904, y: 2, z: 0}
    - {x: 82.86506, y: 2, z: 0}
    - {x: 83.73109, y: 2, z: 0}
    - {x: 84.597115, y: 2, z: 0}
    - {x: 76.36987, y: 2, z: 0.75}
    - {x: 77.23589, y: 2, z: 0.75}
    - {x: 78.10192, y: 2, z: 0.75}
    - {x: 78.96795, y: 2, z: 0.75}
    - {x: 79.83397, y: 2, z: 0.75}
    - {x: 80.7, y: 2, z: 0.75}
    - {x: 81.566025, y: 2, z: 0.75}
    - {x: 82.432045, y: 2, z: 0.75}
    - {x: 83.29807, y: 2, z: 0.75}
    - {x: 84.1641, y: 2, z: 0.75}
    - {x: 76.80288, y: 2, z: 1.5}
    - {x: 77.66891, y: 2, z: 1.5}
    - {x: 78.534935, y: 2, z: 1.5}
    - {x: 79.400955, y: 2, z: 1.5}
    - {x: 80.26698, y: 2, z: 1.5}
    - {x: 81.13301, y: 2, z: 1.5}
    - {x: 81.99904, y: 2, z: 1.5}
    - {x: 82.86506, y: 2, z: 1.5}
    - {x: 83.73109, y: 2, z: 1.5}
    - {x: 84.597115, y: 2, z: 1.5}
    - {x: 76.36987, y: 2, z: 2.25}
    - {x: 77.23589, y: 2, z: 2.25}
    - {x: 78.10192, y: 2, z: 2.25}
    - {x: 78.96795, y: 2, z: 2.25}
    - {x: 79.83397, y: 2, z: 2.25}
    - {x: 80.7, y: 2, z: 2.25}
    - {x: 81.566025, y: 2, z: 2.25}
    - {x: 82.432045, y: 2, z: 2.25}
    - {x: 83.29807, y: 2, z: 2.25}
    - {x: 84.1641, y: 2, z: 2.25}
    - {x: 76.80288, y: 2, z: 3}
    - {x: 77.66891, y: 2, z: 3}
    - {x: 78.534935, y: 2, z: 3}
    - {x: 79.400955, y: 2, z: 3}
    - {x: 80.26698, y: 2, z: 3}
    - {x: 81.13301, y: 2, z: 3}
    - {x: 81.99904, y: 2, z: 3}
    - {x: 82.86506, y: 2, z: 3}
    - {x: 83.73109, y: 2, z: 3}
    - {x: 84.597115, y: 2, z: 3}
    pcBndIn:
    - {x: 82.86506, y: 2, z: 0}
    - {x: 81.99904, y: 2, z: 0}
    - {x: 81.13301, y: 2, z: 0}
    - {x: 80.26698, y: 2, z: 0}
    - {x: 79.400955, y: 2, z: 0}
    - {x: 78.534935, y: 2, z: 0}
    pcBndOut:
    - {x: 84.597115, y: 2, z: 3}
    - {x: 83.73109, y: 2, z: 3}
    - {x: 82.86506, y: 2, z: 3}
    - {x: 81.99904, y: 2, z: 3}
    - {x: 81.13301, y: 2, z: 3}
    - {x: 80.26698, y: 2, z: 3}
    - {x: 79.400955, y: 2, z: 3}
    - {x: 78.534935, y: 2, z: 3}
    - {x: 77.66891, y: 2, z: 3}
    - {x: 76.80288, y: 2, z: 3}
    - {x: 84.1641, y: 2, z: 2.25}
    - {x: 83.29807, y: 2, z: 2.25}
    - {x: 82.432045, y: 2, z: 2.25}
    - {x: 81.566025, y: 2, z: 2.25}
    - {x: 80.7, y: 2, z: 2.25}
    - {x: 79.83397, y: 2, z: 2.25}
    - {x: 78.96795, y: 2, z: 2.25}
    - {x: 78.10192, y: 2, z: 2.25}
    - {x: 77.23589, y: 2, z: 2.25}
    - {x: 76.36987, y: 2, z: 2.25}
    - {x: 84.597115, y: 2, z: 1.5}
    - {x: 83.73109, y: 2, z: 1.5}
    - {x: 82.86506, y: 2, z: 1.5}
    - {x: 81.99904, y: 2, z: 1.5}
    - {x: 81.13301, y: 2, z: 1.5}
    - {x: 80.26698, y: 2, z: 1.5}
    - {x: 79.400955, y: 2, z: 1.5}
    - {x: 78.534935, y: 2, z: 1.5}
    - {x: 77.66891, y: 2, z: 1.5}
    - {x: 76.80288, y: 2, z: 1.5}
    - {x: 84.1641, y: 2, z: 0.75}
    - {x: 83.29807, y: 2, z: 0.75}
    - {x: 82.432045, y: 2, z: 0.75}
    - {x: 81.566025, y: 2, z: 0.75}
    - {x: 80.7, y: 2, z: 0.75}
    - {x: 79.83397, y: 2, z: 0.75}
    - {x: 78.96795, y: 2, z: 0.75}
    - {x: 78.10192, y: 2, z: 0.75}
    - {x: 77.23589, y: 2, z: 0.75}
    - {x: 76.36987, y: 2, z: 0.75}
    - {x: 84.597115, y: 2, z: 0}
    - {x: 83.73109, y: 2, z: 0}
    - {x: 77.66891, y: 2, z: 0}
    - {x: 76.80288, y: 2, z: 0}
    - {x: 84.1641, y: 2, z: -0.75}
    - {x: 83.29807, y: 2, z: -0.75}
    - {x: 82.432045, y: 2, z: -0.75}
    - {x: 81.566025, y: 2, z: -0.75}
    - {x: 80.7, y: 2, z: -0.75}
    - {x: 79.83397, y: 2, z: -0.75}
    - {x: 78.96795, y: 2, z: -0.75}
    - {x: 78.10192, y: 2, z: -0.75}
    - {x: 77.23589, y: 2, z: -0.75}
    - {x: 76.36987, y: 2, z: -0.75}
    - {x: 84.597115, y: 2, z: -1.5}
    - {x: 83.73109, y: 2, z: -1.5}
    - {x: 82.86506, y: 2, z: -1.5}
    - {x: 81.99904, y: 2, z: -1.5}
    - {x: 81.13301, y: 2, z: -1.5}
    - {x: 80.26698, y: 2, z: -1.5}
    - {x: 79.400955, y: 2, z: -1.5}
    - {x: 78.534935, y: 2, z: -1.5}
    - {x: 77.66891, y: 2, z: -1.5}
    - {x: 76.80288, y: 2, z: -1.5}
    - {x: 84.1641, y: 2, z: -2.25}
    - {x: 83.29807, y: 2, z: -2.25}
    - {x: 82.432045, y: 2, z: -2.25}
    - {x: 81.566025, y: 2, z: -2.25}
    - {x: 80.7, y: 2, z: -2.25}
    - {x: 79.83397, y: 2, z: -2.25}
    - {x: 78.96795, y: 2, z: -2.25}
    - {x: 78.10192, y: 2, z: -2.25}
    - {x: 77.23589, y: 2, z: -2.25}
    - {x: 76.36987, y: 2, z: -2.25}
    - {x: 84.597115, y: 2, z: -3}
    - {x: 83.73109, y: 2, z: -3}
    - {x: 82.86506, y: 2, z: -3}
    - {x: 81.99904, y: 2, z: -3}
    - {x: 81.13301, y: 2, z: -3}
    - {x: 80.26698, y: 2, z: -3}
    - {x: 79.400955, y: 2, z: -3}
    - {x: 78.534935, y: 2, z: -3}
    - {x: 77.66891, y: 2, z: -3}
    - {x: 76.80288, y: 2, z: -3}
    - {x: 84.1641, y: 2, z: -3.75}
    - {x: 83.29807, y: 2, z: -3.75}
    - {x: 82.432045, y: 2, z: -3.75}
    - {x: 81.566025, y: 2, z: -3.75}
    - {x: 80.7, y: 2, z: -3.75}
    - {x: 79.83397, y: 2, z: -3.75}
    - {x: 78.96795, y: 2, z: -3.75}
    - {x: 78.10192, y: 2, z: -3.75}
    - {x: 77.23589, y: 2, z: -3.75}
    - {x: 76.36987, y: 2, z: -3.75}
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transforms: []
    vector3: []
    noPoints: 0
    outputPoints: []
    inBoundPoints: []
    outBoundPoints: []
  mirrored:
    amount: 50
    planeType: 0
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  bricks:
    amountType: 0
    mult: 1
    amount_X: 3
    amount_Y: 6
    amount_Z: 0
    size_Lock: 0
    size_X: 1
    size_Y: 1
    size_Z: 1
    sizeVar_X: 0
    sizeVar_Y: 0
    sizeVar_Z: 0
    offset_X: 0.5
    offset_Y: 0.5
    offset_Z: 0
    split_X: 0
    split_Y: 0
    split_Z: 0
    split_probability: 0
    split_offset: 0.5
    split_rotation: 30
  voxels:
    size: 1
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    iMat: {fileID: 0}
    oMat: {fileID: 0}
    mScl: 0.1
    uvE: 0
    uvC: {x: 0, y: 0}
    cE: 0
    cC: {r: 0, g: 0, b: 0, a: 0}
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    elementSizeThreshold: 5
    combineChildren: 0
    smooth: 0
    postWeld: 0
    inner: 0
    planar: 0
    relativeSize: 4
    absoluteSize: 0.1
    sizeLimitation: 0
    sizeAmount: 5
    vertexLimitation: 0
    vertexAmount: 300
    triangleLimitation: 0
    triangleAmount: 300
  export:
    source: 0
    suffix: _frags
  showCenter: 0
  centerPosition: {x: 0, y: 0, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1901299098}
  meshFilter: {fileID: 1901299101}
  meshRenderer: {fileID: 1901299100}
  skinnedMeshRend: {fileID: 0}
  meshFilters: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  materials: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.10924139
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 80.7, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
  resetState: 0
  interactive: 0
  intGo: {fileID: 0}
  intMf: {fileID: 0}
  intMr: {fileID: 0}
--- !u!23 &1901299100
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1901299097}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1901299101
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1901299097}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Radial
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 399585497}
  - {fileID: 203136135}
  - {fileID: 1181678841}
  - {fileID: 371049145}
  - {fileID: 580638905}
  - {fileID: 1719300434}
  - {fileID: 696151833}
  - {fileID: 858165926}
  - {fileID: 1876691645}
  - {fileID: 1780556220}
  - {fileID: 869776493}
  - {fileID: 410587155}
  - {fileID: 1901299098}
  - {fileID: 1482064832}
  - {fileID: 278356556}
  - {fileID: 1296339194}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
