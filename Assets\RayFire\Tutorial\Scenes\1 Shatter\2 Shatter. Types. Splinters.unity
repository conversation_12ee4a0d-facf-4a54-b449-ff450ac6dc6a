%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_TemporalCoherenceThreshold: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &371049144
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 371049145}
  - component: {fileID: 371049148}
  - component: {fileID: 371049147}
  - component: {fileID: 371049146}
  m_Layer: 0
  m_Name: Splinters_Y_200_0.5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &371049145
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 12.900002, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &371049146
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.5
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785365, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 371049145}
  meshFilter: {fileID: 371049148}
  meshRenderer: {fileID: 371049147}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.22531034
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 12.900002, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
--- !u!23 &371049147
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &371049148
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 371049144}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &399585496
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 399585497}
  - component: {fileID: 399585500}
  - component: {fileID: 399585499}
  - component: {fileID: 399585498}
  m_Layer: 0
  m_Name: Splinters_Y_30
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &399585497
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &399585498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 30
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470584, y: 0.48785377, z: 0}
  centerDirection: {x: 0, y: 0, z: -0.000000044369514, w: 1.0000064}
  transForm: {fileID: 399585497}
  meshFilter: {fileID: 399585500}
  meshRenderer: {fileID: 399585499}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.21506895
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 0, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
--- !u!23 &399585499
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &399585500
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399585496}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &580638904
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 580638905}
  - component: {fileID: 580638908}
  - component: {fileID: 580638907}
  - component: {fileID: 580638906}
  m_Layer: 0
  m_Name: Splinters_Y_200_0.95
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &580638905
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 19.2, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &580638906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.95
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785365, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 580638905}
  meshFilter: {fileID: 580638908}
  meshRenderer: {fileID: 580638907}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.19117242
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 19.2, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
--- !u!23 &580638907
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &580638908
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 580638904}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &696151832
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 696151833}
  - component: {fileID: 696151836}
  - component: {fileID: 696151835}
  - component: {fileID: 696151834}
  m_Layer: 0
  m_Name: Splinters_Z_200_0.8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &696151833
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 31.800003, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &696151834
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 2
    amount: 200
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785365, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 696151833}
  meshFilter: {fileID: 696151836}
  meshRenderer: {fileID: 696151835}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.20482759
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 31.800003, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
--- !u!23 &696151835
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &696151836
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 696151832}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1181678840
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1181678841}
  - component: {fileID: 1181678844}
  - component: {fileID: 1181678843}
  - component: {fileID: 1181678842}
  m_Layer: 0
  m_Name: Splinters_Y_200_0.8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1181678841
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.300001, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1181678842
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 1
    amount: 200
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785365, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1181678841}
  meshFilter: {fileID: 1181678844}
  meshRenderer: {fileID: 1181678843}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.21506895
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 6.300001, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
--- !u!23 &1181678843
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1181678844
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1181678840}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1719300433
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1719300434}
  - component: {fileID: 1719300437}
  - component: {fileID: 1719300436}
  - component: {fileID: 1719300435}
  m_Layer: 0
  m_Name: Splinters_X_200_0.8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1719300434
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 25.5, y: 2, z: 0}
  m_LocalScale: {x: 5, y: 4, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1719300435
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 50
    centerBias: 0
  splinters:
    axis: 0
    amount: 200
    strength: 0.8
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: -0.48470613, y: 0.48785365, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 1719300434}
  meshFilter: {fileID: 1719300437}
  meshRenderer: {fileID: 1719300436}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.27310345
  size: 6.4226165
  rescaleFix: 1
  originalScale: {x: 5, y: 4, z: 0.5}
  bound:
    m_Center: {x: 25.5, y: 2, z: 0}
    m_Extent: {x: 2.5, y: 2, z: 0.25}
--- !u!23 &1719300436
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1719300437
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1719300433}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &1762396893
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.x
      value: 15.2336445
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 2}
  m_IsPrefabParent: 0
--- !u!4 &2058777725
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4789674431876064, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 38.02, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2116671453}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2058777726
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1742102081341672, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2058777725}
  - component: {fileID: 2058777729}
  - component: {fileID: 2058777728}
  - component: {fileID: 2058777727}
  m_Layer: 0
  m_Name: Splinters_Column
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2058777727
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b55d9bb3ec909340848c72a5bfc0ad0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 1
  voronoi:
    amount: 300
    centerBias: 1
  splinters:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  slabs:
    axis: 1
    amount: 30
    strength: 0.7
    centerBias: 0
  radial:
    centerAxis: 1
    radius: 1
    divergence: 1
    restrictToPlane: 1
    rings: 10
    focus: 0
    focusStr: 50
    randomRings: 50
    rays: 10
    randomRays: 0
    twist: 0
  custom:
    source: 4
    useAs: 12
    amount: 100
    radius: 1
    enable: 1
    size: 0.05
    transform: []
    vector3: []
    noPoints: 0
  slice:
    plane: 1
    sliceList: []
  tets:
    lattice: 0
    density: 7
    noise: 100
  mode: 1
  material:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  clusters:
    enable: 0
    count: 10
    seed: 1
    relax: 0.5
    amount: 0
    layers: 0
    scale: 1
    min: 1
    max: 3
  advanced:
    seed: 1
    decompose: 0
    removeCollinear: 1
    copyComponents: 0
    inputPrecap: 1
    outputPrecap: 0
    removeDoubleFaces: 1
    excludeInnerFragments: 0
    elementSizeThreshold: 5
  showCenter: 0
  centerPosition: {x: 0.099739075, y: 4.0477533, z: 0}
  centerDirection: {x: 0, y: 0, z: 0, w: 1}
  transForm: {fileID: 2058777725}
  meshFilter: {fileID: 2058777729}
  meshRenderer: {fileID: 2058777728}
  skinnedMeshRend: {fileID: 0}
  meshes: []
  pivots: []
  rootChildList: []
  fragmentsAll: []
  fragmentsLast: []
  origSubMeshIdsRF: []
  shatterMode: 1
  colorPreview: 0
  scalePreview: 1
  previewScale: 0.61789656
  size: 7.9992795
  rescaleFix: 1
  originalScale: {x: 1, y: 1, z: 1}
  bound:
    m_Center: {x: 38.02, y: 3.7068398, z: -0.0000011622906}
    m_Extent: {x: 0.99083453, y: 3.7461464, z: 0.99083424}
--- !u!23 &2058777728
MeshRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 23302332038547678, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2058777729
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 33122025036178304, guid: c0cb72a37d202fd4a8c90d16dfd52432,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2058777726}
  m_Mesh: {fileID: 4300002, guid: ce3a603180ab1e848be4637e8ddfe198, type: 3}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Splinters
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 399585497}
  - {fileID: 1181678841}
  - {fileID: 371049145}
  - {fileID: 580638905}
  - {fileID: 1719300434}
  - {fileID: 696151833}
  - {fileID: 2058777725}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
