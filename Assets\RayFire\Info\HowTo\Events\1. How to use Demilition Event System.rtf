{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fnil\fcharset0 Calibri;}{\f2\fswiss\fcharset204 Calibri;}{\f3\fmodern Consolas;}{\f4\fmodern\fcharset0 Consolas;}}
{\colortbl ;\red0\green0\blue255;\red160\green195\blue220;\red189\green189\blue189;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to use Demilition Event System\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\par
\lang1033 In some cases, you may need to add specific scripts to fragments created after demolition or do something with them. In this case, you may use Demolition Event System that will notify you when any or specific object with Rigid component demolishes. Demolition Event will provide access to demolished Rigid component which storesb a list of all new fragments.\par
\par
{\b\f1\fs32\lang9{\field{\*\fldinst{HYPERLINK https://youtu.be/lRlqcHGA1sI }}{\fldrslt{https://youtu.be/lRlqcHGA1sI\ul0\cf0}}}}\f0\fs22\par
\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [20,1,20]\line\par
{\pntext\f0 2.\tab}Create another Cube, set its name to "\i LocalCube\b\i0 "\b0\lang1033 ,\f2\lang1049  \b\f0\lang9 position \b0 to [0,10,0]\line\par
{\pntext\f0 3.\tab}Create Sphere, set its name to "\i GlobalSphere\b\i0 "\b0\lang1033 ,\f2\lang1049  \b\f0\lang9 position \b0 to [3,7,0]\line\par
{\pntext\f0 4.\tab}Add \b Rayfire Rigid \b0 component to the \i LocalCube \i0 and \i GlobalSphere \i0 objects, set \b Initialization \b0 to \b At Start \b0 and \b Demolition Type \b0 to \b Runtime\b0 .\line\par
{\pntext\f0 5.\tab}\b Start \b0 Play Mode. \line\line Both objects will fall to the ground and will be demolished at collision.\line\par
{\pntext\f0 6.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 7.\tab}Create new \b GameObject\b0 , set its \b name \b0 to "\i EventObject\i0 ".\line\par
{\pntext\f0 8.\tab}Add \b DemolitionEventScript.cs \b0 script from \i Assets\\RayFire\\Tutorial\\Scripts \i0 folder to to EventObject.\line\line This script is for learning purposes only but you can use it as a base to add your own code. It is strongly recommended to learn how Event System works first in order to use Demolition Event System efficiently. \line\line Keep in mind that \b DemolitionEventScript.cs \b0 is only monobehavior part of \b Demolition Event System\b0 . The rest of the code can be found in \b RFEvent.cs \b0 file \i in Assets\\RayFire\\Scripts\\Classes \i0 folder.\line\par
{\pntext\f0 9.\tab}In \b DemolitionEventScript\b0  component enable \b Global Subscription \b0 toggle.\line\par
{\pntext\f0 10.\tab}\b Start \b0 Play Mode. \line\line In the Console you will see four messages:\i\line\line Global demolition: GlobalSphere was just demolished and created 15 fragments\line Contact point: (3.0, 0.5, 0.0)\line Global demolition: LocalCube was just demolished and created 15 fragments\line Contact point: (0.5, 0.5, 0.5)\line\i0\line Global Subscription notifies you when \b any \b0 Rigid component demolishes.\line\par
{\pntext\f0 11.\tab}\b Turn Off \b0 Play Mode\line\par
{\pntext\f0 12.\tab}In \b DemolitionEventScript\b0  component disable \b Global Subscription \b0 toggle and enable Local Subscription toggle.\line\par
{\pntext\f0 13.\tab}\b Drag and Drop \b0 LocalCube object from \b Hierarchy \b0 to \b Local Rigid Component \b0 field in Demolition Event Script component.\line\par
{\pntext\f0 14.\tab}\b Start \b0 Play Mode. \line\line In the Console you will see two messages:\line\line\i Local demolition: LocalCube was just demolished and created 15 fragments\line Contact point: (0.5, 0.5, 0.5)\line\line\i0 Local Subscription notifies you when \b predefined \b0 Rigid component demolishes.\i\line\i0\par
{\pntext\f0 15.\tab}\b Turn Off \b0 Play Mode. \lang1033\line\par
{\pntext\f0 16.\tab}\b\lang9 Open \b0 DemolitionEventScript.cs file.\lang1033\line\line\lang9 In  \b\i\f3\fs18 void LocalMethod\b0 (\b RayfireRigid rigid\b0 )\i0\f4\lang1033  \f0\fs22\lang9  method you can write code that will be evaluated when Local demolition event occurs and in \line\b\f3\fs18 void GlobalMethod\f4\lang1033  \b0\f3\lang9 (\b RayfireRigid rigid\b0 )\f4\lang1033  \f0\fs22\lang9 method you can write code that will be evaluated when Global demolition event occurs.  \line\par

\pard\box\brdrdash\brdrw0 \nowidctlpar\fi-360\li720\sl276\slmult1\b\i\f4\fs18\lang1033\tab\f3\lang9 rigid\f4\lang1033  \b0\i0\f0\fs22\lang9 variable is the Rigid that was demolished and list of all new fragments stored in its \i\f3\fs18 rigid\i0 .\i fragments\f4\lang1033  \i0\f0\fs22\lang9 list.\cf2\i\f3\fs18\par

\pard\nowidctlpar\sl276\slmult1\cf3\i0\par
\par
\cf0\f0\fs22\par
\line\fs24\lang1033\par
}
 