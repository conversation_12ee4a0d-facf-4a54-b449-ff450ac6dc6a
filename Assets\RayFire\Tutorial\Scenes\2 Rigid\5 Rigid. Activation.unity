%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!4 &39214125 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 294956622}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &49037700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 49037701}
  m_Layer: 0
  m_Name: ByVelocity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &49037701
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49037700}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 856647158}
  - {fileID: 1319049499}
  - {fileID: 362793856}
  - {fileID: 1051374643}
  - {fileID: 1738479483}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &115260077 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 490945681}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &127517761
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 127517762}
  m_Layer: 0
  m_Name: ByActivator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &127517762
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127517761}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1912176424}
  - {fileID: 462248090}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &169101759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 169101760}
  - component: {fileID: 169101761}
  m_Layer: 0
  m_Name: UnyieldingGizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &169101760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 169101759}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 10.06, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2085005803}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &169101761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 169101759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dae403a59cd85344ab1c214fcc3ccef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  unyielding: 1
  activatable: 0
  simulationType: 1
  centerPosition: {x: 0, y: 0, z: 0}
  size: {x: 3, y: 0.5, z: 3}
  rigidHost: {fileID: 0}
  rigidList: []
  shardList: []
  showGizmo: 1
  showCenter: 0
  id: 0
--- !u!4 &220468781 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 1526253141}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &286386242
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1996697291}
    m_Modifications:
    - target: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_Name
      value: Table_pf_1.0
      objectReference: {fileID: 0}
    - target: {fileID: 4157595537515100, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4157595537515100, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.81
      objectReference: {fileID: 0}
    - target: {fileID: 4310513622651536, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4323851467883610, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4430450608020004, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.7
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalScale.x
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalScale.y
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalScale.z
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.77
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.43060336
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.90254134
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -51.012
      objectReference: {fileID: 0}
    - target: {fileID: 4974125299253902, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
        type: 3}
      propertyPath: physics.materialType
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 114699511294202408, guid: 5a41fb5a7e4974f49a2856ac4096e821,
        type: 3}
      propertyPath: simulationType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114699511294202408, guid: 5a41fb5a7e4974f49a2856ac4096e821,
        type: 3}
      propertyPath: activation.byOffset
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
--- !u!1001 &294956622
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 856647158}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.054
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byVelocity
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1 &346780323
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 346780324}
  - component: {fileID: 346780327}
  - component: {fileID: 346780326}
  - component: {fileID: 346780325}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &346780324
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346780323}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 12.77, z: 0}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1319049499}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &346780325
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346780323}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &346780326
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346780323}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &346780327
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 346780323}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &362793855
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 362793856}
  m_Layer: 0
  m_Name: LowConcrete_2.1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &362793856
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 362793855}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.64, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2016313491}
  - {fileID: 115260077}
  m_Father: {fileID: 49037701}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &363251261 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 669530263}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &462248090 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 1429498585}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &490945681
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 362793856}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.054
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byVelocity
      value: 2.1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!4 &605652963 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 1271515889}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &645217680
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 645217681}
  m_Layer: 0
  m_Name: ByDamage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &645217681
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 645217680}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1486091085}
  - {fileID: 686735775}
  - {fileID: 4187333958692037}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &669530263
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1051374643}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.054
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byVelocity
      value: 2.1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1001 &673636689
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 645217681}
    m_Modifications:
    - target: {fileID: 1194623286426632, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_Name
      value: Rock_pf_1
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.02
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.y
      value: 26.1
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.x
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.y
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.z
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 114137721341641136, guid: a8fcd1926b0a4d442a1169ceff273537,
        type: 3}
      propertyPath: demolitionType
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
--- !u!114 &674765533
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265906492229737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 2
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 50
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 5
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 1
    maxDamage: 150
    currentDamage: 0
    collect: 1
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!4 &679544181 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 1765089961}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &686735775 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537,
    type: 3}
  m_PrefabInstance: {fileID: 1324277738}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &697963085
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2085005803}
    m_Modifications:
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.88
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.7532203
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1 &713169570 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1453039801469944, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 842149193}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &713169571 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 842149193}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &713169572
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 713169570}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0e08a7e57face1c4bb8be3e98d7d61fc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  expand: 0
  minimumArea: 0
  minimumSize: 0
  percentage: 0
  seed: 0
  clusterize: 1
  demolishable: 0
  startCollapse: 3
  collapseByIntegrity: 50
  collapse:
    type: 1
    start: 0
    end: 75
    steps: 10
    duration: 15
  startStress: 3
  stressByIntegrity: 70
  stress:
    enable: 0
    threshold: 100
    erosion: 1
    interval: 1
    support: 45
    exposed: 0
    bySize: 0
    strShards: []
    supShards: []
    rotCache: {x: 0, y: 0, z: 0}
    grvCache: {x: 0, y: 0, z: 0}
    supCache: 0
    sizeCache: 0
    initialized: 0
  showConnections: 1
  showNodes: 1
  showGizmo: 1
  showStress: 0
  checkConnectivity: 0
  connectivityCheckNeed: 0
  rigidList: []
  cluster:
    id: -1
    tm: {fileID: 0}
    depth: 0
    pos: {x: 0, y: 0, z: 0}
    rot: {x: 0, y: 0, z: 0, w: 0}
    scl: {x: 0, y: 0, z: 0}
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
    demolishable: 1
    rigid: {fileID: 0}
    shards: []
    areaCollapse: 0
    minimumArea: 0
    maximumArea: 0
    sizeCollapse: 0
    minimumSize: 0
    maximumSize: 0
    randomCollapse: 0
    randomSeed: 0
    cachedHost: 0
  initShardAmount: 0
  clsCount: 0
  rigidRootHost: {fileID: 0}
  meshRootHost: {fileID: 0}
--- !u!114 &713169574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 713169570}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dae403a59cd85344ab1c214fcc3ccef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  unyielding: 1
  activatable: 0
  simulationType: 1
  centerPosition: {x: 0, y: 0, z: 0}
  size: {x: 4.97, y: 1.78, z: 3.34}
  rigidHost: {fileID: 0}
  rigidList: []
  shardList: []
  showGizmo: 1
  showCenter: 0
  id: 0
--- !u!114 &742202415
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265905947680222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 0
  simulationType: 2
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 50
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 5
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 1
    maxDamage: 150
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &745675405
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 745675406}
  - component: {fileID: 745675407}
  m_Layer: 0
  m_Name: Gun
  m_TagString: Untagged
  m_Icon: {fileID: -964228994112308473, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &745675406
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 745675405}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 6.8, z: -6.16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1777663487}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &745675407
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 745675405}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6aae98af4160c3f44a105545f7b49970, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: 2
  maxDistance: 50
  target: {fileID: 0}
  rounds: 2
  rate: 0.3
  type: 0
  strength: 1
  radius: 1.56
  demolishCluster: 1
  affectInactive: 1
  rigid: 1
  rigidRoot: 1
  rigidBody: 1
  damage: 1
  debris: 1
  dust: 1
  flash: 1
  Flash:
    intensityMin: 0.5
    intensityMax: 0.7
    rangeMin: 5
    rangeMax: 7
    distance: 0.4
    color: {r: 1, g: 1, b: 0.8, a: 1}
  mask: -1
  tagFilter: Untagged
  showRay: 1
  showHit: 1
  shooting: 0
--- !u!1001 &842149193
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1860129545}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.06
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byConnectivity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byOffset
      value: 0.3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1 &856647157
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 856647158}
  m_Layer: 0
  m_Name: LowConcrete_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &856647158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 856647157}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -10.24, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2003581153}
  - {fileID: 39214125}
  m_Father: {fileID: 49037701}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &964199565 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 2079973232}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &964324099 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 286386242}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &986570608
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1798549005}
    m_Modifications:
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.88
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.55
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.69426274
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1 &1006737339 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1453039801469944, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 1074727724}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1006737340 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 1074727724}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1006737341
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1006737339}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0e08a7e57face1c4bb8be3e98d7d61fc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  type: 0
  expand: 0
  minimumArea: 0
  minimumSize: 0
  percentage: 0
  seed: 0
  clusterize: 1
  demolishable: 0
  startCollapse: 3
  collapseByIntegrity: 50
  collapse:
    type: 1
    start: 0
    end: 75
    steps: 10
    duration: 15
  startStress: 3
  stressByIntegrity: 70
  stress:
    enable: 0
    threshold: 100
    erosion: 1
    interval: 1
    support: 45
    exposed: 0
    bySize: 0
    strShards: []
    supShards: []
    rotCache: {x: 0, y: 0, z: 0}
    grvCache: {x: 0, y: 0, z: 0}
    supCache: 0
    sizeCache: 0
    initialized: 0
  showConnections: 1
  showNodes: 1
  showGizmo: 1
  showStress: 0
  checkConnectivity: 0
  connectivityCheckNeed: 0
  rigidList: []
  cluster:
    id: -1
    tm: {fileID: 0}
    depth: 0
    pos: {x: 0, y: 0, z: 0}
    rot: {x: 0, y: 0, z: 0, w: 0}
    scl: {x: 0, y: 0, z: 0}
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
    demolishable: 1
    rigid: {fileID: 0}
    shards: []
    areaCollapse: 0
    minimumArea: 0
    maximumArea: 0
    sizeCollapse: 0
    minimumSize: 0
    maximumSize: 0
    randomCollapse: 0
    randomSeed: 0
    cachedHost: 0
  initShardAmount: 0
  clsCount: 0
  rigidRootHost: {fileID: 0}
  meshRootHost: {fileID: 0}
--- !u!114 &1006737343
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1006737339}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dae403a59cd85344ab1c214fcc3ccef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  unyielding: 1
  activatable: 0
  simulationType: 1
  centerPosition: {x: 0, y: 7.45, z: 0}
  size: {x: 4.53, y: 1.64, z: 3.89}
  rigidHost: {fileID: 0}
  rigidList: []
  shardList: []
  showGizmo: 1
  showCenter: 0
  id: 0
--- !u!1 &1051374642
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1051374643}
  m_Layer: 0
  m_Name: HighConcrete
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1051374643
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1051374642}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3.86, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1336454435}
  - {fileID: 363251261}
  m_Father: {fileID: 49037701}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1061570238
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1319049499}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.054
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byVelocity
      value: 1.2
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1001 &1074727724
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2085005803}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.73
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byConnectivity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byOffset
      value: 0.3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1001 &1126468301
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1860129545}
    m_Modifications:
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.88
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.69
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.750784
      objectReference: {fileID: 0}
    - target: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 757eb8714409c024aabfc2c2827116c3, type: 3}
--- !u!1001 &1271515889
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1738479483}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.054
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byVelocity
      value: 2.1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1 &1319049498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1319049499}
  m_Layer: 0
  m_Name: LowConcrete_1.2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1319049499
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1319049498}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -5.44, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 346780324}
  - {fileID: 1411433781}
  m_Father: {fileID: 49037701}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1324277738
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 645217681}
    m_Modifications:
    - target: {fileID: 1194623286426632, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_Name
      value: Rock_pf_2
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.24
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.y
      value: 35.050003
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.x
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.y
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
      propertyPath: m_LocalScale.z
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 114137721341641136, guid: a8fcd1926b0a4d442a1169ceff273537,
        type: 3}
      propertyPath: demolitionType
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a8fcd1926b0a4d442a1169ceff273537, type: 3}
--- !u!1 &1336454434
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1336454435}
  - component: {fileID: 1336454438}
  - component: {fileID: 1336454437}
  - component: {fileID: 1336454436}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1336454435
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1336454434}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 20.56, z: 0}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1051374643}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1336454436
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1336454434}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1336454437
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1336454434}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1336454438
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1336454434}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1358818234
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1358818235}
  m_Layer: 0
  m_Name: ByMethod
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1358818235
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1358818234}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 4187334504659314}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1411433781 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07,
    type: 3}
  m_PrefabInstance: {fileID: 1061570238}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1423573542 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 986570608}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1423573543 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 986570608}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1423573544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1423573542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1001 &1429498585
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 127517762}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byActivator
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1001 &1445489124
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 15.2336445
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -43.55
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 32.32
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.055917617
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99843544
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 6.4110003
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -40.18
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!4 &1486091085 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4782160606959480, guid: a8fcd1926b0a4d442a1169ceff273537,
    type: 3}
  m_PrefabInstance: {fileID: 673636689}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1521619841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1521619842}
  m_Layer: 0
  m_Name: ByConnectivity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1521619842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521619841}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1798549005}
  - {fileID: 1860129545}
  - {fileID: 2085005803}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1526253141
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1996697291}
    m_Modifications:
    - target: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1542823542913228, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_Name
      value: Table_pf_0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4157595537515100, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4157595537515100, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.81
      objectReference: {fileID: 0}
    - target: {fileID: 4310513622651536, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4323851467883610, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4430450608020004, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3.8
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4457399236234274, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalScale.x
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalScale.y
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalScale.z
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.77
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.43060336
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.90254134
      objectReference: {fileID: 0}
    - target: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -51.012
      objectReference: {fileID: 0}
    - target: {fileID: 4974125299253902, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
        type: 3}
      propertyPath: physics.materialType
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 114699511294202408, guid: 5a41fb5a7e4974f49a2856ac4096e821,
        type: 3}
      propertyPath: simulationType
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114699511294202408, guid: 5a41fb5a7e4974f49a2856ac4096e821,
        type: 3}
      propertyPath: activation.byOffset
      value: 0.5
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5a41fb5a7e4974f49a2856ac4096e821, type: 3}
--- !u!1 &1584821686 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 697963085}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1584821687 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 697963085}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1584821688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584821686}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &1660611647
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1660611648}
  - component: {fileID: 1660611651}
  - component: {fileID: 1660611650}
  - component: {fileID: 1660611649}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1660611648
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660611647}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 20.56, z: 0}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 1738479483}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1660611649
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660611647}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 6
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1660611650
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660611647}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1660611651
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660611647}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &1738479482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1738479483}
  m_Layer: 0
  m_Name: HighGlass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1738479483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1738479482}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8.36, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1660611648}
  - {fileID: 605652963}
  m_Father: {fileID: 49037701}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1765089961
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1798549005}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.06
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byConnectivity
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byOffset
      value: 0.3
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1 &1772922420 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1265905815854442, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1126468301}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1772922421 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4187334637074886, guid: 757eb8714409c024aabfc2c2827116c3,
    type: 3}
  m_PrefabInstance: {fileID: 1126468301}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1772922422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1772922420}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!1 &1777663486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1777663487}
  m_Layer: 0
  m_Name: ByImpact
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1777663487
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1777663486}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 964199565}
  - {fileID: 745675406}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1798549004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1798549005}
  m_Layer: 0
  m_Name: Off
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1798549005
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1798549004}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1423573543}
  - {fileID: 679544181}
  m_Father: {fileID: 1521619842}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1860129544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1860129545}
  m_Layer: 0
  m_Name: OnBottom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1860129545
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860129544}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1772922421}
  - {fileID: 713169571}
  m_Father: {fileID: 1521619842}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1912176423
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1912176424}
  - component: {fileID: 1912176425}
  m_Layer: 0
  m_Name: Activator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1912176424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1912176423}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.07, y: 10.3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 127517762}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1912176425
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1912176423}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13a2260124482ed4aac2c705989354dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gizmoType: 0
  sphereRadius: 2.2341938
  boxSize: {x: 5, y: 2, z: 5}
  checkRigid: 1
  checkRigidRoot: 1
  type: 0
  delay: 0
  demolishCluster: 0
  apply: 0
  velocity: {x: 0, y: 0, z: 0}
  spin: {x: 0, y: 0, z: 0}
  mode: 0
  showAnimation: 0
  duration: 3
  scaleAnimation: 1
  positionAnimation: 0
  line: {fileID: 0}
  positionList: []
  showGizmo: 1
  activatorCollider: {fileID: 0}
--- !u!1 &1996697290
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1996697291}
  m_Layer: 0
  m_Name: ByOffset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1996697291
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1996697290}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 220468781}
  - {fileID: 964324099}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2003581152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2003581153}
  - component: {fileID: 2003581156}
  - component: {fileID: 2003581155}
  - component: {fileID: 2003581154}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2003581153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2003581152}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 12.77, z: 0}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 856647158}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2003581154
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2003581152}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2003581155
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2003581152}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2003581156
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2003581152}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1 &2016313490
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 1555100833696190, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2016313491}
  - component: {fileID: 2016313494}
  - component: {fileID: 2016313493}
  - component: {fileID: 2016313492}
  m_Layer: 0
  m_Name: Rock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2016313491
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 4774519664048540, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016313490}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 12.77, z: 0}
  m_LocalScale: {x: 15, y: 15, z: 15}
  m_Children: []
  m_Father: {fileID: 362793856}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2016313492
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 114253592078891596, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016313490}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
  activation:
    byVelocity: 0
    byOffset: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    layer: 
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 50
    depthFade: 0.5
    contactBias: 0.75
    seed: 1
    useShatter: 0
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      layer: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
    mesh: {fileID: 0}
    scrShatter: {fileID: 0}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2016313493
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 23202005795366508, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016313490}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2016313494
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 33909060925087810, guid: 5a41fb5a7e4974f49a2856ac4096e821,
    type: 3}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2016313490}
  m_Mesh: {fileID: 4300008, guid: 4228642c7a159304b85026a0dad44269, type: 3}
--- !u!1001 &2079973232
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1777663487}
    m_Modifications:
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.071
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4546915991184526, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114948687255050598, guid: 0a97c32700a3bbd4eb22abe817643d07,
        type: 3}
      propertyPath: activation.byImpact
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0a97c32700a3bbd4eb22abe817643d07, type: 3}
--- !u!1 &2085005802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2085005803}
  m_Layer: 0
  m_Name: OnTop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2085005803
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2085005802}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.3, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1584821687}
  - {fileID: 1006737340}
  - {fileID: 169101760}
  m_Father: {fileID: 1521619842}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Activation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 49037701}
  - {fileID: 1996697291}
  - {fileID: 645217681}
  - {fileID: 127517762}
  - {fileID: 1777663487}
  - {fileID: 1521619842}
  - {fileID: 1358818235}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1265905947680222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4187334504659314}
  - component: {fileID: 33279080703070168}
  - component: {fileID: 23093967578014484}
  - component: {fileID: 742202415}
  m_Layer: 0
  m_Name: Box_pf (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1265906492229737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4187333958692037}
  - component: {fileID: 33279080159730287}
  - component: {fileID: 23093967032448675}
  - component: {fileID: 674765533}
  m_Layer: 0
  m_Name: Box_pf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4187333958692037
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265906492229737}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 14.460001, z: 0}
  m_LocalScale: {x: 4, y: 1, z: 4}
  m_Children: []
  m_Father: {fileID: 645217681}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &4187334504659314
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265905947680222}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 14.460001, z: 0}
  m_LocalScale: {x: 4, y: 1, z: 4}
  m_Children: []
  m_Father: {fileID: 1358818235}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &23093967032448675
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265906492229737}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!23 &23093967578014484
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265905947680222}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1c83bd41b0e6d5e42b90263a2ec96293, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &33279080159730287
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265906492229737}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!33 &33279080703070168
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265905947680222}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
