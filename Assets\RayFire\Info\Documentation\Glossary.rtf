{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 Glossary\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\par
\lang1033\par
\b Demolition\b0 . For Mesh Object type this is process of changing original single mesh object to several other objects. For Connected Cluster Object type this is process of detaching its child objects and reparenting them to another object for separate simulation\par
\par
\b Activation\b0 . Process of changing simulation type of physical object from Inactive or Kinematik to Dynamic.\par
\par
\b Collapse\b0 . Activation of connected fragments or shards based on their size, connection area or random factor.\par
\par
\b Dynamic \b0 object. Physical object that can be affected by gravity and by other physical objects.\par
\par
\b Inactive \b0 object. Physical objects with disabled gravity and very high velocity damping. It can be pushed around and affected by other physical objects but will stay frozen in air until it will be activated and turned to dynamic.\par
\par
\b Kinematik \b0 object. Physical object that can not be affected by Dynamic or Inactive objects, but can affect them when moved manually or via code.\par
\par
\b Fragment\b0 . Single mesh object created after fragmentation. \par
\par
\b Shard\b0 . Single mesh object but when it is one object of Connected Cluster or Connectivity structure.\lang9\par
\par
\b\lang1033 Impact\b0 . Activation by RayfireGun component shot.\lang9\par
\par
\b Fading\b0 . Process of destroying fragment after some time.\par
\lang1033\par
\b Damage\b0 . Special feature in RayfireRigid component that allows to demolish object after its Current Damage value reaches defined Maximum Damage value. Damage can be applied by RayfireGun shot, by RayfireBomb explosion, by collision or via code using ApplyDamage() method.\par
\par
\lang9\par
}
 