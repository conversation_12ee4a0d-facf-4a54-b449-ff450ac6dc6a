{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to shoot and Activate prefragmented MeshRoot or RigidRoot structure\ulnone\b0\fs22\par

\pard\nowidctlpar\sl276\slmult1\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0, 0, 0] and \b scale \b0 to [10, 1, 10]\line\par
{\pntext\f0 3.\tab}Create \b Cylinder\b0 , this will be a pillar we will prefragment and shoot. \line\par
{\pntext\f0 4.\tab}Set its \b name \b0 to "\i Pillar\i0 ", \b position \b0 to [0, 4.5, 0] and \b scale \b0 to [1, 4, 1]\line\par
{\pntext\f0 5.\tab}Add \b RayFire Shatter \b0 to the Pillar object, in \b Voronoi \b0 properties set \b Amount \b0 to 1\b 00\b0  and click the \b Fragment \b0 button. New object Pillar_root will be created.\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 or \b Deactivate \b0 Pillar object, we won\rquote t need it anymore.\line\par
{\pntext\f0 7.\tab}Add \b RayFire Rigid \b0 to the Pillar_root object and set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 8.\tab}Set \b Simulation type \b0 to \b Inactive\b0 . Inactive objects simulate like dynamic objects but they always have 0 gravity and 0 velocity until they will be activated.\line\par
{\pntext\f0 9.\tab}Set \b Object type \b0 to \b Mesh Root\b0 . It means that this Rigid component will be copied to all children objects with mesh with object Type Mesh instead of Mesh Root. \line\par
{\pntext\f0 10.\tab}Create another \b Cylinder \b0 object, this will be our gun barrel.\line\par

\pard 
{\pntext\f0 11.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\ri-72\sl276\slmult1\tx8236\tx8378 Set its \b name \b0 to "\i Gun\i0 ", \b position \b0 to [4, 5, 0], \b rotation \b0 to [0, 0 ,90] and \b scale \b0 to [0.1, 0.2, 0.1] \line\par

\pard 
{\pntext\f0 12.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Add \b Rayfire Gun \b0 component and set \b Axis \b0 property to \b Y Green\b0 , set \b Impact Radius \b0 to \b 0.5 \b0 and \b disable Show \b0 property. Set \b Impact Strength \b0 property to \b 10.\b0\line\par
{\pntext\f0 13.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click several times \b0 on \b Single Shot \b0 button. \line\line Pillar fragments at the middle will be shifted at every shot but they will stay Inactive and still freeze in the air. In order to turn them to Dynamic you need to activate them. Pillar_root Simulation type can be Kinematic, in this case fragments will not shift but you will be able to activate them in the same way as Inactive fragments.\line\par
{\pntext\f0 14.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 15.\tab}\b Select \b0 Pillar_root object and in Rigid component open Activation properties and \b enabled Impact activation\b0 .\line\par
{\pntext\f0 16.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click several times \b0 on \b Single Shot \b0 button. \line\line This time fragments in Impact Radius range will be activated and turned to dynamic.\line\line You may notice that some fragments which were not inside Impact Radius were shifted by fragments which were activated and now they freeze in the air not connected to other fragments. In order to fix this you should enable Activation By Offset in Activation properties.\line\line Another problem is that whole upper group of fragments also stay in the air and do not fall to the ground. In order to fix this issue you should use Rayfire Connectivity and Rayfire Unyielding components.\line\par
{\pntext\f0 17.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 18.\tab}\b Select \b0 Pillar_root object and \b add Rayfire Connectivity \b0 component, this component will maintain connectivity among fragments.\line\par
{\pntext\f0 19.\tab}Set \b Connectivity Type \b0 to \b By Polygons\b0 , this connectivity type works well with Voronoi fragments since they share the faces with each other. For other objects which do not share faces and triangles you can use By Bounding Box connectivity type. It is less accurate but much faster. Also, if you are going to establish all connection in runtime this it the only type which will do this fast enough.\line\par
{\pntext\f0 20.\tab}\b Add Rayfire Unyielding \b0 component. This component will define group of fragments which will be consider as unyielding and other fragments will check for connectivity with these fragments through other fragments, if such connection won't exist then fragment will be activated.\line\par
{\pntext\f0 21.\tab}Set \b Gizmo Center \b0 to (0, \b -4, \b0 0) so Unyielding gizmo will overlap bottom fragments.\line\par
{\pntext\f0 22.\tab}In \b Rigid \b0 component open \b Activation \b0 properties and enable Activation \b By Connectivity\b0 .\line\par
{\pntext\f0 23.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click several times \b0 on \b Single Shot \b0 button. \line\line This time fragments inside Impact Radius range will be activated like before, but also whole upper group pf fragments will be turned into Connected Cluster wand will fall down as one solid object. Notice that when this group will fall to the ground it will not be demolished by collision. This behavior can be changed in Connectivity component.\line\par
{\pntext\f0 24.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 25.\tab}\b Select \b0 Pillar_root object and in Connectivity component \b enable Demolishable \b0 property in Cluster group.\line\par
{\pntext\f0 26.\tab}\b Start \b0 Play Mode, \b select \b0 Gun object and \b click several times \b0 on \b Single Shot \b0 button. \line\line Now upper group of fragments will be demolished gradually when it will collide with other fragments and ground. Demolition behavior of this Connected Cluster can be edited in Rigid_root Rigid component in Cluster Demolition properties. Even though object type is Mesh Root it still allows to change Connected Cluster properties because they will be inherited by possible Connected Clusters created by Conenctivy component.\line\par

\pard\nowidctlpar\sl276\slmult1 Keep in mind that in the same way can be used Rayfire RigidRoot component instead of Rigid with MeshRoot object type since they share the same properties.\line\par
\line\par
\par
\b\par
}
 