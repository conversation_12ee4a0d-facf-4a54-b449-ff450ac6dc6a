{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}{\f2\fnil\fcharset2 Symbol;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 RayFire Sound\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
\fs22\lang9 Sound Component allows to play sound at initialization, activation and demolition when used with Rigid component.\fs24\lang1033\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Properties\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Base Volume\b0\lang1033 : Base volume for all sound events.\fs24\par
\par
\b\fs22\lang9 Size Volume\b0\lang1033 : Additional volume value per one Unit size.\fs24\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Events\b0\fs24\lang1033\par
\par
\fs22\lang9 There are \f1\lang1049 f\f0\lang1033 our\lang9  type of sound events, sound can be played at.\par

\pard{\pntext\f2\'B7\tab}{\*\pn\pnlvlblt\pnf2\pnindent0{\pntxtb\'B7}}\nowidctlpar\fi-360\li720\sl276\slmult1 Initialization. Play audio clip at Rigid component initialization.\fs24\lang1033\par
{\pntext\f2\'B7\tab}\fs22\lang9 Activation. Play audio clip at Rigid component activation.\fs24\lang1033\par
{\pntext\f2\'B7\tab}\fs22\lang9 Demolition. Play audio clip at Rigid component demolition.\fs24\lang1033\par
{\pntext\f2\'B7\tab}\fs22 Collision. Play \lang9 audio clip \lang1033 when object collides with another object and collsion strength is higher than defined value\fs24 .\par

\pard\nowidctlpar\sl276\slmult1\par
\b\fs22 All events have identical properties except Collision event. Collision event has additional Relative Velocity property which works as a threshold to play collision sound.\par
\par
Enable\b0 : Enable sound properties for this event.\par
\par
\b Play Once\b0 : Play audio clip at even once and never play it again.\fs24\par
\par
\b\fs22 Multiplier\b0 : Sound volume multiplier for this event.\fs24\par
\par
\b\fs22 Clip\b0 : Audio Clip to play.\par
\fs24\par
\b\fs22 Random Clips\b0 : List of random Audio Clips to play\b\lang9 . \b0\fs24\lang1033\par
\par
\b\fs28\lang9\tab Audio Mixer\b0\fs24\lang1033\par
\par
\b\fs22 Output\b0 : Audio Mixer Output Group. Should be created manaully and defined in event.\par
\par
\b Priority\b0 : Set Priority property in Audio Source for played audio clip.\par
\par
\b Spatial Blend\b0 : Set Spatial Blend property in Audio Source for played audio clip.\par
\par
\b Min Distance\b0 : Set Min Distance property in Audio Source for played audio clip.\par
\par
\b Max Distance\b0 : Set Max Distance property in Audio Source for played audio clip.\fs24\par
\b\fs48\lang9\tab\b0\fs24\lang1033\par
\b\fs48\lang9\tab Filters\b0\fs24\lang1033\par
\par
\b\fs22\lang9 Minimum Size\b0 : Sound will be played only for objects with size bigger than this value\fs24\lang1033\par
\par
\b\fs22\lang9 Camer Distance\b0 : \lang1033 Sound will be played only if distance between main Camera and source of sound smaller than this value. \fs24\par
}
 