{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to prevent explosive initial simulation.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its name to "Ground", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 3.\tab}Create another Cube, this will be the source of our fragments which we will overlap with each other to initiate explosive simulation so later we will fix this issue.\line\par
{\pntext\f0 4.\tab}Set its name to "Fragments", position to [0,2,0] and scale to [1,3,1]\line\par
{\pntext\f0 5.\tab}Add RayFire Shatter component to "Fragments" and click on Fragment button.\line\par
{\pntext\f0 6.\tab}Destroy "Fragments" object, we do not need it anymore.\line\par
{\pntext\f0 7.\tab}Now select in Hierarchy all fragments under "Fragments_root" object, but not root itself, set Tool Handle Position to Pivot and set their scale to [2, 1, 2].\line\line Now we have a bunch of overlapped fragments.\line\par
{\pntext\f0 8.\tab}Add Rigid component to "Fragments_root" object, set Initialization to At Start, Object Type to Mesh Root.\line\par
{\pntext\f0 9.\tab}Start Play Mode.\line\line Notice how fragments explode at the start because they overlap and push each other.\line\par
{\pntext\f0 10.\tab}Turn Off Play Mode. \line\par
{\pntext\f0 11.\tab}Select "Fragments_root" object and in Rigid Physics properties enable Ignore Near property.\fs24\lang1033\line\par
{\pntext\f0 12.\tab}\fs22\lang9 Start Play Mode. \line\line This time fragments fall down but do not explode. Ignore Near property makes them ignore colliders of overlapped neighbor fragments. Obviously this makes simulation worse, but in some cases this is the only way to avoid explosive simulation if you do not want to edit your structure to remove overlaps. \par

\pard\nowidctlpar\sl276\slmult1\tab\par
\tab Keep in mind that Ignore Near property also works for Connected Cluster object \tab type and for Runtime mesh demolition. \par
\par
\tab In the same way Ignore Near works for RigidRoot component.\line\line\par
\tab\line\line\line\fs24\lang1033\par
}
 