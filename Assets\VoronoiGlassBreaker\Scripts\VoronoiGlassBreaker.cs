using UnityEngine;
using System.Collections.Generic;
using System.Collections;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Main component for Voronoi-based glass breaking system
    /// Supports progressive breaking based on impact points
    /// </summary>
    [RequireComponent(typeof(MeshRenderer), typeof(MeshFilter), typeof(Collider))]
    public class VoronoiGlassBreaker : MonoBehaviour
    {
        [System.Serializable]
        public class BreakSettings
        {
            [Header("Voronoi Generation")]
            public int cellCount = 50;
            public int relaxationIterations = 2;
            public float minCellSize = 0.05f;
            
            [Header("Break Detection")]
            public float breakForce = 10f;
            public float breakRadius = 1f;
            public LayerMask breakableLayers = -1;
            public string breakTag = "Breakable";
            
            [Header("Progressive Breaking")]
            public bool enableProgressiveBreaking = true;
            public float propagationSpeed = 5f;
            public float propagationRadius = 0.3f;
            public AnimationCurve propagationFalloff = AnimationCurve.EaseInOut(0f, 1f, 1f, 0f);
            
            [Header("Fragment Physics")]
            public float fragmentLifetime = 10f;
            public float fragmentMass = 0.1f;
            public float impactForceMultiplier = 1f;
            public PhysicMaterial fragmentPhysicsMaterial;
            
            [Header("Audio")]
            public AudioClip breakSound;
            public AudioClip crackSound;
            public float audioVolume = 1f;
            
            [Header("Effects")]
            public GameObject breakEffectPrefab;
            public GameObject crackEffectPrefab;
            public float effectDuration = 2f;
        }
        
        [SerializeField] private BreakSettings settings = new BreakSettings();
        
        private MeshRenderer meshRenderer;
        private MeshFilter meshFilter;
        private Collider glassCollider;
        private AudioSource audioSource;
        private Material originalMaterial;
        
        private List<VoronoiCell> voronoiCells;
        private List<Mesh> fragmentMeshes;
        private List<GlassFragment> activeFragments;
        private bool isInitialized = false;
        private bool isBreaking = false;
        private Coroutine breakingCoroutine;
        
        // Events
        public System.Action<Vector3, float> OnGlassBreakStart;
        public System.Action<VoronoiCell> OnCellBreak;
        public System.Action OnGlassFullyBroken;
        public System.Action OnGlassRepaired;
        
        public bool IsInitialized => isInitialized;
        public bool IsBreaking => isBreaking;
        public bool IsFullyBroken => voronoiCells != null && voronoiCells.TrueForAll(cell => cell.isBroken);
        public BreakSettings Settings => settings;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            InitializeVoronoiDiagram();
        }
        
        private void InitializeComponents()
        {
            meshRenderer = GetComponent<MeshRenderer>();
            meshFilter = GetComponent<MeshFilter>();
            glassCollider = GetComponent<Collider>();
            
            // Create audio source
            audioSource = gameObject.AddComponent<AudioSource>();
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 1f; // 3D sound
            
            originalMaterial = meshRenderer.material;
            activeFragments = new List<GlassFragment>();
            
            // Ensure collider is trigger for break detection
            if (glassCollider != null)
            {
                // Check if it's a MeshCollider and if it's concave
                MeshCollider meshCol = glassCollider as MeshCollider;
                if (meshCol != null && !meshCol.convex)
                {
                    if (Application.isPlaying)
                    {
                        Debug.LogWarning("VoronoiGlassBreaker: Concave MeshCollider detected at runtime. Please use a convex collider or BoxCollider for proper trigger detection.");
                        // Try to make it convex instead of replacing
                        meshCol.convex = true;
                        meshCol.isTrigger = true;
                    }
                    else
                    {
                        Debug.LogWarning("VoronoiGlassBreaker: MeshCollider must be convex to use as trigger. Making it convex.");

                        // Simply make the MeshCollider convex instead of replacing it
                        meshCol.convex = true;
                        meshCol.isTrigger = true;
                    }
                }
                else
                {
                    glassCollider.isTrigger = true;
                }
            }
        }
        
        private void InitializeVoronoiDiagram()
        {
            if (meshFilter.mesh == null)
            {
                Debug.LogError("VoronoiGlassBreaker: No mesh found!");
                return;
            }
            
            // Generate Voronoi cells based on mesh bounds
            Bounds meshBounds = meshFilter.mesh.bounds;
            Rect voronoiBounds = new Rect(0, 0, 1, 1); // Normalized UV space
            
            voronoiCells = VoronoiGenerator.GenerateVoronoi(voronoiBounds, settings.cellCount, settings.relaxationIterations);
            
            // Pre-generate fragment meshes
            fragmentMeshes = MeshSlicer.SliceMesh(meshFilter.mesh, voronoiCells, transform);
            
            isInitialized = true;
            
            Debug.Log($"VoronoiGlassBreaker initialized with {voronoiCells.Count} cells and {fragmentMeshes.Count} fragments");
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (!isInitialized || IsFullyBroken) return;
            
            if (CanBreakGlass(other))
            {
                Vector3 impactPoint = other.ClosestPoint(transform.position);
                float impactForce = CalculateImpactForce(other);
                
                if (impactForce >= settings.breakForce)
                {
                    BreakGlass(impactPoint, impactForce);
                }
            }
        }
        
        private bool CanBreakGlass(Collider other)
        {
            // Check layer mask
            if ((settings.breakableLayers & (1 << other.gameObject.layer)) == 0)
                return false;
            
            // Check tag if specified
            if (!string.IsNullOrEmpty(settings.breakTag) && !other.CompareTag(settings.breakTag))
                return false;
            
            return true;
        }
        
        private float CalculateImpactForce(Collider other)
        {
            Rigidbody rb = other.GetComponent<Rigidbody>();
            if (rb != null)
            {
                return rb.velocity.magnitude * rb.mass;
            }
            
            return settings.breakForce;
        }
        
        /// <summary>
        /// Break the glass at the specified impact point
        /// </summary>
        public void BreakGlass(Vector3 impactPoint, float impactForce)
        {
            if (!isInitialized || isBreaking) return;
            
            // Convert world impact point to UV space
            Vector2 uvImpactPoint = WorldToUVPoint(impactPoint);
            
            // Find the cell containing the impact point
            VoronoiCell impactCell = FindCellContainingPoint(uvImpactPoint);
            
            if (impactCell != null && !impactCell.isBroken)
            {
                OnGlassBreakStart?.Invoke(impactPoint, impactForce);
                
                if (settings.enableProgressiveBreaking)
                {
                    StartProgressiveBreaking(impactCell, uvImpactPoint, impactForce);
                }
                else
                {
                    BreakAllCells(impactForce);
                }
                
                PlayBreakSound();
                CreateBreakEffect(impactPoint);
            }
        }
        
        private Vector2 WorldToUVPoint(Vector3 worldPoint)
        {
            // Convert world point to local space
            Vector3 localPoint = transform.InverseTransformPoint(worldPoint);
            
            // Convert to UV coordinates (simplified - assumes planar mapping)
            Bounds bounds = meshFilter.mesh.bounds;
            float u = Mathf.InverseLerp(bounds.min.x, bounds.max.x, localPoint.x);
            float v = Mathf.InverseLerp(bounds.min.z, bounds.max.z, localPoint.z);
            
            return new Vector2(u, v);
        }
        
        private VoronoiCell FindCellContainingPoint(Vector2 uvPoint)
        {
            foreach (VoronoiCell cell in voronoiCells)
            {
                if (cell.ContainsPoint(uvPoint))
                {
                    return cell;
                }
            }
            
            // If no cell contains the point, find the closest one
            VoronoiCell closestCell = null;
            float minDistance = float.MaxValue;
            
            foreach (VoronoiCell cell in voronoiCells)
            {
                float distance = Vector2.Distance(uvPoint, cell.seedPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestCell = cell;
                }
            }
            
            return closestCell;
        }
        
        private void StartProgressiveBreaking(VoronoiCell initialCell, Vector2 impactPoint, float impactForce)
        {
            if (breakingCoroutine != null)
            {
                StopCoroutine(breakingCoroutine);
            }
            
            breakingCoroutine = StartCoroutine(ProgressiveBreakingCoroutine(initialCell, impactPoint, impactForce));
        }
        
        private IEnumerator ProgressiveBreakingCoroutine(VoronoiCell initialCell, Vector2 impactPoint, float impactForce)
        {
            isBreaking = true;
            
            // Break the initial cell
            BreakCell(initialCell, impactPoint, impactForce);
            
            // Propagate breaking to neighboring cells
            Queue<VoronoiCell> cellsToBreak = new Queue<VoronoiCell>();
            HashSet<VoronoiCell> processedCells = new HashSet<VoronoiCell>();
            
            cellsToBreak.Enqueue(initialCell);
            processedCells.Add(initialCell);
            
            while (cellsToBreak.Count > 0)
            {
                VoronoiCell currentCell = cellsToBreak.Dequeue();
                
                // Check neighbors for propagation
                foreach (int neighborIndex in currentCell.neighborCells)
                {
                    if (neighborIndex < 0 || neighborIndex >= voronoiCells.Count) continue;
                    
                    VoronoiCell neighbor = voronoiCells[neighborIndex];
                    if (neighbor.isBroken || processedCells.Contains(neighbor)) continue;
                    
                    // Calculate propagation probability based on distance
                    float distance = Vector2.Distance(neighbor.seedPoint, impactPoint);
                    float propagationChance = settings.propagationFalloff.Evaluate(distance / settings.propagationRadius);
                    
                    if (distance <= settings.propagationRadius && Random.value < propagationChance)
                    {
                        // Wait for propagation delay
                        yield return new WaitForSeconds(1f / settings.propagationSpeed);
                        
                        BreakCell(neighbor, impactPoint, impactForce * 0.8f);
                        cellsToBreak.Enqueue(neighbor);
                        processedCells.Add(neighbor);
                        
                        PlayCrackSound();
                        CreateCrackEffect(UVToWorldPoint(neighbor.seedPoint));
                    }
                }
            }
            
            isBreaking = false;
            
            if (IsFullyBroken)
            {
                OnGlassFullyBroken?.Invoke();
                HideOriginalMesh();
            }
        }
        
        private void BreakCell(VoronoiCell cell, Vector2 impactPoint, float impactForce)
        {
            if (cell.isBroken) return;
            
            cell.Break(impactPoint, impactForce, Time.time);
            
            // Create fragment for this cell
            int cellIndex = voronoiCells.IndexOf(cell);
            if (cellIndex >= 0 && cellIndex < fragmentMeshes.Count && fragmentMeshes[cellIndex] != null)
            {
                CreateFragment(fragmentMeshes[cellIndex], cell, UVToWorldPoint(impactPoint), impactForce);
            }
            
            OnCellBreak?.Invoke(cell);
        }
        
        private Vector3 UVToWorldPoint(Vector2 uvPoint)
        {
            // Convert UV coordinates back to world space (simplified)
            Bounds bounds = meshFilter.mesh.bounds;
            Vector3 localPoint = new Vector3(
                Mathf.Lerp(bounds.min.x, bounds.max.x, uvPoint.x),
                bounds.center.y,
                Mathf.Lerp(bounds.min.z, bounds.max.z, uvPoint.y)
            );
            
            return transform.TransformPoint(localPoint);
        }
        
        private void CreateFragment(Mesh fragmentMesh, VoronoiCell sourceCell, Vector3 impactPoint, float impactForce)
        {
            if (fragmentMesh == null || fragmentMesh.vertexCount == 0)
            {
                Debug.LogWarning("VoronoiGlassBreaker: Cannot create fragment with null or empty mesh");
                return;
            }

            GameObject fragmentObj = new GameObject($"GlassFragment_{voronoiCells.IndexOf(sourceCell)}");
            fragmentObj.transform.position = transform.position;
            fragmentObj.transform.rotation = transform.rotation;
            fragmentObj.transform.localScale = transform.localScale;

            // Add required components first
            fragmentObj.AddComponent<MeshRenderer>();
            fragmentObj.AddComponent<MeshFilter>();
            fragmentObj.AddComponent<Rigidbody>();
            fragmentObj.AddComponent<MeshCollider>();

            // Then add the GlassFragment component
            GlassFragment fragment = fragmentObj.AddComponent<GlassFragment>();
            fragment.lifetime = settings.fragmentLifetime;
            fragment.mass = settings.fragmentMass;
            fragment.physicsMaterial = settings.fragmentPhysicsMaterial;

            fragment.Initialize(fragmentMesh, originalMaterial, sourceCell, impactPoint, impactForce * settings.impactForceMultiplier);

            fragment.OnFragmentDestroyed += OnFragmentDestroyed;
            activeFragments.Add(fragment);
        }
        
        private void OnFragmentDestroyed(GlassFragment fragment)
        {
            activeFragments.Remove(fragment);
        }
        
        private void BreakAllCells(float impactForce)
        {
            foreach (VoronoiCell cell in voronoiCells)
            {
                if (!cell.isBroken)
                {
                    BreakCell(cell, cell.seedPoint, impactForce);
                }
            }
            
            OnGlassFullyBroken?.Invoke();
            HideOriginalMesh();
        }
        
        private void HideOriginalMesh()
        {
            meshRenderer.enabled = false;
            if (glassCollider != null)
            {
                glassCollider.enabled = false;
            }
        }
        
        private void PlayBreakSound()
        {
            if (settings.breakSound != null && audioSource != null)
            {
                audioSource.clip = settings.breakSound;
                audioSource.volume = settings.audioVolume;
                audioSource.Play();
            }
        }
        
        private void PlayCrackSound()
        {
            if (settings.crackSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(settings.crackSound, settings.audioVolume * 0.5f);
            }
        }
        
        private void CreateBreakEffect(Vector3 position)
        {
            if (settings.breakEffectPrefab != null)
            {
                GameObject effect = Instantiate(settings.breakEffectPrefab, position, Quaternion.identity);
                Destroy(effect, settings.effectDuration);
            }
        }
        
        private void CreateCrackEffect(Vector3 position)
        {
            if (settings.crackEffectPrefab != null)
            {
                GameObject effect = Instantiate(settings.crackEffectPrefab, position, Quaternion.identity);
                Destroy(effect, settings.effectDuration * 0.5f);
            }
        }
        
        /// <summary>
        /// Force break the glass at a specific point
        /// </summary>
        public void ForceBreak(Vector3 impactPoint)
        {
            BreakGlass(impactPoint, settings.breakForce);
        }
        
        /// <summary>
        /// Repair the glass to its original state
        /// </summary>
        public void RepairGlass()
        {
            // Stop any ongoing breaking
            if (breakingCoroutine != null)
            {
                StopCoroutine(breakingCoroutine);
                isBreaking = false;
            }
            
            // Destroy all fragments
            foreach (GlassFragment fragment in activeFragments)
            {
                if (fragment != null)
                {
                    fragment.DestroyFragment();
                }
            }
            activeFragments.Clear();
            
            // Reset all cells
            foreach (VoronoiCell cell in voronoiCells)
            {
                cell.Reset();
            }
            
            // Show original mesh
            meshRenderer.enabled = true;
            if (glassCollider != null)
            {
                glassCollider.enabled = true;
            }
            
            OnGlassRepaired?.Invoke();
        }
        
        /// <summary>
        /// Get the percentage of broken cells
        /// </summary>
        public float GetBreakagePercentage()
        {
            if (voronoiCells == null || voronoiCells.Count == 0) return 0f;
            
            int brokenCount = 0;
            foreach (VoronoiCell cell in voronoiCells)
            {
                if (cell.isBroken) brokenCount++;
            }
            
            return (float)brokenCount / voronoiCells.Count;
        }
        
        private void OnDestroy()
        {
            // Clean up fragments
            foreach (GlassFragment fragment in activeFragments)
            {
                if (fragment != null)
                {
                    fragment.DestroyFragment();
                }
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!isInitialized || voronoiCells == null) return;
            
            // Draw Voronoi cells
            Gizmos.color = Color.yellow;
            foreach (VoronoiCell cell in voronoiCells)
            {
                Vector3 worldSeed = UVToWorldPoint(cell.seedPoint);
                Gizmos.DrawWireSphere(worldSeed, 0.05f);
                
                if (cell.isBroken)
                {
                    Gizmos.color = Color.red;
                    Gizmos.DrawWireSphere(worldSeed, 0.1f);
                    Gizmos.color = Color.yellow;
                }
            }
        }
    }
}
