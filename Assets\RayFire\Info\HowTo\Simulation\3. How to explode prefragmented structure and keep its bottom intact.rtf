{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to explode prefragmented structure and keep its bottom intact.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create Cube, this will be a ground cube which will be used for collisions.\line\par
{\pntext\f0 2.\tab}Set its name to "Ground", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 3.\tab}Create another Cube, \lang1033 s\lang9 et its name to "Wall", position to [0,4,0] and scale to [3,7,0.5]\line\par
{\pntext\f0 4.\tab}Add RayFire Shatter component to "Wall", set Amount to 100 and click on Fragment button.\line\par
{\pntext\f0 5.\tab}Destroy "Wall" object, we do not need it anymore.\line\par
{\pntext\f0 6.\tab}Add Rigid component to "Wall_root" object, set Initialization to At Start, Object Type to Mesh Root and Simulation Type to Kinematik.\line\par
{\pntext\f0 7.\tab}Create empty game object, \lang1033 s\lang9 et its name to "Bomb" and position to [0,1,4]\line\par
{\pntext\f0 8.\tab}Add RayFire Bomb component to "Bomb" and set its Range to 10 so it will cover the whole Wall.\line\par
{\pntext\f0 9.\tab}Under Activate group of properties enable Kinematic property.\fs24\lang1033\line\par
{\pntext\f0 10.\tab}\fs22\lang9 Start Play Mode. \line\par
{\pntext\f0 11.\tab}Select Bomb and click on Explode button, the same can be done via code using public method Explode()\line\line Wall will be exploded, but in some cases you may need to keep some group of fragments intact. To do so, you need to use RayFire Unyielding component.\line\par
{\pntext\f0 12.\tab}Turn Off Play Mode. \line\par
{\pntext\f0 13.\tab}Add RayFire Unyielding component to "Wall_root", set its Size to [3,1,1]\line and Center to [0,-3,0] so it will overlap the bottom fragments with its gizmo.\line\par
{\pntext\f0 14.\tab}Start Play Mode. \fs24\lang1033\line\fs22\lang9\par
{\pntext\f0 15.\tab}Select Bomb and click on Explode button.\line\line This time, you can see that the group of fragments overlapped by Unyielding component were not affected by the explosion. In the same way you can use RigidRoot component instead of Rigid with Mesh Root object type.\line\par
{\pntext\f0 16.\tab}Turn Off Play Mode. \line\line In some cases you may use Connected Cluster object type instead of Mesh Root, in this case setup should be a bit more complicated to achieve the same result.\line\par
{\pntext\f0 17.\tab}Select "Wall_root" object and in Rigid component change Object Type to Connected Cluster and Demolition Type to Runtime.\line\par
{\pntext\f0 18.\tab}Start Play Mode. \fs24\lang1033\line\fs22\lang9\par
{\pntext\f0 19.\tab}Select Bomb and click on Explode button.\line\line As you can see Connected Cluster was affected by the Bomb as one solid object. To explode only fragments outside Unyielding gizmo you need to enable Damage feature. Using Damage you can demolish Connected Cluster to fragments and then explode them instantly.\fs24\lang1033\line\fs22\lang9\par
{\pntext\f0 20.\tab}Select "Wall_root" object and in Rigid component open Damage properties and turn On Enable property.\f1\lang1049  \f0\lang1033 Set max Damage value to 10. \lang9\line\line\lang1033 You can explode objects several times applying a different amount of damage every time, total amount of applied damage you can see in Current Damage value field. Object will be demolished when it will reach Max Damage value.\lang9\line\par
{\pntext\f0 21.\tab}Select Bomb and enable Apply property under Damage properties, set Value to 100 so first explosion will be enough to demolish Connected Cluster.\fs24\lang1033\line\par
{\pntext\f0 22.\tab}\fs22\lang9 Start Play Mode. \fs24\lang1033\line\par
{\pntext\f0 23.\tab}\fs22\lang9 Select Bomb and click on Explode button.\fs24\lang1033\line\line Upper fragments were exploded, but bottom fragments overlapped by Unyielding gizmo also were exploded as one Connected Cluster. This happened because our bomb activates Wall before explode it and Connected Cluster inherit its simulation type. This time we do not need to activate anything like with Mesh Root setup, instead we need to demolish Connected Cluster by Damage using Bomb.\line\par
{\pntext\f0 24.\tab}\fs22\lang9 Turn Off Play Mode. \fs24\lang1033\line\par
{\pntext\f0 25.\tab}Select Bomb and disable Kinematic checkbox under Activate properties.\line\par
{\pntext\f0 26.\tab}\fs22\lang9 Start Play Mode. \fs24\lang1033\line\par
{\pntext\f0 27.\tab}\fs22\lang9 Select Bomb and click on Explode button.\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\par
This time, explosion looks the same as with MeshRoot setup.\par
\line\par
\line\par
}
 