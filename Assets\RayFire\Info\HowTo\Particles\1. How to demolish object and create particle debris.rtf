{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish object and create particle debris.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create a Cube, this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its name to "Brick", position to [0,7,0], rotation to [30, 0, 50] and scale to [2,1,1]\line\par
{\pntext\f0 3.\tab}Remove it's Box Collider because the Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "Ground", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 6.\tab}Add RayFire Rigid component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid Initialization to At Start and Demolition type to Awake Precache.\line\par
{\pntext\f0 8.\tab}Start Play Mode. Brick will fall down and will be demolished by collision.\line\line Now let's add some debris to this demolition simulation.\line\par
{\pntext\f0 9.\tab}Turn Off Play Mode.\line\par
{\pntext\f0 10.\tab}Select Brick and add RayFire Debris component. You can add several Debris components for different types of debris if necessary.\line\par
{\pntext\f0 11.\tab}Enable On Demolition property because we want to create Debris particles for demolished fragments.\line\par
{\pntext\f0 12.\tab}Drag and drop debris.fbx from \ldblquote Assets\\RayFire\\Tutorial\\Fbx\rdblquote  folder Debris Reference property. \line\line You can define any GameObject with mesh as debris reference. In case you want to define several debris meshes for variety you can define a root with several objects with meshes, in this case every debris particle system will pick one mesh as its source for the particle mesh. In this case debris.fbx contains 6 meshes.\line Optionally You can define Debris Material, if this property won\rquote t be defined then demolished object material will be applied to debris.\line\par
{\pntext\f0 13.\tab}Start Play Mode. Brick will fall down and will be demolished by collision and a bunch of particle debris will be emitted.\line\par
{\pntext\f0 14.\tab}Turn Off Play Mode. \line\par
{\pntext\f0 15.\tab}Now let's make debris dynamic more noticeable and add some speed to them.\fs24\lang1033\line\par
{\pntext\f0 16.\tab}\fs22\lang9 Select Brick and in the Debris / Dynamic group of properties set Speed Min to 4, Speed Max to 8, Velocity Min to 1 and Velocity Max to 2.\fs24\lang1033\line\par
{\pntext\f0 17.\tab}\fs22\lang9 Start Play Mode. \line\par

\pard\nowidctlpar\sl276\slmult1\tab This time debris will be emitted and cover a bigger area with higher speed.\line\fs24\lang1033\par
}
 