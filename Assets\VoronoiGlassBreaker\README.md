# Voronoi Glass Breaker System

A Unity-based glass breaking system using Voronoi diagrams for realistic fracture patterns. This system supports progressive breaking, UV preservation, and physics-based fragments.

## Features

- **Voronoi-based Fracturing**: Uses Voronoi diagrams to create realistic crack patterns
- **Progressive Breaking**: Glass breaks gradually from impact points, not all at once
- **UV Preservation**: Maintains UV coordinates during mesh slicing
- **Physics Integration**: Fragments have realistic physics behavior
- **Audio Support**: Integrated audio system for break and crack sounds
- **Visual Effects**: Particle effects for enhanced visual feedback
- **Customizable**: Extensive settings for different glass types and behaviors

## Quick Start

### 1. Basic Setup

1. **Create the "Breakable" tag**: Use `Tools > Voronoi Glass Breaker > Create Breakable Tag` or manually add it in the Tag Manager
2. Add the `VoronoiGlassBreaker` component to any GameObject with a MeshRenderer and Collider
3. Configure the break settings in the inspector
4. Set up breakable objects with the appropriate layer/tag
5. Run the scene and collide with the glass

### 2. Using the Demo Scene

1. Add the `GlassBreakerDemo` component to an empty GameObject
2. Configure the glass prefab and materials
3. Run the scene for a complete demo setup

## Components

### VoronoiGlassBreaker (Main Component)

The core component that handles glass breaking logic.

**Key Settings:**
- `cellCount`: Number of Voronoi cells (affects fragment count)
- `relaxationIterations`: Quality of Voronoi distribution
- `breakForce`: Minimum force required to break glass
- `enableProgressiveBreaking`: Enable gradual breaking from impact points
- `propagationSpeed`: How fast cracks spread
- `propagationRadius`: How far cracks can spread

### GlassFragment

Individual glass pieces created when breaking occurs.

**Features:**
- Physics simulation with customizable mass and drag
- Automatic lifetime management with fade-out
- Collision detection and bounce effects
- Visual scaling during fade-out

### VoronoiGenerator

Static utility class for generating Voronoi diagrams.

**Methods:**
- `GenerateVoronoi()`: Creates Voronoi cells within bounds
- Uses Lloyd's relaxation for better distribution

### MeshSlicer

Handles mesh cutting and UV preservation.

**Features:**
- Clips triangles to Voronoi cell boundaries
- Preserves UV coordinates and normals
- Creates individual meshes for each fragment

## Usage Examples

### Basic Glass Breaking

```csharp
// Get the glass breaker component
VoronoiGlassBreaker glassBreaker = GetComponent<VoronoiGlassBreaker>();

// Break glass at a specific point
Vector3 impactPoint = transform.position;
float impactForce = 15f;
glassBreaker.BreakGlass(impactPoint, impactForce);

// Repair the glass
glassBreaker.RepairGlass();
```

### Custom Break Settings

```csharp
var settings = glassBreaker.Settings;
settings.cellCount = 50;                    // More fragments
settings.enableProgressiveBreaking = true;  // Enable gradual breaking
settings.propagationSpeed = 5f;             // Fast crack propagation
settings.fragmentLifetime = 10f;            // Fragments last 10 seconds
```

### Event Handling

```csharp
// Subscribe to events
glassBreaker.OnGlassBreakStart += OnBreakStart;
glassBreaker.OnCellBreak += OnCellBreak;
glassBreaker.OnGlassFullyBroken += OnFullyBroken;

private void OnBreakStart(Vector3 impactPoint, float force)
{
    Debug.Log($"Glass breaking at {impactPoint} with force {force}");
}

private void OnCellBreak(VoronoiCell cell)
{
    Debug.Log($"Cell broken at {cell.seedPoint}");
}

private void OnFullyBroken()
{
    Debug.Log("Glass completely destroyed!");
}
```

## Configuration Tips

### For Realistic Glass
- Use 30-50 cells for good detail without performance issues
- Enable progressive breaking with moderate propagation speed (3-5)
- Set fragment lifetime to 8-12 seconds
- Use low fragment mass (0.1-0.3) for realistic movement

### For Stylized/Game Glass
- Use fewer cells (15-25) for larger fragments
- Disable progressive breaking for instant shattering
- Shorter fragment lifetime (3-5 seconds) for cleaner scenes
- Higher impact force multiplier for dramatic effects

### Performance Optimization
- Limit cell count based on target platform
- Use object pooling for fragments in high-frequency scenarios
- Adjust fragment lifetime based on scene requirements
- Consider LOD system for distant glass objects

## Audio Integration

The system includes `GlassAudioManager` for comprehensive audio support:

```csharp
// Setup audio
GlassAudioManager audioManager = GetComponent<GlassAudioManager>();
audioManager.PlayBreakSound(impactPoint, intensity);
audioManager.PlayCrackSound(crackPoint, intensity);
audioManager.StartAmbientSounds(); // For ongoing crack sounds
```

## Visual Effects

Use `GlassBreakEffect` for particle effects:

```csharp
// Create break effect
GlassBreakEffect.CreateBreakEffect(impactPoint, intensity);

// Or configure manually
GlassBreakEffect effect = GetComponent<GlassBreakEffect>();
effect.particleCount = 100;
effect.particleSpeed = 8f;
effect.TriggerEffect(position, intensity);
```

## Testing

Use `GlassBreakerTester` for easy testing:

- Left-click to break glass at mouse position
- Space key to break at random position
- R key to repair glass
- F key to shoot projectile

## Troubleshooting

### Glass Not Breaking
- **Missing "Breakable" tag**: Use `Tools > Voronoi Glass Breaker > Create Breakable Tag` to create it
- Check that colliding objects have the correct layer/tag
- Verify impact force meets the breakForce threshold
- Ensure the collider is set as trigger

### Poor Fragment Quality
- Increase cell count for more detailed fractures
- Adjust relaxation iterations for better distribution
- Check mesh complexity (very simple meshes may not slice well)

### Performance Issues
- Reduce cell count
- Lower fragment lifetime
- Implement object pooling for high-frequency breaking
- Use simpler physics materials

## Requirements

- Unity 2020.3 or later
- Standard Render Pipeline (URP/HDRP compatible with minor modifications)

## License

This system is provided as-is for educational and commercial use. No warranty provided.
