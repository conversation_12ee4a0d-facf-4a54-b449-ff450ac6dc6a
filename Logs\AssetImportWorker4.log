Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.38f1c1 (b17906c7b2b6) revision 11630854'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 48295 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\GameSoft\Unity\Editor\2022.3.38f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
D:/GameSoft/Unity/g-pro/My project
-logFile
Logs/AssetImportWorker4.log
-srvPort
62321
Successfully changed project path to: D:/GameSoft/Unity/g-pro/My project
D:/GameSoft/Unity/g-pro/My project
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12976]  Target information:

Player connection [12976]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2646259674 [EditorId] 2646259674 [Version] 1048832 [Id] WindowsEditor(7,Top1a-Va11hallA) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12976] Host joined multi-casting on [***********:54997]...
Player connection [12976] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 24.98 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.38f1c1 (b17906c7b2b6)
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/GameSoft/Unity/g-pro/My project/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 5070 Ti (ID=0x2c05)
    Vendor:   NVIDIA
    VRAM:     15907 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56404
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/GameSoft/Unity/Editor/2022.3.38f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005729 seconds.
- Loaded All Assemblies, in  0.473 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.142 seconds
Domain Reload Profiling: 613ms
	BeginReloadAssembly (60ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (99ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (58ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (272ms)
			TypeCache.Refresh (271ms)
				TypeCache.ScanAssembly (264ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (142ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (112ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (70ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.416 seconds
Refreshing native plugins compatible for Editor in 4.83 ms, found 6 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.378 seconds
Domain Reload Profiling: 792ms
	BeginReloadAssembly (82ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (16ms)
	RebuildCommonClasses (17ms)
	RebuildNativeTypeToScriptingClass (5ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (290ms)
		LoadAssemblies (186ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (149ms)
			TypeCache.Refresh (129ms)
				TypeCache.ScanAssembly (87ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (379ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (201ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
Refreshing native plugins compatible for Editor in 8.58 ms, found 6 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4930 Unused Serialized files (Serialized files now loaded: 0)
Unloading 99 unused Assets / (442.4 KB). Loaded Objects now: 5380.
Memory consumption went from 233.0 MB to 232.5 MB.
Total: 5.890400 ms (FindLiveObjects: 0.632700 ms CreateObjectMapping: 0.139400 ms MarkObjects: 4.686800 ms  DeleteObjects: 0.429700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 67270.370596 seconds.
  path: Assets/VoronoiGlassBreaker/Scripts/GlassAudioManager.cs
  artifactKey: Guid(f526889b14062e749968eb2fc981b266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VoronoiGlassBreaker/Scripts/GlassAudioManager.cs using Guid(f526889b14062e749968eb2fc981b266) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '4d82ced86be36eb7d23cde40ec2dd8f0') in 0.002024 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 15.296696 seconds.
  path: Assets/New Scene 1.unity
  artifactKey: Guid(2d615c126fbc92340bd175e37b585b39) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Scene 1.unity using Guid(2d615c126fbc92340bd175e37b585b39) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '45637f07659821ef7541a70dd92bd2d8') in 0.000550 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.492094 seconds.
  path: Assets/New Scene.unity
  artifactKey: Guid(de729eba75f68d5449fd1ec5e0027317) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Scene.unity using Guid(de729eba75f68d5449fd1ec5e0027317) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'f205101593471b2d1999f298bf463ac9') in 0.000605 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 5.249882 seconds.
  path: Assets/Scenes/Cockpit/Art/Environment/Prefabs/Asteroid_01_Prefab.prefab
  artifactKey: Guid(065b867415dad40488c6ddaa805a189b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Cockpit/Art/Environment/Prefabs/Asteroid_01_Prefab.prefab using Guid(065b867415dad40488c6ddaa805a189b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '2f01d33a790744b57874264635c9a110') in 0.197400 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BambooTube_01_Prefab.prefab
  artifactKey: Guid(7b12f1be31c08374480ef7062c030dc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BambooTube_01_Prefab.prefab using Guid(7b12f1be31c08374480ef7062c030dc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'adf35e57d8fc3627345454681be1f925') in 0.057323 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 41
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_13x16x380_03_Prefab.prefab
  artifactKey: Guid(3dad38c4e4fa75f46b02a786e7a15266) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_13x16x380_03_Prefab.prefab using Guid(3dad38c4e4fa75f46b02a786e7a15266) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'fd87f4f8c78280fd8adb636824f6d032') in 0.061622 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 44
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BambooStructure_Top_01_Prefab.prefab
  artifactKey: Guid(ef21bcd5cfd5866439483a134baa4159) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BambooStructure_Top_01_Prefab.prefab using Guid(ef21bcd5cfd5866439483a134baa4159) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e16cddccfca00f7de053a4cc81843162') in 0.024139 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 45
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Scenes/Garden/Art/Vegetation/Meshes/BambooSeedling_Mat.fbx
  artifactKey: Guid(65686d4c68643e743b7d6461714cb198) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Vegetation/Meshes/BambooSeedling_Mat.fbx using Guid(65686d4c68643e743b7d6461714cb198) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '2154fa05eca2e396d21b09088f9b0362') in 0.065405 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 22
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Meshes/BeamWooden_13x16x380_01_Mesh.fbx
  artifactKey: Guid(e4e9d833e012dd148a2bcfed2c7e6b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Meshes/BeamWooden_13x16x380_01_Mesh.fbx using Guid(e4e9d833e012dd148a2bcfed2c7e6b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '15927707da0d846829cb437227e711a0') in 0.047985 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_13x16x380_01_Prefab.prefab
  artifactKey: Guid(306aa234f6abbe841a17615818f8b30f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Garden/Art/Architecture/Prefabs/BeamWooden_13x16x380_01_Prefab.prefab using Guid(306aa234f6abbe841a17615818f8b30f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'eec8fdf6e7ee40686813621283f5b7e4') in 0.024809 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 45
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error: operation aborted. errorcode: 995, details: 由于线程退出或应用程序请求，已中止 I/O 操作。
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0