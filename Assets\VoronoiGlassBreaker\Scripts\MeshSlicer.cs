using UnityEngine;
using System.Collections.Generic;

namespace VoronoiGlassBreaker
{
    /// <summary>
    /// Handles mesh slicing and fragment generation while preserving UVs
    /// </summary>
    public static class MeshSlicer
    {
        /// <summary>
        /// Slice a mesh based on Voronoi cells and create fragments
        /// </summary>
        public static List<Mesh> SliceMesh(Mesh originalMesh, List<VoronoiCell> cells, Transform meshTransform)
        {
            List<Mesh> fragments = new List<Mesh>();

            if (originalMesh == null || cells == null || cells.Count == 0)
            {
                Debug.LogWarning("MeshSlicer: Invalid input parameters");
                return fragments;
            }

            // Get original mesh data
            Vector3[] vertices = originalMesh.vertices;
            Vector2[] uvs = originalMesh.uv;
            Vector3[] normals = originalMesh.normals;
            int[] triangles = originalMesh.triangles;

            if (vertices.Length == 0 || triangles.Length == 0)
            {
                Debug.LogWarning("MeshSlicer: Original mesh has no vertices or triangles");
                return fragments;
            }
            
            // Convert mesh bounds to 2D for Voronoi mapping
            Bounds bounds = originalMesh.bounds;
            Rect uvBounds = new Rect(0, 0, 1, 1); // UV space bounds
            
            // Create a fragment for each cell
            for (int cellIndex = 0; cellIndex < cells.Count; cellIndex++)
            {
                VoronoiCell cell = cells[cellIndex];
                Mesh fragment = CreateFragmentMesh(cell, vertices, uvs, normals, triangles, bounds, uvBounds);
                
                if (fragment != null && fragment.vertexCount > 0)
                {
                    fragments.Add(fragment);
                }
            }
            
            return fragments;
        }
        
        /// <summary>
        /// Create a mesh fragment for a specific Voronoi cell
        /// </summary>
        private static Mesh CreateFragmentMesh(VoronoiCell cell, Vector3[] originalVertices, Vector2[] originalUVs, 
            Vector3[] originalNormals, int[] originalTriangles, Bounds meshBounds, Rect uvBounds)
        {
            List<Vector3> fragmentVertices = new List<Vector3>();
            List<Vector2> fragmentUVs = new List<Vector2>();
            List<Vector3> fragmentNormals = new List<Vector3>();
            List<int> fragmentTriangles = new List<int>();
            
            // Map cell vertices to UV space for intersection testing
            List<Vector2> cellUVVertices = new List<Vector2>();
            foreach (Vector2 vertex in cell.vertices)
            {
                // Convert from Voronoi space to UV space
                Vector2 uvVertex = new Vector2(
                    Mathf.InverseLerp(0, 1, vertex.x),
                    Mathf.InverseLerp(0, 1, vertex.y)
                );
                cellUVVertices.Add(uvVertex);
            }
            
            // Process each triangle in the original mesh
            for (int i = 0; i < originalTriangles.Length; i += 3)
            {
                int v0 = originalTriangles[i];
                int v1 = originalTriangles[i + 1];
                int v2 = originalTriangles[i + 2];
                
                Vector2 uv0 = originalUVs[v0];
                Vector2 uv1 = originalUVs[v1];
                Vector2 uv2 = originalUVs[v2];
                
                // Check if triangle intersects with this cell
                if (TriangleIntersectsCell(uv0, uv1, uv2, cellUVVertices))
                {
                    // Clip triangle to cell boundary
                    List<TriangleData> clippedTriangles = ClipTriangleToCell(
                        new TriangleData(originalVertices[v0], originalVertices[v1], originalVertices[v2],
                                       uv0, uv1, uv2,
                                       originalNormals[v0], originalNormals[v1], originalNormals[v2]),
                        cellUVVertices
                    );
                    
                    // Add clipped triangles to fragment
                    foreach (TriangleData triangle in clippedTriangles)
                    {
                        int baseIndex = fragmentVertices.Count;
                        
                        fragmentVertices.Add(triangle.v0);
                        fragmentVertices.Add(triangle.v1);
                        fragmentVertices.Add(triangle.v2);
                        
                        fragmentUVs.Add(triangle.uv0);
                        fragmentUVs.Add(triangle.uv1);
                        fragmentUVs.Add(triangle.uv2);
                        
                        fragmentNormals.Add(triangle.n0);
                        fragmentNormals.Add(triangle.n1);
                        fragmentNormals.Add(triangle.n2);
                        
                        fragmentTriangles.Add(baseIndex);
                        fragmentTriangles.Add(baseIndex + 1);
                        fragmentTriangles.Add(baseIndex + 2);
                    }
                }
            }
            
            // Create mesh if we have valid geometry
            if (fragmentVertices.Count == 0 || fragmentTriangles.Count == 0)
            {
                // Create a simple fallback mesh (small cube) for this cell
                return CreateFallbackMesh(cell);
            }

            // Ensure we have enough vertices for the triangles
            if (fragmentTriangles.Count > 0 && fragmentVertices.Count < 3)
            {
                return CreateFallbackMesh(cell);
            }

            Mesh fragmentMesh = new Mesh();
            fragmentMesh.name = $"Fragment_{cell.seedPoint.x:F2}_{cell.seedPoint.y:F2}";
            fragmentMesh.vertices = fragmentVertices.ToArray();
            fragmentMesh.uv = fragmentUVs.ToArray();
            fragmentMesh.normals = fragmentNormals.ToArray();
            fragmentMesh.triangles = fragmentTriangles.ToArray();

            // Recalculate bounds and tangents
            fragmentMesh.RecalculateBounds();
            fragmentMesh.RecalculateNormals(); // Recalculate normals in case they're wrong
            fragmentMesh.RecalculateTangents();

            return fragmentMesh;
        }
        
        /// <summary>
        /// Create a simple fallback mesh when slicing fails
        /// </summary>
        private static Mesh CreateFallbackMesh(VoronoiCell cell)
        {
            Mesh fallbackMesh = new Mesh();
            fallbackMesh.name = $"FallbackFragment_{cell.seedPoint.x:F2}_{cell.seedPoint.y:F2}";

            // Create a small cube
            Vector3[] vertices = {
                new Vector3(-0.05f, -0.05f, -0.05f),
                new Vector3( 0.05f, -0.05f, -0.05f),
                new Vector3( 0.05f,  0.05f, -0.05f),
                new Vector3(-0.05f,  0.05f, -0.05f),
                new Vector3(-0.05f, -0.05f,  0.05f),
                new Vector3( 0.05f, -0.05f,  0.05f),
                new Vector3( 0.05f,  0.05f,  0.05f),
                new Vector3(-0.05f,  0.05f,  0.05f)
            };

            int[] triangles = {
                0, 2, 1, 0, 3, 2, // Front
                4, 5, 6, 4, 6, 7, // Back
                0, 1, 5, 0, 5, 4, // Bottom
                3, 7, 6, 3, 6, 2, // Top
                0, 4, 7, 0, 7, 3, // Left
                1, 2, 6, 1, 6, 5  // Right
            };

            Vector2[] uvs = {
                new Vector2(0, 0), new Vector2(1, 0), new Vector2(1, 1), new Vector2(0, 1),
                new Vector2(0, 0), new Vector2(1, 0), new Vector2(1, 1), new Vector2(0, 1)
            };

            fallbackMesh.vertices = vertices;
            fallbackMesh.triangles = triangles;
            fallbackMesh.uv = uvs;
            fallbackMesh.RecalculateNormals();
            fallbackMesh.RecalculateBounds();

            return fallbackMesh;
        }

        /// <summary>
        /// Check if a triangle intersects with a Voronoi cell
        /// </summary>
        private static bool TriangleIntersectsCell(Vector2 uv0, Vector2 uv1, Vector2 uv2, List<Vector2> cellVertices)
        {
            // Check if any triangle vertex is inside the cell
            if (IsPointInPolygon(uv0, cellVertices) || 
                IsPointInPolygon(uv1, cellVertices) || 
                IsPointInPolygon(uv2, cellVertices))
            {
                return true;
            }
            
            // Check if any cell vertex is inside the triangle
            foreach (Vector2 cellVertex in cellVertices)
            {
                if (IsPointInTriangle(cellVertex, uv0, uv1, uv2))
                {
                    return true;
                }
            }
            
            // Check for edge intersections
            Vector2[] triangleEdges = { uv0, uv1, uv1, uv2, uv2, uv0 };
            for (int i = 0; i < triangleEdges.Length; i += 2)
            {
                for (int j = 0; j < cellVertices.Count; j++)
                {
                    Vector2 cellEdgeStart = cellVertices[j];
                    Vector2 cellEdgeEnd = cellVertices[(j + 1) % cellVertices.Count];
                    
                    if (LineSegmentsIntersect(triangleEdges[i], triangleEdges[i + 1], cellEdgeStart, cellEdgeEnd))
                    {
                        return true;
                    }
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// Clip a triangle to a Voronoi cell boundary
        /// </summary>
        private static List<TriangleData> ClipTriangleToCell(TriangleData triangle, List<Vector2> cellVertices)
        {
            List<TriangleData> result = new List<TriangleData>();
            
            // For simplicity, if triangle intersects cell, include the whole triangle
            // In a more advanced implementation, you would perform actual polygon clipping
            result.Add(triangle);
            
            return result;
        }
        
        /// <summary>
        /// Check if a point is inside a polygon using ray casting
        /// </summary>
        private static bool IsPointInPolygon(Vector2 point, List<Vector2> polygon)
        {
            bool inside = false;
            int j = polygon.Count - 1;
            
            for (int i = 0; i < polygon.Count; i++)
            {
                Vector2 vi = polygon[i];
                Vector2 vj = polygon[j];
                
                if (((vi.y > point.y) != (vj.y > point.y)) &&
                    (point.x < (vj.x - vi.x) * (point.y - vi.y) / (vj.y - vi.y) + vi.x))
                {
                    inside = !inside;
                }
                j = i;
            }
            
            return inside;
        }
        
        /// <summary>
        /// Check if a point is inside a triangle using barycentric coordinates
        /// </summary>
        private static bool IsPointInTriangle(Vector2 point, Vector2 a, Vector2 b, Vector2 c)
        {
            Vector2 v0 = c - a;
            Vector2 v1 = b - a;
            Vector2 v2 = point - a;
            
            float dot00 = Vector2.Dot(v0, v0);
            float dot01 = Vector2.Dot(v0, v1);
            float dot02 = Vector2.Dot(v0, v2);
            float dot11 = Vector2.Dot(v1, v1);
            float dot12 = Vector2.Dot(v1, v2);
            
            float invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
            float u = (dot11 * dot02 - dot01 * dot12) * invDenom;
            float v = (dot00 * dot12 - dot01 * dot02) * invDenom;
            
            return (u >= 0) && (v >= 0) && (u + v <= 1);
        }
        
        /// <summary>
        /// Check if two line segments intersect
        /// </summary>
        private static bool LineSegmentsIntersect(Vector2 p1, Vector2 q1, Vector2 p2, Vector2 q2)
        {
            int o1 = Orientation(p1, q1, p2);
            int o2 = Orientation(p1, q1, q2);
            int o3 = Orientation(p2, q2, p1);
            int o4 = Orientation(p2, q2, q1);
            
            // General case
            if (o1 != o2 && o3 != o4) return true;
            
            // Special cases for collinear points
            if (o1 == 0 && OnSegment(p1, p2, q1)) return true;
            if (o2 == 0 && OnSegment(p1, q2, q1)) return true;
            if (o3 == 0 && OnSegment(p2, p1, q2)) return true;
            if (o4 == 0 && OnSegment(p2, q1, q2)) return true;
            
            return false;
        }
        
        /// <summary>
        /// Find orientation of ordered triplet (p, q, r)
        /// </summary>
        private static int Orientation(Vector2 p, Vector2 q, Vector2 r)
        {
            float val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
            if (Mathf.Abs(val) < 0.0001f) return 0; // Collinear
            return (val > 0) ? 1 : 2; // Clockwise or Counterclockwise
        }
        
        /// <summary>
        /// Check if point q lies on line segment pr
        /// </summary>
        private static bool OnSegment(Vector2 p, Vector2 q, Vector2 r)
        {
            return q.x <= Mathf.Max(p.x, r.x) && q.x >= Mathf.Min(p.x, r.x) &&
                   q.y <= Mathf.Max(p.y, r.y) && q.y >= Mathf.Min(p.y, r.y);
        }
        
        /// <summary>
        /// Data structure to hold triangle information
        /// </summary>
        private struct TriangleData
        {
            public Vector3 v0, v1, v2;
            public Vector2 uv0, uv1, uv2;
            public Vector3 n0, n1, n2;
            
            public TriangleData(Vector3 vertex0, Vector3 vertex1, Vector3 vertex2,
                              Vector2 uv0, Vector2 uv1, Vector2 uv2,
                              Vector3 normal0, Vector3 normal1, Vector3 normal2)
            {
                this.v0 = vertex0; this.v1 = vertex1; this.v2 = vertex2;
                this.uv0 = uv0; this.uv1 = uv1; this.uv2 = uv2;
                this.n0 = normal0; this.n1 = normal1; this.n2 = normal2;
            }
        }
    }
}
