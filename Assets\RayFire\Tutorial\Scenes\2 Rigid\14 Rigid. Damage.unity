%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 10
    m_AtlasSize: 512
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 256
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &57226997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 57226998}
  - component: {fileID: 57227001}
  - component: {fileID: 57227000}
  - component: {fileID: 57226999}
  m_Layer: 0
  m_Name: Box_100_far
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &57226998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 57226997}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 5, y: 0.525, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 756469939}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &57226999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 57226997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
    physicsDataCorState: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 1
    tag: 
    depth: 1
    time: 0.1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0.8
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 1
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &57227000
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 57226997}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &57227001
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 57226997}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &186470116
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 186470117}
  m_Layer: 0
  m_Name: ByCollision
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &186470117
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 186470116}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -21.58, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1956765049}
  - {fileID: 2102178638}
  - {fileID: 506148595}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &236701389
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 709178317}
    m_Modifications:
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: damage.enable
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: simulationType
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: damage.maxDamage
      value: 40
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: limitations.time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: limitations.solidity
      value: 1.21
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
--- !u!114 &236701390 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
    type: 3}
  m_PrefabInstance: {fileID: 236701389}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &236701391 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332,
    type: 3}
  m_PrefabInstance: {fileID: 236701389}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &244384312
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 244384313}
  - component: {fileID: 244384316}
  - component: {fileID: 244384315}
  - component: {fileID: 244384314}
  m_Layer: 0
  m_Name: Box_100_close
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &244384313
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244384312}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2, y: 0.525, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 756469939}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &244384314
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244384312}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
    physicsDataCorState: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 1
    tag: 
    depth: 1
    time: 0.1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0.8
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 1
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &244384315
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244384312}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &244384316
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 244384312}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &368775759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 368775760}
  - component: {fileID: 368775763}
  - component: {fileID: 368775762}
  - component: {fileID: 368775761}
  m_Layer: 0
  m_Name: Box_50_close
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &368775760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 368775759}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2, y: 0.525, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 756469939}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &368775761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 368775759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
    physicsDataCorState: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 1
    tag: 
    depth: 1
    time: 0.1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0.8
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 1
    maxDamage: 50
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &368775762
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 368775759}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &368775763
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 368775759}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &506148594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 506148595}
  - component: {fileID: 506148598}
  - component: {fileID: 506148597}
  - component: {fileID: 506148596}
  m_Layer: 0
  m_Name: Block (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &506148595
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506148594}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.99, y: 7.87, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 5}
  m_Children: []
  m_Father: {fileID: 186470117}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &506148596
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506148594}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
    physicsDataCorState: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0.8
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &506148597
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506148594}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &506148598
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506148594}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &534135899
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 534135900}
  - component: {fileID: 534135901}
  m_Layer: 0
  m_Name: ApplyDamage
  m_TagString: Untagged
  m_Icon: {fileID: -964228994112308473, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &534135900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 534135899}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.99, y: 3.34, z: -0.21}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 709178317}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &534135901
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 534135899}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 266627bc17527794fa2210266ccf429d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  rigid: {fileID: 236701390}
  damageValue: 30
  damagePoint: {fileID: 544895922}
  damageRadius: 1
  coll: {fileID: 0}
--- !u!1 &544895921
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 544895922}
  m_Layer: 0
  m_Name: DamagePoint
  m_TagString: Untagged
  m_Icon: {fileID: 7174288486110832750, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &544895922
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 544895921}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.51, z: -0.07}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 709178317}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &709178316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 709178317}
  m_Layer: 0
  m_Name: ByMethod
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &709178317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 709178316}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 15, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 236701391}
  - {fileID: 534135900}
  - {fileID: 544895922}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &756469938
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 756469939}
  m_Layer: 0
  m_Name: ByBomb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &756469939
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 756469938}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -5.82, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1478550360}
  - {fileID: 57226998}
  - {fileID: 244384313}
  - {fileID: 368775760}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &884791226 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332,
    type: 3}
  m_PrefabInstance: {fileID: 2052321732}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1174400859
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1174400860}
  m_Layer: 0
  m_Name: ByGun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1174400860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1174400859}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 7.9, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 884791226}
  - {fileID: 1718596956}
  m_Father: {fileID: 2116671453}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1445489124
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1910325183980152, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4259816824953554, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 22.2
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 16.36
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: -22.73637
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 20.90508
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4289062719471492, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 34.9
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -39.48
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.98553336
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.16948168
      objectReference: {fileID: 0}
    - target: {fileID: 4352528364258364, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 19.515001
      objectReference: {fileID: 0}
    - target: {fileID: 4579783213449530, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: -40.18
      objectReference: {fileID: 0}
    - target: {fileID: 23019430021055472, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 18c5c2169b9f5bd439ca94baa6e8e7ec, type: 2}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: atStart
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: initialization
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114689447726362688, guid: 0f7b67bc08186d14ba66630d8e7527ef,
        type: 3}
      propertyPath: simulationType
      value: 4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0f7b67bc08186d14ba66630d8e7527ef, type: 3}
--- !u!1 &1478550359
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1478550360}
  - component: {fileID: 1478550361}
  m_Layer: 0
  m_Name: Bomb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1478550360
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1478550359}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 756469939}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1478550361
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1478550359}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dafd0a8e9ee856a49bf432688899df59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  showGizmo: 1
  rangeType: 0
  fadeType: 0
  range: 7.4309616
  deletion: 0
  strength: 1
  variation: 50
  chaos: 30
  forceByMass: 0
  affectInactive: 0
  affectKinematic: 0
  heightOffset: 0
  delay: 0
  atStart: 0
  destroy: 0
  applyDamage: 1
  damageValue: 100
  curve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: -1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.7
      value: 0
      inSlope: -1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: -1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  play: 0
  volume: 1
  clip: {fileID: 0}
  bombPosition: {x: -5.82, y: 0, z: 0}
  explPosition: {x: -5.82, y: 0, z: 0}
  colliders: []
  rigidbodies: []
  mask: -1
  tagFilter: Untagged
--- !u!1 &1718596955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1718596956}
  - component: {fileID: 1718596957}
  m_Layer: 0
  m_Name: Gun
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1718596956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1718596955}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.05, y: 4.17, z: -5.66}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1174400860}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1718596957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1718596955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6aae98af4160c3f44a105545f7b49970, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: 2
  maxDistance: 50
  target: {fileID: 0}
  rounds: 1
  rate: 0.3
  type: 0
  strength: 1.74
  radius: 1
  demolishCluster: 1
  affectInactive: 1
  rigid: 1
  rigidRoot: 1
  rigidBody: 1
  damage: 55.2
  debris: 1
  dust: 1
  flash: 1
  Flash:
    intensityMin: 0.5
    intensityMax: 0.7
    rangeMin: 5
    rangeMax: 7
    distance: 0.4
    color: {r: 1, g: 1, b: 0.8, a: 1}
  mask: -1
  tagFilter: Untagged
  showRay: 1
  showHit: 1
  shooting: 0
--- !u!1 &1956765048
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1956765049}
  - component: {fileID: 1956765052}
  - component: {fileID: 1956765051}
  - component: {fileID: 1956765050}
  m_Layer: 0
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1956765049
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956765048}
  m_LocalRotation: {x: -0.2337299, y: 0.69786304, z: 0.64861387, w: 0.19405568}
  m_LocalPosition: {x: 0, y: 13.87, z: -0.43}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 186470117}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -84.874, y: 201.22899, z: -56.480003}
--- !u!114 &1956765050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956765048}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 0
  objectType: 0
  demolitionType: 1
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
    physicsDataCorState: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 2
    tag: 
    depth: 1
    time: 0.1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0.8
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 1
    maxDamage: 60
    currentDamage: 0
    collect: 1
    multiplier: 1
    toShards: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &1956765051
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956765048}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &1956765052
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956765048}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1001 &2052321732
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1174400860}
    m_Modifications:
    - target: {fileID: 1327702833991186, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_Name
      value: Column_cls_conn
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4862060477811598, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: damage.enable
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: simulationType
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: damage.maxDamage
      value: 40
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: limitations.time
      value: 0.05
      objectReference: {fileID: 0}
    - target: {fileID: 114498410513813532, guid: 3f7470793a33ce9459a6a0ae084ba332,
        type: 3}
      propertyPath: limitations.solidity
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 3f7470793a33ce9459a6a0ae084ba332, type: 3}
--- !u!1 &2102178637
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2102178638}
  - component: {fileID: 2102178641}
  - component: {fileID: 2102178640}
  - component: {fileID: 2102178639}
  m_Layer: 0
  m_Name: Block
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2102178638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2102178637}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.91, y: 11.47, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 5}
  m_Children: []
  m_Father: {fileID: 186470117}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2102178639
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2102178637}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eb6e52f37bca2404fac00b69fd8d21f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  initialization: 1
  simulationType: 3
  objectType: 0
  demolitionType: 0
  physics:
    materialType: 4
    material: {fileID: 0}
    massBy: 0
    mass: 0
    colliderType: 0
    planarCheck: 1
    ignoreNear: 0
    useGravity: 1
    solverIterations: 6
    dampening: 0.8
    rigidBody: {fileID: 0}
    meshCollider: {fileID: 0}
    clusterColliders: []
    ignoreList: 
    rec: 0
    exclude: 0
    solidity: 1
    destructible: 0
    physicsDataCorState: 0
  activation:
    local: 0
    byOffset: 0
    byVelocity: 0
    byDamage: 0
    byActivator: 0
    byImpact: 0
    byConnectivity: 0
    unyielding: 0
    activatable: 0
    l: 0
    layer: 0
    connect: {fileID: 0}
  limitations:
    byCollision: 1
    solidity: 0.1
    tag: 
    depth: 1
    time: 1
    size: 0.1
    visible: 0
    sliceByBlade: 0
    bound:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0, y: 0, z: 0}
  meshDemolition:
    amount: 15
    variation: 0
    depthFade: 0.5
    contactBias: 0.8
    seed: 1
    useShatter: 0
    addChildren: 1
    clusterize: 0
    simType: 4
    meshInput: 9
    properties:
      colliderType: 0
      sizeFilter: 0
      decompose: 0
      removeCollinear: 1
      l: 1
      m: 0
      layer: 0
      t: 1
      tag: 
    runtimeCaching:
      type: 0
      frames: 3
      fragments: 4
      skipFirstDemolition: 0
      inProgress: 0
      wasUsed: 0
      stop: 0
    scrShatter: {fileID: 0}
    cacheRotationStart: {x: 0, y: 0, z: 0, w: 1}
  clusterDemolition:
    connectivity: 0
    minimumArea: 0
    minimumSize: 0
    percentage: 0
    seed: 0
    type: 0
    ratio: 15
    units: 1
    shardArea: 100
    shardDemolition: 0
    minAmount: 3
    maxAmount: 6
    demolishable: 1
    collapse:
      type: 1
      start: 0
      end: 75
      steps: 10
      duration: 15
    clsCount: 1
    cluster:
      id: 0
      tm: {fileID: 0}
      depth: 0
      pos: {x: 0, y: 0, z: 0}
      rot: {x: 0, y: 0, z: 0, w: 0}
      scl: {x: 0, y: 0, z: 0}
      bound:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
      demolishable: 1
      rigid: {fileID: 0}
      shards: []
      areaCollapse: 0
      minimumArea: 0
      maximumArea: 0
      sizeCollapse: 0
      minimumSize: 0
      maximumSize: 0
      randomCollapse: 0
      randomSeed: 0
      cachedHost: 0
    minorClusters: []
    cn: 0
    nd: 0
    am: 0
  referenceDemolition:
    reference: {fileID: 0}
    randomList: []
    action: 0
    addRigid: 1
    inheritScale: 1
    inheritMaterials: 0
  materials:
    innerMaterial: {fileID: 0}
    mappingScale: 0.1
    outerMaterial: {fileID: 0}
    needNewMat: 0
  damage:
    enable: 0
    maxDamage: 100
    currentDamage: 0
    collect: 0
    multiplier: 1
    toShards: 1
  fading:
    onDemolition: 1
    onActivation: 0
    byOffset: 0
    lifeType: 4
    lifeTime: 10
    lifeVariation: 3
    fadeType: 0
    fadeTime: 5
    sizeFilter: 0
    shardAmount: 5
  reset:
    transform: 1
    damage: 1
    connectivity: 0
    action: 0
    destroyDelay: 1
    mesh: 4
    fragments: 0
  initialized: 0
  corState: 0
  meshes: []
  pivots: []
  rfMeshes: []
  subIds: []
  fragments: []
  cacheRotation: {x: 0, y: 0, z: 0, w: 0}
  transForm: {fileID: 0}
  rootChild: {fileID: 0}
  rootParent: {fileID: 0}
  meshFilter: {fileID: 0}
  meshRenderer: {fileID: 0}
  skinnedMeshRend: {fileID: 0}
  debrisList: []
  dustList: []
  restriction: {fileID: 0}
  sound: {fileID: 0}
--- !u!23 &2102178640
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2102178637}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &2102178641
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2102178637}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2116671452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2116671453}
  m_Layer: 0
  m_Name: Damage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2116671453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2116671452}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 186470117}
  - {fileID: 756469939}
  - {fileID: 1174400860}
  - {fileID: 709178317}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
