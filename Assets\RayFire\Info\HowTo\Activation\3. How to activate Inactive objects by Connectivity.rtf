{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to activate Inactive objects by Connectivity\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Sphere\b0 . This is the object which will collide with Inactive fragments and activate them by collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Rock\i0 " and \b position \b0 to [0,10,0]\line\par
{\pntext\f0 3.\tab}Add \b RayFire Rigid \b0 component to Rock and set \b Initialization \b0 to \b At Start.\b0\line\par
{\pntext\f0 4.\tab}Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [\f1\lang1049 5\f0\lang9 ,1,10]\line\par
{\pntext\f0 6.\tab}Create second \b Cube\b0 .\line\par
{\pntext\f0 7.\tab}Set its \b name \b0 to "\i Wall\i0 ", \b position \b0 to [0,3,-4] and \b scale \b0 to [1,5,1]\line\par
{\pntext\f0 8.\tab}Create third \b Cube\b0 . \line\par
{\pntext\f0 9.\tab}Set its \b name \b0 to "\i Floor\i0 ", \b position \b0 to [0,5,0] and \b scale \b0 to [2, 0.2, 7]\line\par
{\pntext\f0 10.\tab}Add \b RayFire Shatter \b0 to the Floor object, in \b Voronoi \b0 properties set \b Amount \b0 to \b 300\b0  and click the \b Fragment \b0 button. New object Floor_root will be created.\line\par
{\pntext\f0 11.\tab}\b Destroy \b0 or \b Deactivate \b0 Floor object, we won\rquote t need it anymore.\line\par
{\pntext\f0 12.\tab}Add \b RayFire Rigid \b0 to the Floor_root object and set \b Initialization \b0 to \b At Start\b0 .\line\par
{\pntext\f0 13.\tab}Set \b Simulation type \b0 to \b Inactive\b0 . Inactive objects simulate like dynamic objects but they always have 0 gravity and 0 velocity until they will be activated.\line\par
{\pntext\f0 14.\tab}Set \b Object type \b0 to \b Mesh Root\b0 . It means that this Rigid component will be copied to all children objects with mesh with object Type Mesh instead of Mesh Root. \line\par
{\pntext\f0 15.\tab}In \b Activation \b0 properties set \b Velocity \b0 property to \b 0.3 \b0 and \b Offset \b0 property to \b 0.1. \b0\line\par
{\pntext\f0 16.\tab}\b Start \b0 Play Mode. \line\line\tab Rock will fall to the floor fragments and activate a group of fragments at the middle. Notice that a group of fragments at the Wall side still stays in the air and it looks logically correct, but another group of fragments at the other side not connected with anything and is \b freezing \b0 in the air. Obviously this looks unrealistic and to fix this we need to activate such fragments using \b Connectivity \b0 component.\line\par
{\pntext\f0 17.\tab}\b Turn Off \b0 Play Mode. \line\par
{\pntext\f0 18.\tab}\b Select \b0 Floor_root object and in \b Activation \b0 properties enable \b Connectivity\b0 .\line\par
{\pntext\f0 19.\tab}Add \b RayFire Connectivity \b0 component.\line\par
{\pntext\f0 20.\tab}\b Start \b0 Play Mode.\line\line\tab Notice that now all fragments are connected with each other by the \b green \b0 lines. Now Rock falls to the floor fragments, activates a group of fragments at the middle and both groups from the left and from the right side fall down with \b blue \b0 connections as \b Connected Clusters\b0 . This happens because the Connectivity component checks connections among fragments and activates them if they are not connected through other connections with at least one \b Unyielding \b0 fragment. \line\par
{\pntext\f0 21.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 22.\tab}\b Select \b0 Floor_root object and add \b RayFire Unyielding \b0 component. \line\line\tab Now you can move Unyielding component gizmo to overlap fragments which you want to define as Unyielding, usually these are objects at the bottom of your structure but in this case we need to overlap several floor fragments near the Wall object.  \line\tab You can move Unyielding gizmo by clicking on the \b Show Center \b0 button and then moving gizmo center by \b hand tool\b0 . But for now we will input values in Center Position property.\line\par
{\pntext\f0 23.\tab}Set \b Center \b0 to \b [0,0,-3]\line\b0\par
{\pntext\f0 24.\tab}\b Start \b0 Play Mode.\line\line\tab Notice that now all connections inside Unyielding gizmo changed their color to \b Red\b0 . This time group of fragments which is connected with the Wall object stays intact and only a group of fragments which is not connected with anything fall down as one Connected Cluster.\line\par
{\pntext\f0 25.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 26.\tab}\b Select \b0 Floor_root object and in the \b Connectivity \b0 component disable \b Clusterize \b0 property.\line\par
{\pntext\f0 27.\tab}\b Start \b0 Play Mode.\line\line\tab Now a group of fragments which are not connected with the wall fall down as \b separate \b0 objects and not as one Connected Cluster. It is also possible to activate this group as one Connected Connected cluster but then demolish it when it collides with ground.\line\par
{\pntext\f0 28.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 29.\tab}\b Select \b0 Floor_root object and in the \b Connectivity \b0 component enable \b Clusterize \b0 and \b Demolishable \b0 properties\line\par
{\pntext\f0 30.\tab}\b Start \b0 Play Mode.\line\line\tab Notice that Connected Cluster demolishes at contact point, but there are very few fragments that were detached. In order to change this you need to change Cluster Demolition properties in the Rigid Component. First Rigid with Mesh Root object type copies itself to every child and when such fragments are grouped into Connected Cluster it gets all its properties from one of them.\line\par
{\pntext\f0 31.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 32.\tab}\b Select \b0 Floor_root object and in the \b Rigid \b0 component in \b Cluster Demolition \b0 properties set \b Ratio \b0 property to \b 50\b0\line\par
{\pntext\f0 33.\tab}\b Start \b0 Play Mode.\line\par

\pard\nowidctlpar\sl276\slmult1\tab\tab Now the Connected Cluster detaches more  fragments at collision.\par
\line\par
\b\par
}
 