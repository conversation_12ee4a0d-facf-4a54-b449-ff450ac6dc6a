{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish Mesh object in runtime to radial fragments.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Glass\i0 ", \b position \b0 to [0,5,0] and \b scale \b0 to [5,1,5]\line\par
{\pntext\f0 3.\tab}\b Remove \b0 it's Box Collider because Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "\i Ground\i0 ", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 6.\tab}Add \b RayFire Rigid \b0 component to Glass.\line\par
{\pntext\f0 7.\tab}Set Rigid \b Initialization to At Start\b0 .\line\par
{\pntext\f0 8.\tab}Set \b Simulation \b0 type to \b Sleeping\b0 .\line\par
{\pntext\f0 9.\tab}Set \b Object \b0 type to \b Mesh\b0 .\line\par
{\pntext\f0 10.\tab}Set \b Demolition \b0 type to \b Runtime\b0 .\line\par
{\pntext\f0 11.\tab}Create \b Sphere\b0 . This is the object which will collide with Glass and demolish it.\line\par
{\pntext\f0 12.\tab}Set its \b name \b0 to "\i Rock\i0 " and \b position \b0 to [0,10,0]\line\par
{\pntext\f0 13.\tab}Add \b RayFire Rigid \b0 component to Rock and set \b Initialization \b0 to \b At Start.\b0\line\par
{\pntext\f0 14.\tab}\b Start \b0 Play Mode. \line\line\tab Rock will fall to the Glass object and will demolish it to \b default \b0 Voronoi fragments. In order to demolish objects in \b Runtime \b0 to other fragmentation patterns which are available in RayFire Shatter component you should use it as a properties container.\line\par
{\pntext\f0 15.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 16.\tab}\b Select \b0 Glass object and add \b RayFire Shatter \b0 component.\line\par
{\pntext\f0 17.\tab}In the Shatter component set \b Type \b0 to \b Radial\b0 .\line\par
{\pntext\f0 18.\tab}In \b Radial \b0 properties set \b Radius \b0 to 3 and \b Divergence \b0 to 0.2\line\par
{\pntext\f0 19.\tab}In the \b RayFire Rigid \b0 component in \b Mesh Demolition \b0 properties enable \b Use Shatter\b0 . It is very \b important \b0 to enable this property to demolish objects to other fragmentation types.\line\par
{\pntext\f0 20.\tab}\b Start \b0 Play Mode. \line\line\tab Rock will fall to the Glass object and will demolish it to \b radial \b0 fragments.\line\par
{\pntext\f0 21.\tab}\b Turn Off \b0 Play Mode.\line\par
{\pntext\f0 22.\tab}\b Select \b0 Rock and set its \b position \b0 to [1, 10, 1]\line\par
{\pntext\f0 23.\tab}\b Start \b0 Play Mode. \line\line\tab Rock will fall to the Glass object and will demolish it to radial fragments. Notice that the center of radial fragments at the \b collision \b0 point.\line\par
{\pntext\f0 24.\tab}\b Turn Off \b0 Play mode.\line\par
{\pntext\f0 25.\tab}\b Select \b0 the Glass object and in \b the RayFire Shatter \b0 component in \b Radial \b0 properties set \b Center Axis \b0 to \b X Red\b0 .\line\par
{\pntext\f0 26.\tab}\b Start \b0 Play Mode.\line\line\tab Rock will fall to the Glass object and will demolish it to radial fragments. Notice that this time direction of radial fragments is \b different\b0 . This time the object was fragmented over its local \b X axis\b0 . It is very \b important \b0 to set the correct \b Center Axis\b0  property in the Shatter component so the object will be fragmented right.\line\par
{\pntext\f0 27.\tab}\b Turn Off \b0 Play mode.\line\par
{\pntext\f0 28.\tab}\b Select \b0 the Glass object and in the \b RayFire Shatter \b0 component set \b Type \b0 to \b Splinters\b0 , in \b Splinters \b0 properties set \b Axis \b0 to \b Z Blue \b0 and \b Strength \b0 to \b 0.9\b0\line\par
{\pntext\f0 29.\tab}\b Start \b0 Play Mode.\line\par

\pard\nowidctlpar\sl276\slmult1\tab\tab Rock will fall to the Glass object and will demolish it to splinters.\line\par
\tab\tab In this way using RayFire Shatter component you can demolish objects in \tab runtime to fragmentation types other than default Voronoi. \par

\pard\nowidctlpar\sl276\slmult1\fs24\lang1033\par
}
 