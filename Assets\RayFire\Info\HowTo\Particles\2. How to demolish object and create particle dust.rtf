{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to demolish object and create particle dust.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create a Cube, this will be the cube which will be demolished in runtime. \line\par
{\pntext\f0 2.\tab}Set its name to "Brick", position to [0,7,0], rotation to [30, 0, 50] and scale to [2,1,1]\line\par
{\pntext\f0 3.\tab}Remove it's Box Collider because the Rigid component will add its own collider.\line\par
{\pntext\f0 4.\tab}Create another Cube, this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 5.\tab}Set its name to "Ground", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 6.\tab}Add RayFire Rigid component to Brick.\line\par
{\pntext\f0 7.\tab}Set Rigid Initialization to At Start and Demolition type to Awake Precache.\line\par
{\pntext\f0 8.\tab}Start Play Mode. Brick will fall down and will be demolished by collision.\line\line Now let's add some dust to this demolition simulation.\line\par
{\pntext\f0 9.\tab}Turn Off Play Mode.\line\par
{\pntext\f0 10.\tab}Select Brick and add RayFire Dust component. You can add several Dust components for different types of duif necessary.\line\par
{\pntext\f0 11.\tab}Enable On Demolition property because we want to create Dust particles for demolished fragments.\line\par
{\pntext\f0 12.\tab}Drag and drop dust_2_m.mat from \ldblquote Assets\\RayFire\\Tutorial\\Material\\Dust\rdblquote  folder Dust Material property. \line\line You can define several materials with different dust textures for variety in the Dust Materials list under Dust Material property. \line\par
{\pntext\f0 13.\tab}Start Play Mode. Brick will fall down and will be demolished by collision and a bunch of particle dust will be emitted.\line\par
{\pntext\f0 14.\tab}Turn Off Play Mode.\line\par
{\pntext\f0 15.\tab}Now let's make dust more noticeable.\line\par
{\pntext\f0 16.\tab}Select Brick and in the Dust component set Opacity to 0.4\line\par
{\pntext\f0 17.\tab}In the Emission group of properties set Size Min to 1 and Size Max to 3.\line\par
{\pntext\f0 18.\tab}Start Play Mode. \line\par

\pard\nowidctlpar\sl276\slmult1\tab\par
}
 