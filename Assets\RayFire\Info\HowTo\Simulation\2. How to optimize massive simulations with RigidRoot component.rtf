{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to optimize massive simulations with RigidRoot component.\ulnone\b0\fs24\lang1033\par

\pard\nowidctlpar\sl276\slmult1\fs22\lang9\par
\tab To efficiently manage the simulation of a large number of objects, use the RigidRoot component rather than the Rigid component with the Mesh Root object type.\par
\tab MeshRoot workflow adds a rigid component with the same properties to all its children, which is fine if you have a small number of objects (10-100), but every child will get a rigid component, and each rigid will run its own coroutines and perform unnecessary collision checks even if you do not intend to demolish objects to fragments.\par
\tab RigidRoot component do not add any RayFire components to children and run few coroutines for all child objects\lang1033 ,\lang9  which makes it much more efficient if you want to simulate a lot of objects.\par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create Cube, this will be a ground cube which will be used for collisions.\line\par
{\pntext\f0 2.\tab}Set its name to "Ground", position to [0,0,0] and scale to [10,1,10]\line\par
{\pntext\f0 3.\tab}Create another Cube, this will be the source of our fragments. Set its name to "Fragments", position to [-4,6,0] and scale to [1,11,1]\line\par
{\pntext\f0 4.\tab}Add RayFire Shatter component to "Fragments", set Amount value to 2000 and click on Fragment button.\line\par
{\pntext\f0 5.\tab}Destroy "Fragments" object, we do not need it anymore.\line\par
{\pntext\f0 6.\tab}Add RayFire RigidRoot component to "Fragments_root" object, set Initialization to At Start, Simulation Type to Inactive. We will collapse this structure with RigidRoot and then with Rigid/MeshRoot to compare which one works faster.\line\par
{\pntext\f0 7.\tab}In Acivation properties enable Connectivity checkbox and set Offset to 0.1\line\par
{\pntext\f0 8.\tab}Add RayFire Connectivity component to "Fragments_root" object, set Type to By Polygons. Connectivity component will handle connections among fragments and initiate Collapse. Because of the large number of fragments, it is best to turn off Connections and Nodes preview. Toggle Off Show Connections and Show Nodes buttons. Later you can enable them to see how Collapse destroys connections if you want.\line\par
{\pntext\f0 9.\tab}In Collapse properties set Type to Random, End to 85 and Duration to 5. It means that with every step Collapse will destroy random connections, after 5 seconds 85% of random connections will be destroyed and Collapse will stop. Seed for randomly destroyed connections can be defined in Filters properties. With the same Seed every time you will get the same Collapse.\line\par
{\pntext\f0 10.\tab}Add RayFire Unyielding component to "Fragments_root" object and set Center to [0,-5,0]. Unyielding component required to define fragments with which other fragments will attempt to connect via each other, and fragments that fail to establish connection will be activated and begin to fall down.\line\par
{\pntext\f0 11.\tab}Start Play Mode and in Connectivity component click on Start Collapse button.\line\line Notice how cracks start to appear over the whole structure and then some fragments start to detach from the structure and fall down. Also notice the average FPS during simulations, in my case it was around 90-100 FPS.\line\par
{\pntext\f0 12.\tab}Turn Off Play Mode. \line\line Now we will create identical setup but with Rigid component. \line\par
{\pntext\f0 13.\tab}Select "Fragments_root" object and duplicate it. Rename new "Fragments_root (1)" root with fragments to "Fragments_meshroot" and set its position to [4,6,0]. Select "Fragments_root" and in its RigidRoot properties set Initialization to by Method, so it won't participate in simulation. \fs24\lang1033\line\par
{\pntext\f0 14.\tab}\fs22\lang9 Select "Fragments_meshroot" object and Remove RayFire RigidRoot component. Instead Add RayFire Rigid component, set its Initialization to At Start, Object Type to Mesh Root and simulation to Inactive.\line\par
{\pntext\f0 15.\tab}In Activation properties set Offset to 0.1 and enable Connectivity checkbox. Now we have absolutely identical setup.\line\par
{\pntext\f0 16.\tab}Start Play Mode and in Connectivity component click on Start Collapse button.\line\tab\par

\pard\nowidctlpar\li710\sl276\slmult1\tab You should get absolutely identical collapse but this time it is much slower. Average amount of FPS in my case was around 30-40 FPS.\line\line\par
\tab\line\line\line\fs24\lang1033\par
}
 