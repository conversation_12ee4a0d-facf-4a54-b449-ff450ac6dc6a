{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to environment modeling in Editor mode.\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\par

\pard\nowidctlpar\sl276\slmult1 Combine component allows you to combine multiple meshes into one mesh. This can be useful for enviroment modeling if you want to spread a lot of objects over scene and then bake them into one static mesh. \par
\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , this will be a ground cube which will be used for collision.\line\par
{\pntext\f0 2.\tab}Set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0 ,0, 0] and \b scale \b0 to [15, 1, 10]\line\par
{\pntext\f0 3.\tab}Create \b\lang1033 Cube\b0\lang9  and set its \b position \b0 to [0, 1, 0]\line\par
{\pntext\f0 4.\tab}Create \b Material \b0 asset, set it sname to "\i Red\i0 ", set its Albedo color to \b Red \b0 and apply to \b Cube\b0 .\line\par
{\pntext\f0 5.\tab}Create \b Sphere \b0 and set its \b position \b0 to [0, 2, 0]\line\par
{\pntext\f0 6.\tab}Create \b Material \b0 asset, set it sname to "\i Green\i0 ", set its Albedo color to \b Green \b0 and apply to \b Sphere \b0 .\line\line You can create more different objects with different materials, but for this \f1\lang1049 t\f0\lang1033 utorial\lang9  it will be enough just these two objects.\line\par
{\pntext\f0 7.\tab}Create \b empty \b0 gameobject, set its name to "\i Combine\i0 " and \b position \b0 to  [0, 0.5, 0]\line\par
{\pntext\f0 8.\tab}Add \b RayFire Combine\b0  component to Combine object.\line\par
{\pntext\f0 9.\tab}\b Select \b0 Cube and Sphere and in Hierarchy set them as children for Combine object.\line\par
{\pntext\f0 10.\tab}Select Combine object and click on Combine button.\line\line MeshFilter with combined mesh and MeshRenderer components will be added to Combine object.\line\par
{\pntext\f0 11.\tab}Select Cube and Sphere and deactivate them to see combined mesh. \line\par

\pard\nowidctlpar\sl276\slmult1 If you are going to create prefab using combined mesh then do not forget to Export combined mesh into mesh asset first using Export Mesh button at the bottom of the Combine component, otherwise mesh will not be saved in prefab.\par
\par
In this tutorial you can see basic usage of Combine component in Editor. In next tutorial you can learn how to combine runtime demolished and simulated fragments using the same setup.\par
\par
\par
\par
\par
\par
}
 