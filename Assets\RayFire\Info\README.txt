RayFire for Unity

---------------------------------------

Website: 
http://rayfirestudios.com/

Discord:
https://discord.gg/8G98JKj

YouTube channel: 
https://www.youtube.com/user/MirVadim

YouTube Tutorial Playlist:
https://www.youtube.com/playlist?list=PLcjScvftXufKA-dLmqvokYnU7zFfKYWk0

Email:   
<EMAIL>

Telegram:
https://t.me/mir_vadim

Unity forum:
https://forum.unity.com/threads/rayfire-for-unity-runtime-demolition-and-fragmentation-plugin.674593/

Twitter:
https://twitter.com/RayFireStudios

---------------------------------------

Unity builds support: 2020, 2021, 2022, 6

---------------------------------------
          
Platforms with runtime fragmentation support: Windows, Mac OS X, iOS, Android, Linux
Already prefragmented objects can be simulated and demolished on any build platforms.

---------------------------------------

How to test runtime demolition feature:
Open scene in Assets/RayFire/Tutorial/Scenes/2. Rigid/3 Rigid. Mesh Demolition Types
Start Play Mode
Rock should fall down and demolish all concrete plates.

---------------------------------------

We will be very grateful if you will put RayFire logo at your game launch screen.

Thank You.