{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}{\f1\fswiss\fcharset204 Calibri;}{\f2\fmodern Consolas;}{\f3\fmodern\fcharset0 Consolas;}}
{\colortbl ;\red108\green149\blue235;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to record and playback simulation animation\par

\pard\nowidctlpar\sl276\slmult1\ulnone\b0\fs22\par
\lang1033 Recorder component allows to record objects simulation into an animation clip to playback it later. Keep in mind that you can record animation only in Editor. In order to record simulation all simulated objects should be children of object with recorder component.\par
\lang9\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1 Create \b Cube\b0 , set its \b name \b0 to "\i Ground\i0 ", \b position \b0 to [0,0,0] and \b scale \b0 to [20,1,20]\line\par
{\pntext\f0 2.\tab}Create another \b Cube\b0\lang1033 ,\f1\lang1049  \f0\lang1033 s\lang9 et its \b name \b0 to "\i Wall\i0 ", \b position \b0 to [-7,4,0] and \b scale \b0 to [1,7,3]\line\par
{\pntext\f0 3.\tab}Add \b Rayfire Shatter \b0 component to the Wall and click on \b Fragment \b0 button.\line\line Wall_root object with several fragments will be created. Now you can destroy or deactivate Wall object since we do not need it anymore.\line\par
{\pntext\f0 4.\tab}Add \b Rayfire Rigid \b0 component to the Wall_root object, set \b Initialization \b0 to \b At Start \b0 and \b Object Type \b0 to \b Mesh Root\b0 .\line\par
{\pntext\f0 5.\tab}\b Create \b0 new empty Gameobject, set its \b name \b0 to "\i Bomb\i0 " and \b position \b0 to [-10,1,0].\line\par
{\pntext\f0 6.\tab}Add \b Rayfire Bomb \b0 component to Bomb object, set its \b Range \b0 to \b 10\b0 , \b Strength \b0 to \b 1.3\b0 .\line\par
{\pntext\f0 7.\tab}Enable \b At Start \b0 toggle in Bomb component.\line\line Now you can start Play Mode to see simple explosion simulation which we are going to record.\line\par
{\pntext\f0 8.\tab}\b\lang1033 Create \b0 empty Gameobject and set its \b name \b0 to "\i Recorder\i0 "\lang9 .\line\par
{\pntext\f0 9.\tab}Add \b Rayfire Recorder \b0 component to Recorder object and set \b Clip Name \b0 to "\i WallExplosion\i0 "\line\par
{\pntext\f0 10.\tab}\b Select \b0 Wall_root object and in \b Hierarchy \b0 drag and drop it so it will be child of Recorder object. \line\par
{\pntext\f0 11.\tab}\b Start \b0 Play Mode. \line\line Wall_root fragments will be exploded and \i RayFireRecords \i0 folder will be created in Assets folder. In this folder "\i WallExplosion_animation\i0 " animation clip and "\i WallExplosion_controller\i0 " runtime animation controller assets will be created.\line\par
{\pntext\f0 12.\tab}\b Turn Off \b0 Play Mode. \line\line Now we need to playback recorded simulation.\line\par
{\pntext\f0 13.\tab}\b Destroy \b0 Bomb object, then \b select \b0 Wall_root object and \b destroy \b0 Rayfire Rigid component.\lang1033\line\par
{\pntext\f0 14.\tab}\b\lang9 Select \b0 recorder object and change \b Mode \b0 to \b Play\b0 . New properties will be revealed.\lang1033\line\par
{\pntext\f0 15.\tab}\b Drag and drop "\b0\i\lang9 WallExplosion_animation\i0 " asset to \b Clip \b0 property field.\lang1033\line\par
{\pntext\f0 16.\tab}\b Drag and drop \b0 "\i\lang9 WallExplosion_controller\i0 " asset to \b Controller \b0 property field.\lang1033\line\par
{\pntext\f0 17.\tab}\b\lang9 Start \b0 Play Mode. \line\par
{\pntext\f0 18.\tab}\b\lang1033 Select \b0 Recorder object and click on \b Play \b0 button. The same can be implemented in code using \cf1\f2 public \b void \cf0\b0 StartPlay()\f3  \f0 method.\lang9\line\line Recorded animation will be played.\par

\pard\nowidctlpar\sl276\slmult1\par
\par
\line\fs24\lang1033\par
}
 