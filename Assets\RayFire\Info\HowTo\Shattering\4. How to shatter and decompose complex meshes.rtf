{\rtf1\ansi\ansicpg1252\deff0\nouicompat{\fonttbl{\f0\fswiss\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\nowidctlpar\sl276\slmult1\qc\ul\b\f0\fs52\lang9 How to shatter and decompose complex meshes\par
\ulnone\b0\fs22\par

\pard 
{\pntext\f0 1.\tab}{\*\pn\pnlvlbody\pnf0\pnindent0\pnstart1\pndec{\pntxta.}}
\nowidctlpar\fi-360\li720\sl276\slmult1\b Create \b0 an empty gameobject, and set its \b name \b0 to \ldblquote\i Root\i0\rdblquote  and \b position \b0 at [0,0,0\lang1033 ]\lang9\line\par
{\pntext\f0 2.\tab}Create \b three cubes \b0 at \b position \b0 [0, 0, 0], [0, 1.3, 0] and [0, 2.6, 0]\line\par
{\pntext\f0 3.\tab}In the \b Hierarchy \b0 window move all cubes under Root and make them it\rquote s \b children\b0 .\line\par
{\pntext\f0 4.\tab}\b Select \b0 Root object and add \b RayFire Combine \b0 component.\line\par
{\pntext\f0 5.\tab}In Combine component click on \b Combine \b0 button.\line\line\tab After this Root object will get MeshFilter with complex mesh which consists of cubes meshes. By complex mesh I mean mesh which consists of multiple not connected with each other meshes.\line\par
{\pntext\f0 6.\tab}\b Destroy \b0 cubes.\line\par
{\pntext\f0 7.\tab}\b Select \b0 Root object and \b remove \b0 RayFire Combine component.\line\par
{\pntext\f0 8.\tab}Set its \b scale \b0 to [1,5,1]\line\line\tab Now we can start to shatter and decompose our complex mesh.\line\par
{\pntext\f0 9.\tab}Add \b RayFire Shatter \b0 to the Root object.\line\par
{\pntext\f0 10.\tab}Set the \b Fragment\lang1033 s\lang9  Type \b0 to \b Decompose \b0 and click on the \b Fragment \b0 button.\line\par
{\pntext\f0 11.\tab}Move \b Scale Preview \b0 slider right and left. \line\line\tab As you can see, the Decompose type allows to \b revert \b0 previous mesh combining. It detached all not connected meshes into separate objects.\line\par
{\pntext\f0 12.\tab}Click on the \b Delete Last \b0 button to destroy fragments.\line\line\tab Now let\rquote s shatter the Root object.\line\par
{\pntext\f0 13.\tab}Set \b Fragments Type \b0 to \b Voronoi\b0 , in \b Voronoi \b0 properties set \b Amount \b0 to 4.\line\par
{\pntext\f0 14.\tab}Open \b Properties\b0  disable \b Decompose \b0 property.\line\par
{\pntext\f0 15.\tab}Click on the \b Fragment \b0 button.\line\par
{\pntext\f0 16.\tab}Move \b Scale Preview \b0 slider right and left.\line\line\tab As you can see now we have 4 fragments, but every fragment consists of 2 or 3 not connected meshes. In order to avoid adding a RayFire Shatter component to every fragment you can reenable the Decompose property.\line\par
{\pntext\f0 17.\tab}Click on the \b Delete Last \b0 button\lang1033 .\lang9\line\par
{\pntext\f0 18.\tab}In \b Shatter \b0 component enable \b Decompose \b0 property back.\line\par
{\pntext\f0 19.\tab}Click on the \b Fragment \b0 button.\line\par
{\pntext\f0 20.\tab}Move \b Scale Preview \b0 slider right and left.\line\line\tab As you can see now there are more than 4 fragments because every fragment was decomposed and produced 2-3 fragments with separated meshes.\line\par

\pard\nowidctlpar\sl276\slmult1\tab\tab Keep in mind that complex mesh has a lot of interpenetrated meshes it is \tab recommended to \b disable Decompose \b0 property. Otherwise you may end up with a \tab lot of interpenetrated objects which \b explode \b0 when you try to simulate them.\par
}
 